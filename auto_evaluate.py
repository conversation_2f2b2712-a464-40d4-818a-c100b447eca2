#!/usr/bin/env python3
"""
自动化AI模型评估脚本
支持配置文件路径、模型列表，自动生成结果文件夹
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

class AutoEvaluator:
    """自动化评估器"""
    
    def __init__(self):
        """初始化评估器"""
        self.base_dir = Path.cwd()
        self.results_base_dir = self.base_dir / "evaluation_results"
        
    def run_evaluation(self, 
                      input_file: str,
                      template: str,
                      models: List[str],
                      output_name: Optional[str] = None) -> Dict:
        """
        运行评估
        
        Args:
            input_file: 输入文件路径
            template: 模板名称
            models: 模型列表
            output_name: 输出名称（可选）
            
        Returns:
            评估结果信息
        """
        # 验证输入文件
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 生成输出文件夹名称
        if output_name is None:
            file_stem = input_path.stem  # 不包含扩展名的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_name = f"{file_stem}_{timestamp}"
        
        # 创建结果目录
        output_dir = self.results_base_dir / output_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 构建模型参数
        models_str = ",".join(models)
        
        # 构建评估命令
        cmd = [
            "poetry", "run", "ai-eval", "evaluate",
            "-i", str(input_path),
            "-t", template,
            "-m", models_str,
            "-o", str(output_dir),
            "--name", output_name
        ]
        
        print(f"🚀 开始评估...")
        print(f"📁 输入文件: {input_file}")
        print(f"📋 模板: {template}")
        print(f"🤖 模型: {models}")
        print(f"📂 输出目录: {output_dir}")
        print(f"⚡ 执行命令: {' '.join(cmd)}")
        print("-" * 60)
        
        # 执行评估
        try:
            result = subprocess.run(cmd, 
                                  capture_output=True, 
                                  text=True, 
                                  cwd=self.base_dir)
            
            # 保存执行日志
            log_file = output_dir / "execution.log"
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"执行时间: {datetime.now().isoformat()}\n")
                f.write(f"命令: {' '.join(cmd)}\n")
                f.write(f"返回码: {result.returncode}\n")
                f.write(f"\n=== STDOUT ===\n{result.stdout}\n")
                f.write(f"\n=== STDERR ===\n{result.stderr}\n")
            
            if result.returncode == 0:
                print("✅ 评估完成!")
                
                # 查找生成的结果文件
                result_files = list(output_dir.glob("*_results.csv"))

                evaluation_info = {
                    "status": "success",
                    "input_file": str(input_path),
                    "template": template,
                    "models": models,
                    "output_dir": str(output_dir),
                    "result_files": [str(f) for f in result_files],
                    "execution_time": datetime.now().isoformat(),
                    "log_file": str(log_file)
                }

                # 保存评估信息
                info_file = output_dir / "evaluation_info.json"
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(evaluation_info, f, ensure_ascii=False, indent=2)

                print(f"📊 结果文件: {result_files}")
                print(f"📋 评估信息: {info_file}")

                # 自动生成分析报告
                if result_files:
                    self._generate_analysis_report(result_files[0], output_dir)

                return evaluation_info
                
            else:
                print(f"❌ 评估失败，返回码: {result.returncode}")
                print(f"错误信息: {result.stderr}")
                
                evaluation_info = {
                    "status": "failed",
                    "input_file": str(input_path),
                    "template": template,
                    "models": models,
                    "output_dir": str(output_dir),
                    "error": result.stderr,
                    "execution_time": datetime.now().isoformat(),
                    "log_file": str(log_file)
                }
                
                # 保存失败信息
                info_file = output_dir / "evaluation_info.json"
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(evaluation_info, f, ensure_ascii=False, indent=2)
                
                return evaluation_info
                
        except Exception as e:
            print(f"❌ 执行过程中发生错误: {e}")
            raise
    
    def batch_evaluate(self, evaluations: List[Dict]) -> List[Dict]:
        """
        批量评估
        
        Args:
            evaluations: 评估配置列表，每个包含 input_file, template, models, output_name
            
        Returns:
            评估结果列表
        """
        results = []
        
        for i, eval_config in enumerate(evaluations, 1):
            print(f"\n{'='*60}")
            print(f"📋 批量评估 {i}/{len(evaluations)}")
            print(f"{'='*60}")
            
            try:
                result = self.run_evaluation(**eval_config)
                results.append(result)
            except Exception as e:
                print(f"❌ 评估 {i} 失败: {e}")
                results.append({
                    "status": "error",
                    "error": str(e),
                    "config": eval_config
                })
        
        # 保存批量评估总结
        summary_file = self.results_base_dir / f"batch_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                "total_evaluations": len(evaluations),
                "successful": len([r for r in results if r.get("status") == "success"]),
                "failed": len([r for r in results if r.get("status") != "success"]),
                "results": results,
                "summary_time": datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 批量评估总结保存到: {summary_file}")
        return results

    def _generate_analysis_report(self, result_file: Path, output_dir: Path):
        """
        生成分析报告

        Args:
            result_file: 结果CSV文件路径
            output_dir: 输出目录
        """
        try:
            import pandas as pd

            print("📈 正在生成分析报告...")

            # 读取结果文件
            df = pd.read_csv(result_file)

            # 基本统计
            total_rows = len(df)
            model_columns = [col for col in df.columns if col.endswith('_result')]

            analysis = {
                "file_info": {
                    "result_file": str(result_file),
                    "total_rows": total_rows,
                    "columns": list(df.columns)
                },
                "model_performance": {}
            }

            # 分析每个模型的表现
            for model_col in model_columns:
                model_name = model_col.replace('_result', '')

                # 计算成功率
                valid_results = df[model_col].notna() & (df[model_col] != 'None') & (df[model_col] != '')
                success_count = valid_results.sum()
                success_rate = success_count / total_rows if total_rows > 0 else 0

                # 获取执行时间信息
                time_col = f"{model_name}_execution_time"
                avg_time = df[time_col].mean() if time_col in df.columns else 0

                # 结果分布
                if success_count > 0:
                    result_distribution = df[valid_results][model_col].value_counts().to_dict()
                else:
                    result_distribution = {}

                analysis["model_performance"][model_name] = {
                    "total_requests": total_rows,
                    "successful_requests": int(success_count),
                    "success_rate": float(success_rate),
                    "average_execution_time": float(avg_time),
                    "result_distribution": result_distribution
                }

            # 保存分析结果
            analysis_file = output_dir / "analysis_report.json"
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)

            # 生成简单的HTML报告
            html_content = self._generate_html_report(analysis)
            html_file = output_dir / "analysis_report.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"📊 分析报告已生成:")
            print(f"   - JSON: {analysis_file}")
            print(f"   - HTML: {html_file}")

        except Exception as e:
            print(f"⚠️ 生成分析报告时出错: {e}")

    def _generate_html_report(self, analysis: Dict) -> str:
        """生成HTML分析报告"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型评估报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .section {{ margin: 20px 0; }}
        .stats {{ display: flex; gap: 20px; flex-wrap: wrap; }}
        .stat-card {{ background-color: #e9f5ff; padding: 15px; border-radius: 5px; min-width: 200px; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #0066cc; }}
        table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .success {{ color: #28a745; }}
        .warning {{ color: #ffc107; }}
        .error {{ color: #dc3545; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>AI模型评估报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>数据文件: {analysis['file_info']['result_file']}</p>
    </div>

    <div class="section">
        <h2>评估概览</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{analysis['file_info']['total_rows']}</div>
                <div>总测试样本</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(analysis['model_performance'])}</div>
                <div>评估模型数</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>模型表现</h2>
        <table>
            <tr>
                <th>模型名称</th>
                <th>成功请求数</th>
                <th>成功率</th>
                <th>平均响应时间</th>
                <th>状态</th>
            </tr>
"""

        for model_name, perf in analysis['model_performance'].items():
            success_rate = perf['success_rate']
            status_class = "success" if success_rate > 0.8 else "warning" if success_rate > 0.5 else "error"
            status_text = "优秀" if success_rate > 0.8 else "良好" if success_rate > 0.5 else "需改进"

            html += f"""
            <tr>
                <td>{model_name}</td>
                <td>{perf['successful_requests']}/{perf['total_requests']}</td>
                <td>{success_rate:.1%}</td>
                <td>{perf['average_execution_time']:.2f}s</td>
                <td class="{status_class}">{status_text}</td>
            </tr>
"""

        html += """
        </table>
    </div>
</body>
</html>
"""
        return html


def main():
    """主函数 - 配置和运行评估"""
    
    # ==================== 配置区域 ====================
    
    # 1. 单个评估配置
    SINGLE_EVALUATION = {
        "input_file": "yitu.xlsx",  # 输入文件路径
        "template": "yitu",         # 模板名称
        "models": ["qwen-plus"],    # 模型列表
        "output_name": None         # 输出名称（None为自动生成）
    }
    
    # 2. 批量评估配置
    BATCH_EVALUATIONS = [
        {
            "input_file": "yitu.xlsx",
            "template": "yitu", 
            "models": ["qwen-plus"],
            "output_name": "yitu_qwen_only"
        },
        {
            "input_file": "yitu.xlsx",
            "template": "yitu",
            "models": ["qwen-plus", "ernie-4"],
            "output_name": "yitu_multi_models"
        },
        # 可以添加更多评估配置...
    ]
    
    # 3. 运行模式选择
    RUN_MODE = "single"  # "single" 或 "batch"
    
    # ==================== 执行区域 ====================
    
    evaluator = AutoEvaluator()
    
    try:
        if RUN_MODE == "single":
            print("🔄 运行单个评估...")
            result = evaluator.run_evaluation(**SINGLE_EVALUATION)
            print(f"\n✅ 评估完成! 状态: {result['status']}")
            
        elif RUN_MODE == "batch":
            print("🔄 运行批量评估...")
            results = evaluator.batch_evaluate(BATCH_EVALUATIONS)
            successful = len([r for r in results if r.get("status") == "success"])
            print(f"\n✅ 批量评估完成! 成功: {successful}/{len(results)}")
            
        else:
            print("❌ 无效的运行模式，请选择 'single' 或 'batch'")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断评估")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 评估过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
