"""
Report generator for evaluation results analysis.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..models.core import ResultRow, AnalysisResult, ComparisonReport, StatisticsReport
from ..utils.exceptions import FileProcessingError
from .analysis import AnalysisEngine


class ReportGenerator:
    """Generator for analysis reports in various formats."""
    
    def __init__(self):
        """Initialize report generator."""
        self.analysis_engine = AnalysisEngine()
    
    def generate_text_report(self, results: List[ResultRow], 
                           output_path: Optional[str] = None) -> str:
        """
        Generate a comprehensive text report.
        
        Args:
            results: List of result rows
            output_path: Optional path to save the report
            
        Returns:
            Report content as string
        """
        if not results:
            report = "No evaluation results to analyze.\n"
            if output_path:
                Path(output_path).write_text(report)
            return report
        
        # Generate analysis components
        analysis = self.analysis_engine.analyze_results(results)
        comparison = self.analysis_engine.compare_models(results)
        statistics = self.analysis_engine.generate_statistics(results)
        
        # Build report sections
        report_sections = []
        
        # Header
        report_sections.append(self._generate_header())
        
        # Executive Summary
        report_sections.append(self._generate_executive_summary(analysis, comparison))
        
        # Detailed Analysis
        report_sections.append(self._generate_detailed_analysis(analysis))
        
        # Model Comparison
        report_sections.append(self._generate_model_comparison(comparison))
        
        # Statistics
        report_sections.append(self._generate_statistics_section(statistics))
        
        # Problematic Cases
        problematic_rows = self.analysis_engine.identify_problematic_rows(results)
        if problematic_rows:
            report_sections.append(self._generate_problematic_cases(problematic_rows))
        
        # Model Agreement
        agreement = self.analysis_engine.calculate_model_agreement(results)
        if agreement:
            report_sections.append(self._generate_agreement_section(agreement))
        
        # Recommendations
        report_sections.append(self._generate_recommendations(analysis, comparison, problematic_rows))
        
        # Footer
        report_sections.append(self._generate_footer())
        
        # Combine all sections
        report = "\n\n".join(report_sections)
        
        # Save to file if path provided
        if output_path:
            Path(output_path).write_text(report, encoding='utf-8')
        
        return report
    
    def generate_json_report(self, results: List[ResultRow], 
                           output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a structured JSON report.
        
        Args:
            results: List of result rows
            output_path: Optional path to save the report
            
        Returns:
            Report data as dictionary
        """
        if not results:
            report_data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_results": 0
                },
                "message": "No evaluation results to analyze"
            }
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, indent=2, ensure_ascii=False)
            return report_data
        
        # Generate analysis components
        analysis = self.analysis_engine.analyze_results(results)
        comparison = self.analysis_engine.compare_models(results)
        statistics = self.analysis_engine.generate_statistics(results)
        problematic_rows = self.analysis_engine.identify_problematic_rows(results)
        agreement = self.analysis_engine.calculate_model_agreement(results)
        
        # Build structured report
        report_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "total_results": len(results),
                "analysis_version": "1.0"
            },
            "executive_summary": {
                "total_rows": analysis.total_rows,
                "model_count": analysis.model_count,
                "overall_success_rate": sum(analysis.success_rates.values()) / len(analysis.success_rates) if analysis.success_rates else 0.0,
                "best_performing_model": self._find_best_model_summary(analysis)
            },
            "model_analysis": {
                "success_rates": analysis.success_rates,
                "average_execution_times": analysis.average_execution_times,
                "error_counts": analysis.error_counts
            },
            "model_comparison": {
                "models": comparison.models,
                "metrics": comparison.metrics,
                "summary": comparison.summary
            },
            "detailed_statistics": {
                "total_evaluations": statistics.total_evaluations,
                "successful_evaluations": statistics.successful_evaluations,
                "failed_evaluations": statistics.failed_evaluations,
                "average_response_length": statistics.average_response_length,
                "response_time_stats": statistics.response_time_stats
            },
            "problematic_cases": {
                "count": len(problematic_rows),
                "cases": problematic_rows
            },
            "model_agreement": agreement,
            "recommendations": self._generate_recommendations_data(analysis, comparison, problematic_rows)
        }
        
        # Save to file if path provided
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        return report_data
    
    def generate_csv_summary(self, results: List[ResultRow], 
                           output_path: Optional[str] = None) -> str:
        """
        Generate a CSV summary of model performance.
        
        Args:
            results: List of result rows
            output_path: Optional path to save the CSV
            
        Returns:
            CSV content as string
        """
        if not results:
            csv_content = "model_id,success_rate,avg_execution_time,total_requests,successful_requests,failed_requests\n"
            if output_path:
                Path(output_path).write_text(csv_content)
            return csv_content
        
        analysis = self.analysis_engine.analyze_results(results)
        comparison = self.analysis_engine.compare_models(results)
        
        # Build CSV content
        csv_lines = ["model_id,success_rate,avg_execution_time,total_requests,successful_requests,failed_requests,avg_response_length"]
        
        for model_id in sorted(analysis.success_rates.keys()):
            metrics = comparison.metrics.get(model_id, {})
            line = f"{model_id},{analysis.success_rates[model_id]:.4f},{analysis.average_execution_times[model_id]:.4f},{int(metrics.get('total_requests', 0))},{int(metrics.get('successful_requests', 0))},{int(metrics.get('failed_requests', 0))},{metrics.get('average_response_length', 0.0):.2f}"
            csv_lines.append(line)
        
        csv_content = "\n".join(csv_lines) + "\n"
        
        # Save to file if path provided
        if output_path:
            Path(output_path).write_text(csv_content, encoding='utf-8')
        
        return csv_content
    
    def generate_html_report(self, results: List[ResultRow], 
                           output_path: Optional[str] = None) -> str:
        """
        Generate an HTML report with basic styling.
        
        Args:
            results: List of result rows
            output_path: Optional path to save the HTML
            
        Returns:
            HTML content as string
        """
        if not results:
            html_content = self._generate_empty_html_report()
            if output_path:
                Path(output_path).write_text(html_content, encoding='utf-8')
            return html_content
        
        # Generate analysis components
        analysis = self.analysis_engine.analyze_results(results)
        comparison = self.analysis_engine.compare_models(results)
        statistics = self.analysis_engine.generate_statistics(results)
        problematic_rows = self.analysis_engine.identify_problematic_rows(results)
        
        # Build HTML sections
        html_sections = []
        
        # HTML header and CSS
        html_sections.append(self._generate_html_header())
        
        # Executive summary
        html_sections.append(self._generate_html_executive_summary(analysis, comparison))
        
        # Model performance table
        html_sections.append(self._generate_html_model_table(comparison))
        
        # Statistics charts (basic tables for now)
        html_sections.append(self._generate_html_statistics(statistics))
        
        # Problematic cases
        if problematic_rows:
            html_sections.append(self._generate_html_problematic_cases(problematic_rows))
        
        # HTML footer
        html_sections.append(self._generate_html_footer())
        
        html_content = "\n".join(html_sections)
        
        # Save to file if path provided
        if output_path:
            Path(output_path).write_text(html_content, encoding='utf-8')
        
        return html_content
    
    def _generate_header(self) -> str:
        """Generate report header."""
        return f"""AI Model Evaluation Report
{'=' * 50}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
    
    def _generate_executive_summary(self, analysis: AnalysisResult, comparison: ComparisonReport) -> str:
        """Generate executive summary section."""
        if not analysis.success_rates:
            return "EXECUTIVE SUMMARY\n" + "-" * 20 + "\nNo models evaluated."
        
        best_model = max(analysis.success_rates.items(), key=lambda x: x[1])
        overall_success_rate = sum(analysis.success_rates.values()) / len(analysis.success_rates)
        
        return f"""EXECUTIVE SUMMARY
{'-' * 20}
• Total evaluation rows: {analysis.total_rows}
• Models evaluated: {analysis.model_count}
• Overall success rate: {overall_success_rate:.1%}
• Best performing model: {best_model[0]} ({best_model[1]:.1%} success rate)
• Total API requests: {sum(comparison.metrics[m]['total_requests'] for m in comparison.models)}"""
    
    def _generate_detailed_analysis(self, analysis: AnalysisResult) -> str:
        """Generate detailed analysis section."""
        if not analysis.success_rates:
            return "DETAILED ANALYSIS\n" + "-" * 20 + "\nNo analysis data available."
        
        lines = ["DETAILED ANALYSIS", "-" * 20]
        
        for model_id in sorted(analysis.success_rates.keys()):
            lines.append(f"\n{model_id}:")
            lines.append(f"  Success Rate: {analysis.success_rates[model_id]:.1%}")
            lines.append(f"  Avg Execution Time: {analysis.average_execution_times[model_id]:.2f}s")
            lines.append(f"  Error Count: {analysis.error_counts[model_id]}")
        
        return "\n".join(lines)
    
    def _generate_model_comparison(self, comparison: ComparisonReport) -> str:
        """Generate model comparison section."""
        if not comparison.models:
            return "MODEL COMPARISON\n" + "-" * 20 + "\nNo models to compare."
        
        lines = ["MODEL COMPARISON", "-" * 20]
        lines.append(f"Summary: {comparison.summary}")
        lines.append("")
        
        # Create comparison table
        lines.append(f"{'Model':<15} {'Success Rate':<12} {'Avg Time':<10} {'Requests':<10}")
        lines.append("-" * 50)
        
        for model_id in comparison.models:
            metrics = comparison.metrics[model_id]
            lines.append(f"{model_id:<15} {metrics['success_rate']:<11.1%} {metrics['average_execution_time']:<9.2f}s {int(metrics['total_requests']):<10}")
        
        return "\n".join(lines)
    
    def _generate_statistics_section(self, statistics: StatisticsReport) -> str:
        """Generate statistics section."""
        lines = ["DETAILED STATISTICS", "-" * 20]
        
        lines.append(f"Total Evaluations: {statistics.total_evaluations}")
        lines.append(f"Successful: {statistics.successful_evaluations}")
        lines.append(f"Failed: {statistics.failed_evaluations}")
        
        if statistics.response_time_stats:
            lines.append("\nResponse Time Statistics:")
            for model_id, stats in statistics.response_time_stats.items():
                lines.append(f"  {model_id}:")
                lines.append(f"    Mean: {stats['mean']:.2f}s")
                lines.append(f"    Median: {stats['median']:.2f}s")
                lines.append(f"    Min: {stats['min']:.2f}s")
                lines.append(f"    Max: {stats['max']:.2f}s")
                lines.append(f"    Std Dev: {stats['std_dev']:.2f}s")
        
        return "\n".join(lines)
    
    def _generate_problematic_cases(self, problematic_rows: List[Dict[str, Any]]) -> str:
        """Generate problematic cases section."""
        lines = ["PROBLEMATIC CASES", "-" * 20]
        lines.append(f"Found {len(problematic_rows)} rows with high failure rates:")
        lines.append("")
        
        for case in problematic_rows[:10]:  # Show top 10
            lines.append(f"Row {case['row_index']}:")
            lines.append(f"  Prompt: {case['original_prompt'][:100]}...")
            lines.append(f"  Failure Rate: {case['failure_rate']:.1%} ({case['failed_models']}/{case['total_models']})")
            lines.append(f"  Errors: {', '.join(case['errors'].keys())}")
            lines.append("")
        
        if len(problematic_rows) > 10:
            lines.append(f"... and {len(problematic_rows) - 10} more cases")
        
        return "\n".join(lines)
    
    def _generate_agreement_section(self, agreement: Dict[str, float]) -> str:
        """Generate model agreement section."""
        lines = ["MODEL AGREEMENT", "-" * 20]
        
        for pair, score in agreement.items():
            lines.append(f"{pair}: {score:.1%} agreement")
        
        return "\n".join(lines)
    
    def _generate_recommendations(self, analysis: AnalysisResult, 
                                comparison: ComparisonReport, 
                                problematic_rows: List[Dict[str, Any]]) -> str:
        """Generate recommendations section."""
        lines = ["RECOMMENDATIONS", "-" * 20]
        
        if not analysis.success_rates:
            lines.append("• No data available for recommendations")
            return "\n".join(lines)
        
        # Find best and worst models
        best_model = max(analysis.success_rates.items(), key=lambda x: x[1])
        worst_model = min(analysis.success_rates.items(), key=lambda x: x[1])
        
        lines.append(f"• Consider using {best_model[0]} for production (highest success rate: {best_model[1]:.1%})")
        
        if worst_model[1] < 0.8:
            lines.append(f"• Investigate issues with {worst_model[0]} (success rate: {worst_model[1]:.1%})")
        
        if problematic_rows:
            lines.append(f"• Review {len(problematic_rows)} problematic input cases for data quality issues")
        
        # Performance recommendations
        fastest_model = min(analysis.average_execution_times.items(), key=lambda x: x[1])
        slowest_model = max(analysis.average_execution_times.items(), key=lambda x: x[1])
        
        if slowest_model[1] > fastest_model[1] * 2:
            lines.append(f"• {slowest_model[0]} is significantly slower ({slowest_model[1]:.2f}s vs {fastest_model[1]:.2f}s)")
        
        return "\n".join(lines)
    
    def _generate_footer(self) -> str:
        """Generate report footer."""
        return f"""
{'=' * 50}
Report generated by AI Model Evaluation System
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
    
    def _find_best_model_summary(self, analysis: AnalysisResult) -> Dict[str, Any]:
        """Find best model for summary."""
        if not analysis.success_rates:
            return {"model_id": None, "success_rate": 0.0}
        
        best_model = max(analysis.success_rates.items(), key=lambda x: x[1])
        return {
            "model_id": best_model[0],
            "success_rate": best_model[1]
        }
    
    def _generate_recommendations_data(self, analysis: AnalysisResult, 
                                     comparison: ComparisonReport, 
                                     problematic_rows: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations as list of strings."""
        recommendations = []
        
        if not analysis.success_rates:
            recommendations.append("No data available for recommendations")
            return recommendations
        
        # Find best and worst models
        best_model = max(analysis.success_rates.items(), key=lambda x: x[1])
        worst_model = min(analysis.success_rates.items(), key=lambda x: x[1])
        
        recommendations.append(f"Consider using {best_model[0]} for production (highest success rate: {best_model[1]:.1%})")
        
        if worst_model[1] < 0.8:
            recommendations.append(f"Investigate issues with {worst_model[0]} (success rate: {worst_model[1]:.1%})")
        
        if problematic_rows:
            recommendations.append(f"Review {len(problematic_rows)} problematic input cases for data quality issues")
        
        return recommendations
    
    def _generate_html_header(self) -> str:
        """Generate HTML header with CSS."""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Model Evaluation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1, h2 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; }
        .summary { background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .metric { display: inline-block; margin: 10px 20px 10px 0; }
        .metric-value { font-size: 1.5em; font-weight: bold; color: #2c5aa0; }
        .metric-label { font-size: 0.9em; color: #666; }
        .problematic { background-color: #fff3cd; padding: 10px; border-radius: 3px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>AI Model Evaluation Report</h1>
    <p><strong>Generated:</strong> """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "</p>"
    
    def _generate_html_executive_summary(self, analysis: AnalysisResult, comparison: ComparisonReport) -> str:
        """Generate HTML executive summary."""
        if not analysis.success_rates:
            return '<div class="summary"><h2>Executive Summary</h2><p>No models evaluated.</p></div>'
        
        best_model = max(analysis.success_rates.items(), key=lambda x: x[1])
        overall_success_rate = sum(analysis.success_rates.values()) / len(analysis.success_rates)
        
        return f'''<div class="summary">
        <h2>Executive Summary</h2>
        <div class="metric">
            <div class="metric-value">{analysis.total_rows}</div>
            <div class="metric-label">Evaluation Rows</div>
        </div>
        <div class="metric">
            <div class="metric-value">{analysis.model_count}</div>
            <div class="metric-label">Models Evaluated</div>
        </div>
        <div class="metric">
            <div class="metric-value">{overall_success_rate:.1%}</div>
            <div class="metric-label">Overall Success Rate</div>
        </div>
        <div class="metric">
            <div class="metric-value">{best_model[0]}</div>
            <div class="metric-label">Best Model ({best_model[1]:.1%})</div>
        </div>
    </div>'''
    
    def _generate_html_model_table(self, comparison: ComparisonReport) -> str:
        """Generate HTML model comparison table."""
        if not comparison.models:
            return "<h2>Model Comparison</h2><p>No models to compare.</p>"
        
        html = "<h2>Model Comparison</h2>\n<table>\n"
        html += "<tr><th>Model</th><th>Success Rate</th><th>Avg Time (s)</th><th>Total Requests</th><th>Successful</th><th>Failed</th></tr>\n"
        
        for model_id in comparison.models:
            metrics = comparison.metrics[model_id]
            html += f"<tr><td>{model_id}</td><td>{metrics['success_rate']:.1%}</td><td>{metrics['average_execution_time']:.2f}</td><td>{int(metrics['total_requests'])}</td><td>{int(metrics['successful_requests'])}</td><td>{int(metrics['failed_requests'])}</td></tr>\n"
        
        html += "</table>\n"
        return html
    
    def _generate_html_statistics(self, statistics: StatisticsReport) -> str:
        """Generate HTML statistics section."""
        html = "<h2>Detailed Statistics</h2>\n"
        html += f"<p><strong>Total Evaluations:</strong> {statistics.total_evaluations}</p>\n"
        html += f"<p><strong>Successful:</strong> {statistics.successful_evaluations}</p>\n"
        html += f"<p><strong>Failed:</strong> {statistics.failed_evaluations}</p>\n"
        
        if statistics.response_time_stats:
            html += "<h3>Response Time Statistics</h3>\n<table>\n"
            html += "<tr><th>Model</th><th>Mean (s)</th><th>Median (s)</th><th>Min (s)</th><th>Max (s)</th><th>Std Dev (s)</th></tr>\n"
            
            for model_id, stats in statistics.response_time_stats.items():
                html += f"<tr><td>{model_id}</td><td>{stats['mean']:.2f}</td><td>{stats['median']:.2f}</td><td>{stats['min']:.2f}</td><td>{stats['max']:.2f}</td><td>{stats['std_dev']:.2f}</td></tr>\n"
            
            html += "</table>\n"
        
        return html
    
    def _generate_html_problematic_cases(self, problematic_rows: List[Dict[str, Any]]) -> str:
        """Generate HTML problematic cases section."""
        html = f"<h2>Problematic Cases ({len(problematic_rows)} found)</h2>\n"
        
        for case in problematic_rows[:5]:  # Show top 5 in HTML
            html += f'''<div class="problematic">
                <strong>Row {case['row_index']}:</strong> {case['original_prompt'][:150]}...<br>
                <strong>Failure Rate:</strong> {case['failure_rate']:.1%} ({case['failed_models']}/{case['total_models']} models failed)<br>
                <strong>Errors:</strong> {', '.join(case['errors'].keys())}
            </div>\n'''
        
        if len(problematic_rows) > 5:
            html += f"<p><em>... and {len(problematic_rows) - 5} more cases</em></p>\n"
        
        return html
    
    def _generate_html_footer(self) -> str:
        """Generate HTML footer."""
        return f"""
    <hr>
    <p><small>Report generated by AI Model Evaluation System on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
</body>
</html>"""
    
    def _generate_empty_html_report(self) -> str:
        """Generate HTML report for empty results."""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Model Evaluation Report</title>
</head>
<body>
    <h1>AI Model Evaluation Report</h1>
    <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    <p>No evaluation results to analyze.</p>
</body>
</html>"""