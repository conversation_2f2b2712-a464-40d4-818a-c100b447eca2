"""
Tests for template processing engine.
"""

import pytest

from ai_model_evaluation.services.template_engine import TemplateEngine
from ai_model_evaluation.models.core import PromptTemplate, EvaluationRow
from ai_model_evaluation.utils.exceptions import TemplateError


class TestTemplateEngine:
    """Test TemplateEngine class."""
    
    def create_test_template(self):
        """Create a test template."""
        return PromptTemplate(
            id="test_template",
            name="Test Template",
            template="Hello {name}, your task is to {task}. Please provide {output_type}.",
            variables=["name", "task", "output_type"],
            description="Test template for unit tests"
        )
    
    def test_render_template_success(self):
        """Test successful template rendering."""
        engine = TemplateEngine()
        template = self.create_test_template()
        
        variables = {
            "name": "Alice",
            "task": "analyze data",
            "output_type": "a summary"
        }
        
        result = engine.render_template(template, variables)
        expected = "Hello Alice, your task is to analyze data. Please provide a summary."
        
        assert result == expected
    
    def test_render_template_missing_variables(self):
        """Test template rendering with missing variables."""
        engine = TemplateEngine()
        template = self.create_test_template()
        
        variables = {
            "name": "Alice",
            "task": "analyze data"
            # Missing "output_type"
        }
        
        with pytest.raises(TemplateError, match="Missing required variables"):
            engine.render_template(template, variables)
    
    def test_render_template_for_evaluation(self):
        """Test rendering template with evaluation row."""
        engine = TemplateEngine()
        
        # Create template that uses evaluation row fields
        template = PromptTemplate(
            id="eval_template",
            name="Evaluation Template",
            template="{original_prompt}\n\nOption A: {variable_a}\nOption B: {variable_b}",
            variables=["original_prompt", "variable_a", "variable_b"],
            description="Template for evaluation"
        )
        
        evaluation_row = EvaluationRow(
            original_prompt="Compare these options",
            variable_a="Cloud hosting",
            variable_b="On-premise hosting",
            expected_result="Cloud is better"
        )
        
        result = engine.render_template_for_evaluation(template, evaluation_row)
        expected = "Compare these options\n\nOption A: Cloud hosting\nOption B: On-premise hosting"
        
        assert result == expected
    
    def test_validate_template_valid(self):
        """Test validation of valid template."""
        engine = TemplateEngine()
        template = self.create_test_template()
        
        errors = engine.validate_template(template)
        assert len(errors) == 0
    
    def test_validate_template_empty(self):
        """Test validation of empty template."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="empty_template",
            name="Empty Template",
            template="",
            variables=[],
            description="Empty template"
        )
        
        errors = engine.validate_template(template)
        assert len(errors) > 0
        assert any("cannot be empty" in error for error in errors)
    
    def test_validate_template_missing_declarations(self):
        """Test validation with missing variable declarations."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="missing_vars",
            name="Missing Variables",
            template="Hello {name}, your {age} is showing.",
            variables=["name"],  # Missing "age"
            description="Template with missing variable declarations"
        )
        
        errors = engine.validate_template(template)
        assert len(errors) > 0
        assert any("not declared" in error for error in errors)
    
    def test_validate_template_unused_declarations(self):
        """Test validation with unused variable declarations."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="unused_vars",
            name="Unused Variables",
            template="Hello {name}",
            variables=["name", "unused_var"],  # "unused_var" is not used
            description="Template with unused variable declarations"
        )
        
        errors = engine.validate_template(template)
        assert len(errors) > 0
        assert any("not used" in error for error in errors)
    
    def test_validate_template_invalid_variable_names(self):
        """Test validation with invalid variable names."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="invalid_vars",
            name="Invalid Variables",
            template="Hello {123invalid} and {with-dash}",
            variables=["123invalid", "with-dash"],
            description="Template with invalid variable names"
        )
        
        errors = engine.validate_template(template)
        assert len(errors) > 0
        assert any("Invalid variable name" in error for error in errors)
    
    def test_validate_template_malformed_placeholders(self):
        """Test validation with malformed placeholders."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="malformed",
            name="Malformed Template",
            template="Hello {name and {incomplete",
            variables=["name"],
            description="Template with malformed placeholders"
        )
        
        errors = engine.validate_template(template)
        assert len(errors) > 0
        assert any("Malformed" in error or "Unmatched" in error for error in errors)
    
    def test_preview_template_with_sample_data(self):
        """Test template preview with sample data."""
        engine = TemplateEngine()
        template = self.create_test_template()
        
        sample_variables = {
            "name": "John",
            "task": "write code",
            "output_type": "documentation"
        }
        
        preview = engine.preview_template(template, sample_variables)
        expected = "Hello John, your task is to write code. Please provide documentation."
        
        assert preview == expected
    
    def test_preview_template_without_sample_data(self):
        """Test template preview without sample data."""
        engine = TemplateEngine()
        template = self.create_test_template()
        
        preview = engine.preview_template(template)
        expected = "Hello [NAME], your task is to [TASK]. Please provide [OUTPUT_TYPE]."
        
        assert preview == expected
    
    def test_get_template_statistics(self):
        """Test getting template statistics."""
        engine = TemplateEngine()
        template = self.create_test_template()
        
        stats = engine.get_template_statistics(template)
        
        assert stats['template_id'] == "test_template"
        assert stats['template_name'] == "Test Template"
        assert stats['character_count'] > 0
        assert stats['word_count'] > 0
        assert stats['line_count'] == 1
        assert stats['variable_count'] == 3
        assert set(stats['variables_used']) == {"name", "task", "output_type"}
        assert set(stats['variables_declared']) == {"name", "task", "output_type"}
        assert stats['is_valid'] is True
    
    def test_create_template_from_text(self):
        """Test creating template from text."""
        engine = TemplateEngine()
        
        template_text = "Process {input} and generate {output} with {format} formatting."
        
        template = engine.create_template_from_text(
            template_id="auto_template",
            name="Auto Template",
            template_text=template_text,
            description="Automatically created template"
        )
        
        assert template.id == "auto_template"
        assert template.name == "Auto Template"
        assert template.template == template_text
        assert set(template.variables) == {"input", "output", "format"}
        assert template.description == "Automatically created template"
    
    def test_create_template_from_invalid_text(self):
        """Test creating template from invalid text."""
        engine = TemplateEngine()
        
        invalid_template_text = "Process {input and generate {output"  # Malformed
        
        with pytest.raises(TemplateError, match="Invalid template"):
            engine.create_template_from_text(
                template_id="invalid_template",
                name="Invalid Template",
                template_text=invalid_template_text
            )
    
    def test_suggest_improvements_short_template(self):
        """Test improvement suggestions for short template."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="short",
            name="Short",
            template="Hi {name}",
            variables=["name"],
            description="Short template"
        )
        
        suggestions = engine.suggest_improvements(template)
        assert len(suggestions) > 0
        assert any("very short" in suggestion for suggestion in suggestions)
    
    def test_suggest_improvements_no_variables(self):
        """Test improvement suggestions for template without variables."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="no_vars",
            name="No Variables",
            template="This is a static template with no variables.",
            variables=[],
            description="Template without variables"
        )
        
        suggestions = engine.suggest_improvements(template)
        assert len(suggestions) > 0
        assert any("no variables" in suggestion for suggestion in suggestions)
    
    def test_suggest_improvements_common_variable_names(self):
        """Test improvement suggestions for common variable names."""
        engine = TemplateEngine()
        template = PromptTemplate(
            id="common_vars",
            name="Common Variables",
            template="Process this {input} and generate {output}.",
            variables=["input", "output"],
            description="Template with common variable names"
        )
        
        suggestions = engine.suggest_improvements(template)
        assert len(suggestions) > 0
        assert any("more descriptive" in suggestion for suggestion in suggestions)
    
    def test_extract_variables_from_template(self):
        """Test extracting variables from template text."""
        engine = TemplateEngine()
        
        template_text = "Hello {name}, you are {age} years old and live in {city}."
        variables = engine._extract_variables_from_template(template_text)
        
        assert variables == {"name", "age", "city"}
    
    def test_extract_variables_no_variables(self):
        """Test extracting variables from template with no variables."""
        engine = TemplateEngine()
        
        template_text = "This is a static template with no variables."
        variables = engine._extract_variables_from_template(template_text)
        
        assert variables == set()
    
    def test_is_valid_variable_name(self):
        """Test variable name validation."""
        engine = TemplateEngine()
        
        # Valid names
        assert engine._is_valid_variable_name("name") is True
        assert engine._is_valid_variable_name("user_name") is True
        assert engine._is_valid_variable_name("_private") is True
        assert engine._is_valid_variable_name("var123") is True
        
        # Invalid names
        assert engine._is_valid_variable_name("123invalid") is False
        assert engine._is_valid_variable_name("with-dash") is False
        assert engine._is_valid_variable_name("with space") is False
        assert engine._is_valid_variable_name("") is False