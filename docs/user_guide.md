# AI 模型评估系统 - 用户指南

这份全面的指南将帮助您开始使用AI模型评估系统，并充分利用其功能。

## 目录

1. [入门指南](#入门指南)
2. [配置](#配置)
3. [数据准备](#数据准备)
4. [运行评估](#运行评估)
5. [分析和报告](#分析和报告)
6. [任务管理](#任务管理)
7. [历史管理](#历史管理)
8. [高级功能](#高级功能)
9. [最佳实践](#最佳实践)
10. [故障排除](#故障排除)

## 入门指南

### 安装

1. **克隆仓库：**
   ```bash
   git clone <repository-url>
   cd ai-model-evaluation
   ```

2. **安装依赖项：**
   ```bash
   poetry install
   ```

   安装完成后，您需要激活 Poetry 环境，以便 `ai-eval` 命令能够被系统识别：

   ```bash
   poetry shell
   ```

   或者，您可以在每次运行 `ai-eval` 命令时使用 `poetry run` 前缀。

3. **验证安装：**
   ```bash
   poetry run ai-eval --help
   ```

### 首次使用步骤

1. **系统已预配置多个AI模型提供商：**
   - ✅ 通义千问 (qwen-plus)
   - ✅ 百度AI (ERNIE-4.0, DeepSeek V3)
   - ✅ 火山AI (doubao-1.6-seed)
   - ✅ 混元AI (hunyuan-pro)
   - 🔧 智谱AI (glm-4) - 需要配置API密钥
   - 🔧 OpenAI - 需要配置API密钥

2. **可选：设置额外的环境变量：**
   ```bash
   # 复制环境变量示例文件
   cp .env.example .env
   
   # 编辑 .env 文件，添加可选的API密钥
   export ZHIPU_API_KEY="your-zhipu-api-key"
   export OPENAI_API_KEY="your-openai-api-key"
   ```

3. **验证配置：**
   ```bash
   # 运行配置验证脚本
   poetry run python scripts/validate_config.py
   
   # 或使用命令行工具
   poetry run ai-eval config validate
   ```

4. **快速测试：**
   ```bash
   # 使用预配置的模型进行测试
   poetry run ai-eval run --models qwen-plus --template default --input examples/sample_data.csv
   ```



## Configuration

### Basic Configuration Structure

The system uses YAML configuration files with three main sections:

```yaml
providers:    # AI service providers
models:       # Model configurations
prompt_templates:  # Prompt templates
```

### Provider Configuration

```yaml
providers:
  - id: openai                    # Unique identifier
    name: OpenAI                  # Display name
    base_url: https://api.openai.com/v1/
    api_key: ${OPENAI_API_KEY}   # Environment variable
    is_active: true              # Enable/disable
```

**Key Points:**
- Use environment variables for API keys
- `base_url` should end with `/v1/` for OpenAI-compatible APIs
- Set `is_active: false` to temporarily disable a provider

### Model Configuration

```yaml
models:
  - id: gpt4                      # Unique identifier
    provider_id: openai           # Reference to provider
    model_name: gpt-4             # API model name
    display_name: GPT-4           # Human-readable name
    temperature: 0.7              # Sampling temperature (0.0-2.0)
    thinking_enabled: false       # Enable thinking mode
    max_tokens: 4096             # Maximum response tokens
```

**Parameters Explained:**
- `temperature`: Controls randomness (0.0 = deterministic, 2.0 = very random)
- `thinking_enabled`: Some models support "thinking" before responding
- `max_tokens`: Limits response length (affects cost and performance)

### Template Configuration

```yaml
prompt_templates:
  - id: qa_template
    name: Question Answering
    template: |
      Question: {original_prompt}
      Context: {variable_a}
      Please answer based on the context.
    variables: [original_prompt, variable_a]
    description: Basic Q&A template
```

**Template Variables:**
- `{original_prompt}`: Main question/prompt
- `{variable_a}`, `{variable_b}`: Additional context variables
- `{expected_result}`: Expected answer (optional)

### Configuration Management Commands

```bash
# View current configuration
poetry run ai-eval config show

# Validate configuration
ai-eval config validate

# Add provider
poetry run ai-eval config add-provider --name "Custom AI" --base-url "https://api.custom.ai/v1/" --api-key "key"

# Add model
poetry run ai-eval config add-model --id custom_model --provider custom_ai --model-name "model-v1" --display-name "Custom Model"
```

## Data Preparation

### Required CSV Format

Your evaluation data must be in CSV format with these columns:

| Column | Required | Description |
|--------|----------|-------------|
| `original_prompt` | Yes | Main question or prompt |
| `variable_a` | No | First context variable |
| `variable_b` | No | Second context variable |
| `expected_result` | No | Expected answer for comparison |

### Example Data Files

**Basic Q&A Data:**
```csv
original_prompt,variable_a,variable_b,expected_result
What is the capital of France?,France,European country,Paris
What is 2 + 2?,Mathematics,Basic arithmetic,4
```

**Translation Data:**
```csv
original_prompt,variable_a,variable_b,expected_result
Hello,English,Spanish,Hola
Good morning,English,French,Bonjour
```

**Comparison Data:**
```csv
original_prompt,variable_a,variable_b,expected_result
Which is better for web development?,React,Vue.js,Both have strengths
```

### Data Validation

```bash
# Check file format
poetry run ai-eval file-info data.csv

# Validate data structure
ai-eval validate-data data.csv

# Check template compatibility
ai-eval validate-data data.csv --template qa_template

# Preview template rendering
ai-eval preview-template --template qa_template --input data.csv --rows 3
```

### Data Preparation Tips

1. **Use consistent formatting:**
   - Keep column names exactly as specified
   - Avoid special characters in data
   - Use UTF-8 encoding for international text

2. **Handle missing data:**
   - Empty cells are allowed for optional columns
   - Use consistent placeholder values if needed

3. **Optimize for evaluation:**
   - Start with small datasets (10-50 rows) for testing
   - Group similar types of questions together
   - Include diverse examples to test model capabilities

## Running Evaluations

### Basic Evaluation

```bash
poetry run ai-eval evaluate \
  --input data.csv \
  --template qa_template \
  --models gpt4,claude3 \
  --output results/
```

### Advanced Evaluation Options

```bash
poetry run ai-eval evaluate \
  --input data.csv \
  --template qa_template \
  --models gpt4,claude3,gpt35 \
  --output results/ \
  --name "Q&A Comparison" \
  --concurrent 10 \
  --timeout 30
```

**Parameters:**
- `--concurrent`: Number of simultaneous API requests (default: 5)
- `--timeout`: Request timeout in seconds (default: 30)
- `--name`: Custom name for the evaluation task

### Monitoring Evaluation Progress

The system provides real-time progress updates:

```
✓ Loaded 50 evaluation rows
✓ Configuration validated
⚡ Starting evaluation: Q&A Comparison
📊 Evaluating 3 models on 50 rows...
████████████████████████████████████████ 100% 150/150 requests completed
✓ Evaluation completed successfully!
📁 Results saved to: results/qa_comparison_results.csv
```

### Handling Errors During Evaluation

Common issues and solutions:

1. **API Rate Limits:**
   - Reduce `--concurrent` value
   - Add delays between requests
   - Check provider rate limits

2. **Network Timeouts:**
   - Increase `--timeout` value
   - Check internet connection
   - Verify provider endpoints

3. **Authentication Errors:**
   - Verify API keys are correct
   - Check environment variables
   - Ensure provider is active

## Analysis and Reporting

### Quick Analysis

```bash
# Analyze results with default settings
ai-eval report analyze results/evaluation_results.csv
```

This shows:
- Overall success rates
- Model performance comparison
- Execution time statistics
- Problematic cases (high failure rate)

### Detailed Analysis

```bash
# Analyze specific models
ai-eval report analyze results.csv --models gpt4,claude3

# Custom failure threshold
ai-eval report analyze results.csv --threshold 0.3

# Filter to specific models
ai-eval report analyze results.csv --models gpt4,claude3
```

### Report Generation

Generate reports in different formats:

```bash
# Text report (human-readable)
ai-eval report generate results.csv --format text --output report.txt

# JSON report (machine-readable)
poetry run ai-eval report generate results.csv --format json --output report.json

# CSV summary (spreadsheet-friendly)
ai-eval report generate results.csv --format csv --output summary.csv

# HTML report (web-friendly)
poetry run ai-eval report generate results.csv --format html --output report.html
```

### Model Comparison

```bash
# Compare by success rate
ai-eval report compare results.csv --models gpt4,claude3 --metric success_rate

# Compare by response time
ai-eval report compare results.csv --models gpt4,claude3 --metric response_time

# Compare by response length
ai-eval report compare results.csv --models gpt4,claude3 --metric response_length
```

### Understanding Report Metrics

**Success Rate:** Percentage of successful API calls
**Average Execution Time:** Mean response time in seconds
**Error Count:** Number of failed requests
**Response Length:** Average character count of responses
**Agreement Rate:** How often models give similar responses

## Task Management

### Viewing Tasks

```bash
# List all tasks
ai-eval task list

# Filter by status
ai-eval task list --status completed
ai-eval task list --status running
ai-eval task list --status failed

# Limit results
ai-eval task list --limit 10
```

### Task Details

```bash
# Show task information
ai-eval task show <task-id>

# Show with results preview
ai-eval task show <task-id> --show-results

# Show with execution logs
ai-eval task show <task-id> --show-logs
```

### Task Control

```bash
# Cancel running task
ai-eval task cancel <task-id>

# Delete completed task
ai-eval task delete <task-id>

# View task statistics
ai-eval task stats
```

## History Management

### Viewing History

```bash
# List recent tasks
ai-eval history list

# List with pagination
ai-eval history list --limit 20 --offset 0

# Filter by status
ai-eval history list --status completed
```

### Searching History

```bash
# Search by name
poetry run ai-eval history search "Q&A evaluation"

# Search by template
ai-eval history search "qa_template"

# Search by model
ai-eval history search "gpt4"
```

### Detailed History View

```bash
# Show task details
ai-eval history show <task-id>

# Include evaluation results
poetry run ai-eval history show <task-id> --show-results

# Include execution logs
poetry run ai-eval history show <task-id> --show-logs
```

### Data Export

```bash
# Export task data
ai-eval history export <task-id> --output task_data.json

# Export includes:
# - Task configuration
# - Evaluation results
# - Execution logs
# - Metadata
```

### History Maintenance

```bash
# View history statistics
ai-eval history stats

# Clean up old tasks (older than 30 days)
ai-eval history cleanup --days 30

# Dry run (preview what would be deleted)
ai-eval history cleanup --days 30 --dry-run
```

## Advanced Features

### Custom Templates

Create templates for specific use cases:

```yaml
prompt_templates:
  - id: code_review
    name: Code Review
    template: |
      Please review the following code:
      
      Language: {variable_a}
      Code: {original_prompt}
      Focus Area: {variable_b}
      
      Provide feedback on:
      1. Code quality
      2. Best practices
      3. Potential improvements
    variables: [original_prompt, variable_a, variable_b]
    description: Template for code review tasks
```

### Batch Processing

For large datasets:

```bash
# Process large files efficiently
ai-eval evaluate \
  --input large_dataset.csv \
  --template qa_template \
  --models gpt4 \
  --output results/ \
  --concurrent 20 \
  --batch-size 100
```

### Performance Optimization

1. **Concurrent Requests:**
   - Start with 5-10 concurrent requests
   - Increase gradually while monitoring rate limits
   - Monitor system resources (CPU, memory, network)

2. **Batch Size:**
   - Process data in smaller batches for better memory usage
   - Save intermediate results to prevent data loss

3. **Caching:**
   - The system automatically caches successful requests
   - Rerun evaluations will skip already processed items

### Integration with Other Tools

Export results for use in other tools:

```bash
# Export to pandas-compatible format
ai-eval report generate results.csv --format json --output data.json

# Export summary statistics
ai-eval report generate results.csv --format csv --output summary.csv

# Generate visualization-ready data
ai-eval report analyze results.csv --export-metrics metrics.json
```

## Best Practices

### Configuration Best Practices

1. **Security:**
   - Never commit API keys to version control
   - Use environment variables for sensitive data
   - Rotate API keys regularly

2. **Organization:**
   - Use descriptive names for models and templates
   - Group related configurations together
   - Document custom templates clearly

3. **Testing:**
   - Validate configuration before production use
   - Test with small datasets first
   - Monitor API usage and costs

### Data Best Practices

1. **Quality:**
   - Clean and validate data before evaluation
   - Use consistent formatting across datasets
   - Include diverse examples for comprehensive testing

2. **Structure:**
   - Follow the required CSV format exactly
   - Use meaningful column names
   - Include expected results when possible

3. **Size:**
   - Start with small datasets (10-50 rows)
   - Scale up gradually
   - Consider API rate limits and costs

### Evaluation Best Practices

1. **Planning:**
   - Define clear evaluation objectives
   - Choose appropriate models for comparison
   - Select relevant metrics for your use case

2. **Execution:**
   - Monitor evaluations in real-time
   - Save intermediate results
   - Handle errors gracefully

3. **Analysis:**
   - Generate multiple report formats
   - Compare models on relevant metrics
   - Document findings and decisions

### Performance Best Practices

1. **Resource Management:**
   - Monitor system resources during evaluation
   - Use appropriate concurrency levels
   - Clean up old tasks and results regularly

2. **Cost Optimization:**
   - Estimate API costs before large evaluations
   - Use cheaper models for initial testing
   - Monitor usage across different providers

3. **Reliability:**
   - Implement retry logic for failed requests
   - Save progress frequently
   - Have backup plans for critical evaluations

## Troubleshooting

### Common Issues

#### Configuration Problems

**Issue:** "Provider not found"
```bash
# Solution: Check provider ID in configuration
ai-eval config show
ai-eval config validate
```

**Issue:** "Invalid API key"
```bash
# Solution: Verify environment variables
echo $OPENAI_API_KEY
ai-eval test-connectivity --provider openai
```

#### Data Problems

**Issue:** "Column not found"
```bash
# Solution: Validate data format
ai-eval file-info data.csv
ai-eval validate-data data.csv
```

**Issue:** "Template variable missing"
```bash
# Solution: Check template compatibility
ai-eval validate-data data.csv --template your_template
ai-eval preview-template --template your_template --input data.csv
```

#### Evaluation Problems

**Issue:** "Rate limit exceeded"
```bash
# Solution: Reduce concurrency
ai-eval evaluate --input data.csv --template template --models model --concurrent 3
```

**Issue:** "Request timeout"
```bash
# Solution: Increase timeout
ai-eval evaluate --input data.csv --template template --models model --timeout 60
```

#### Performance Problems

**Issue:** "High memory usage"
- Process smaller batches
- Clean up old results
- Monitor system resources

**Issue:** "Slow evaluation"
- Increase concurrency (within rate limits)
- Use faster models for testing
- Optimize network connection

### Getting Help

1. **Check logs:**
   ```bash
   ai-eval history show <task-id> --show-logs
   ```

2. **Validate configuration:**
   ```bash
   ai-eval config validate
   ai-eval test-connectivity
   ```

3. **Test with minimal data:**
   - Create a small test dataset (2-3 rows)
   - Use a single model
   - Check for basic functionality

4. **Review documentation:**
   - Check this user guide
   - Review configuration examples
   - Look at sample data files

### Error Messages Reference

| Error Message | Cause | Solution |
|---------------|-------|----------|
| "Template variable not found" | Missing column in data | Add required columns to CSV |
| "Provider not responding" | Network/API issue | Check connectivity and API keys |
| "File format not supported" | Wrong file type | Use CSV or Excel format |
| "Model not found" | Invalid model ID | Check model configuration |
| "Rate limit exceeded" | Too many requests | Reduce concurrency |
| "Authentication failed" | Invalid API key | Verify API key and permissions |

### Performance Tuning

1. **For small datasets (< 100 rows):**
   - Concurrency: 5-10
   - Timeout: 30 seconds
   - Batch size: Process all at once

2. **For medium datasets (100-1000 rows):**
   - Concurrency: 10-20
   - Timeout: 45 seconds
   - Batch size: 100-200 rows

3. **For large datasets (> 1000 rows):**
   - Concurrency: 15-25
   - Timeout: 60 seconds
   - Batch size: 200-500 rows
   - Consider splitting into multiple evaluations

Remember to always monitor your API usage and costs, especially with large datasets and multiple models.