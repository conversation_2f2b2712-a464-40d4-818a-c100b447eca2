"""
History manager for evaluation tasks and results.
"""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..models.core import EvaluationTask, TaskStatus
from ..utils.exceptions import HistoryError


class HistoryManager:
    """Manager for task history and persistence."""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize history manager.
        
        Args:
            db_path: Path to SQLite database file (defaults to ~/.ai_eval_history.db)
        """
        if db_path is None:
            home_dir = Path.home()
            db_path = home_dir / ".ai_eval_history.db"
        
        self.db_path = Path(db_path)
        self._init_database()
    
    def _init_database(self):
        """Initialize the SQLite database with required tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create tasks table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS tasks (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        prompt_template_id TEXT NOT NULL,
                        selected_models TEXT NOT NULL,  -- JSON array
                        input_file_path TEXT NOT NULL,
                        output_directory TEXT NOT NULL,
                        status TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        started_at TEXT,
                        completed_at TEXT,
                        error_message TEXT,
                        metadata TEXT  -- JSON object for additional data
                    )
                """)
                
                # Create task_results table for storing evaluation results
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS task_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id TEXT NOT NULL,
                        row_index INTEGER NOT NULL,
                        original_prompt TEXT,
                        variable_a TEXT,
                        variable_b TEXT,
                        expected_result TEXT,
                        model_results TEXT NOT NULL,  -- JSON object
                        execution_time TEXT NOT NULL,  -- JSON object
                        error_info TEXT,  -- JSON object
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (task_id) REFERENCES tasks (id)
                    )
                """)
                
                # Create task_logs table for storing execution logs
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS task_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id TEXT NOT NULL,
                        level TEXT NOT NULL,  -- INFO, WARNING, ERROR
                        message TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        details TEXT,  -- JSON object for additional details
                        FOREIGN KEY (task_id) REFERENCES tasks (id)
                    )
                """)
                
                # Create indexes for better performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks (created_at)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_results_task_id ON task_results (task_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_logs_task_id ON task_logs (task_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_logs_level ON task_logs (level)")
                
                conn.commit()
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to initialize database: {e}")
    
    def save_task(self, task: EvaluationTask) -> None:
        """
        Save or update a task in the history.
        
        Args:
            task: Task to save
            
        Raises:
            HistoryError: If task cannot be saved
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Convert task to database format
                task_data = {
                    'id': task.id,
                    'name': task.name,
                    'prompt_template_id': task.prompt_template_id,
                    'selected_models': json.dumps(task.selected_models),
                    'input_file_path': task.input_file_path,
                    'output_directory': task.output_directory,
                    'status': task.status.value,
                    'created_at': task.created_at.isoformat(),
                    'started_at': task.started_at.isoformat() if task.started_at else None,
                    'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                    'error_message': task.error_message,
                    'metadata': json.dumps(task.metadata) if task.metadata else None
                }
                
                # Use INSERT OR REPLACE to handle both new and updated tasks
                cursor.execute("""
                    INSERT OR REPLACE INTO tasks 
                    (id, name, prompt_template_id, selected_models, input_file_path, 
                     output_directory, status, created_at, started_at, completed_at, 
                     error_message, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_data['id'], task_data['name'], task_data['prompt_template_id'],
                    task_data['selected_models'], task_data['input_file_path'],
                    task_data['output_directory'], task_data['status'],
                    task_data['created_at'], task_data['started_at'],
                    task_data['completed_at'], task_data['error_message'],
                    task_data['metadata']
                ))
                
                conn.commit()
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to save task {task.id}: {e}")
    
    def get_task(self, task_id: str) -> Optional[EvaluationTask]:
        """
        Retrieve a task by ID.
        
        Args:
            task_id: Task ID to retrieve
            
        Returns:
            Task if found, None otherwise
            
        Raises:
            HistoryError: If database query fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
                row = cursor.fetchone()
                
                if row is None:
                    return None
                
                return self._row_to_task(row)
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to retrieve task {task_id}: {e}")
    
    def list_tasks(self, status: Optional[TaskStatus] = None, 
                  limit: Optional[int] = None,
                  offset: int = 0) -> List[EvaluationTask]:
        """
        List tasks with optional filtering.
        
        Args:
            status: Optional status filter
            limit: Maximum number of tasks to return
            offset: Number of tasks to skip
            
        Returns:
            List of tasks
            
        Raises:
            HistoryError: If database query fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = "SELECT * FROM tasks"
                params = []
                
                if status is not None:
                    query += " WHERE status = ?"
                    params.append(status.value)
                
                query += " ORDER BY created_at DESC"
                
                if limit is not None:
                    query += " LIMIT ?"
                    params.append(limit)
                
                if offset > 0:
                    query += " OFFSET ?"
                    params.append(offset)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [self._row_to_task(row) for row in rows]
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to list tasks: {e}")
    
    def delete_task(self, task_id: str) -> bool:
        """
        Delete a task and all associated data.
        
        Args:
            task_id: Task ID to delete
            
        Returns:
            True if task was deleted, False if not found
            
        Raises:
            HistoryError: If deletion fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete in order: logs, results, then task
                cursor.execute("DELETE FROM task_logs WHERE task_id = ?", (task_id,))
                cursor.execute("DELETE FROM task_results WHERE task_id = ?", (task_id,))
                cursor.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
                
                deleted = cursor.rowcount > 0
                conn.commit()
                
                return deleted
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to delete task {task_id}: {e}")
    
    def get_task_statistics(self) -> Dict[str, int]:
        """
        Get statistics about tasks in the history.
        
        Returns:
            Dictionary with task counts by status
            
        Raises:
            HistoryError: If query fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT status, COUNT(*) as count 
                    FROM tasks 
                    GROUP BY status
                """)
                
                stats = {}
                total = 0
                
                for row in cursor.fetchall():
                    status, count = row
                    stats[status] = count
                    total += count
                
                stats['total'] = total
                
                # Ensure all statuses are represented
                for status in TaskStatus:
                    if status.value not in stats:
                        stats[status.value] = 0
                
                return stats
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to get task statistics: {e}")
    
    def save_task_results(self, task_id: str, results: List[Any]) -> None:
        """
        Save evaluation results for a task.
        
        Args:
            task_id: Task ID
            results: List of result rows
            
        Raises:
            HistoryError: If results cannot be saved
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Clear existing results for this task
                cursor.execute("DELETE FROM task_results WHERE task_id = ?", (task_id,))
                
                # Insert new results
                for i, result in enumerate(results):
                    cursor.execute("""
                        INSERT INTO task_results 
                        (task_id, row_index, original_prompt, variable_a, variable_b, 
                         expected_result, model_results, execution_time, error_info, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        task_id, i,
                        result.original_prompt,
                        result.variable_a,
                        result.variable_b,
                        result.expected_result,
                        json.dumps(result.model_results),
                        json.dumps(result.execution_time),
                        json.dumps(result.error_info) if result.error_info else None,
                        datetime.now().isoformat()
                    ))
                
                conn.commit()
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to save results for task {task_id}: {e}")
    
    def get_task_results(self, task_id: str) -> List[Dict[str, Any]]:
        """
        Get evaluation results for a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            List of result dictionaries
            
        Raises:
            HistoryError: If query fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT row_index, original_prompt, variable_a, variable_b, 
                           expected_result, model_results, execution_time, error_info
                    FROM task_results 
                    WHERE task_id = ? 
                    ORDER BY row_index
                """, (task_id,))
                
                results = []
                for row in cursor.fetchall():
                    result = {
                        'row_index': row[0],
                        'original_prompt': row[1],
                        'variable_a': row[2],
                        'variable_b': row[3],
                        'expected_result': row[4],
                        'model_results': json.loads(row[5]) if row[5] else {},
                        'execution_time': json.loads(row[6]) if row[6] else {},
                        'error_info': json.loads(row[7]) if row[7] else None
                    }
                    results.append(result)
                
                return results
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to get results for task {task_id}: {e}")
    
    def log_task_event(self, task_id: str, level: str, message: str, 
                      details: Optional[Dict[str, Any]] = None) -> None:
        """
        Log an event for a task.
        
        Args:
            task_id: Task ID
            level: Log level (INFO, WARNING, ERROR)
            message: Log message
            details: Optional additional details
            
        Raises:
            HistoryError: If log cannot be saved
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO task_logs (task_id, level, message, timestamp, details)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    task_id, level, message, datetime.now().isoformat(),
                    json.dumps(details) if details else None
                ))
                
                conn.commit()
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to log event for task {task_id}: {e}")
    
    def get_task_logs(self, task_id: str, level: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get logs for a task.
        
        Args:
            task_id: Task ID
            level: Optional level filter
            
        Returns:
            List of log entries
            
        Raises:
            HistoryError: If query fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = "SELECT level, message, timestamp, details FROM task_logs WHERE task_id = ?"
                params = [task_id]
                
                if level:
                    query += " AND level = ?"
                    params.append(level)
                
                query += " ORDER BY timestamp"
                
                cursor.execute(query, params)
                
                logs = []
                for row in cursor.fetchall():
                    log_entry = {
                        'level': row[0],
                        'message': row[1],
                        'timestamp': row[2],
                        'details': json.loads(row[3]) if row[3] else None
                    }
                    logs.append(log_entry)
                
                return logs
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to get logs for task {task_id}: {e}")
    
    def search_tasks(self, query: str, limit: int = 50) -> List[EvaluationTask]:
        """
        Search tasks by name or other criteria.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching tasks
            
        Raises:
            HistoryError: If search fails
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                search_pattern = f"%{query}%"
                cursor.execute("""
                    SELECT * FROM tasks 
                    WHERE name LIKE ? OR prompt_template_id LIKE ? OR input_file_path LIKE ?
                    ORDER BY created_at DESC
                    LIMIT ?
                """, (search_pattern, search_pattern, search_pattern, limit))
                
                rows = cursor.fetchall()
                return [self._row_to_task(row) for row in rows]
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to search tasks: {e}")
    
    def cleanup_old_tasks(self, days: int = 30) -> int:
        """
        Clean up tasks older than specified days.
        
        Args:
            days: Number of days to keep
            
        Returns:
            Number of tasks deleted
            
        Raises:
            HistoryError: If cleanup fails
        """
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days)
            cutoff_str = cutoff_date.isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get task IDs to delete
                cursor.execute("""
                    SELECT id FROM tasks 
                    WHERE created_at < ? AND status IN ('completed', 'failed', 'cancelled')
                """, (cutoff_str,))
                
                task_ids = [row[0] for row in cursor.fetchall()]
                
                # Delete associated data
                for task_id in task_ids:
                    cursor.execute("DELETE FROM task_logs WHERE task_id = ?", (task_id,))
                    cursor.execute("DELETE FROM task_results WHERE task_id = ?", (task_id,))
                
                # Delete tasks
                cursor.execute("""
                    DELETE FROM tasks 
                    WHERE created_at < ? AND status IN ('completed', 'failed', 'cancelled')
                """, (cutoff_str,))
                
                deleted_count = len(task_ids)
                conn.commit()
                
                return deleted_count
                
        except sqlite3.Error as e:
            raise HistoryError(f"Failed to cleanup old tasks: {e}")
    
    def _row_to_task(self, row) -> EvaluationTask:
        """Convert database row to EvaluationTask object."""
        return EvaluationTask(
            id=row[0],
            name=row[1],
            prompt_template_id=row[2],
            selected_models=json.loads(row[3]),
            input_file_path=row[4],
            output_directory=row[5],
            status=TaskStatus(row[6]),
            created_at=datetime.fromisoformat(row[7]),
            started_at=datetime.fromisoformat(row[8]) if row[8] else None,
            completed_at=datetime.fromisoformat(row[9]) if row[9] else None,
            error_message=row[10],
            metadata=json.loads(row[11]) if row[11] else None
        )
    
    def export_task_data(self, task_id: str, output_path: str) -> None:
        """
        Export all data for a task to a JSON file.
        
        Args:
            task_id: Task ID to export
            output_path: Path to save the export file
            
        Raises:
            HistoryError: If export fails
        """
        try:
            task = self.get_task(task_id)
            if not task:
                raise HistoryError(f"Task {task_id} not found")
            
            results = self.get_task_results(task_id)
            logs = self.get_task_logs(task_id)
            
            export_data = {
                'task': {
                    'id': task.id,
                    'name': task.name,
                    'prompt_template_id': task.prompt_template_id,
                    'selected_models': task.selected_models,
                    'input_file_path': task.input_file_path,
                    'output_directory': task.output_directory,
                    'status': task.status.value,
                    'created_at': task.created_at.isoformat(),
                    'started_at': task.started_at.isoformat() if task.started_at else None,
                    'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                    'error_message': task.error_message,
                    'metadata': task.metadata
                },
                'results': results,
                'logs': logs,
                'exported_at': datetime.now().isoformat()
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            raise HistoryError(f"Failed to export task data: {e}")