#!/usr/bin/env python3
"""
配置验证脚本
用于验证AI模型评估系统的配置是否正确
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from ai_model_evaluation.config.manager import ConfigManager
from ai_model_evaluation.services.api_client import AIAPIClient
from ai_model_evaluation.utils.exceptions import ConfigurationError


def validate_config_file():
    """验证配置文件格式和内容"""
    print("🔍 验证配置文件...")
    
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print(f"✅ 配置文件加载成功")
        print(f"   - 提供商数量: {len(config.providers)}")
        print(f"   - 模型数量: {len(config.models)}")
        print(f"   - 模板数量: {len(config.prompt_templates)}")
        
        # 验证模型配置
        for model in config.models:
            provider = next((p for p in config.providers if p.id == model.provider_id), None)
            if not provider:
                print(f"❌ 模型 {model.id} 引用了不存在的提供商 {model.provider_id}")
                return False
            else:
                print(f"   ✅ 模型 {model.display_name} -> 提供商 {provider.name}")
        
        return True
        
    except ConfigurationError as e:
        print(f"❌ 配置文件错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False


async def test_model_connectivity():
    """测试模型连接性"""
    print("\n🌐 测试模型连接性...")
    
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 测试每个模型
        for model in config.models:
            provider = next(p for p in config.providers if p.id == model.provider_id)
            
            print(f"\n📡 测试 {model.display_name}...")
            
            try:
                async with AIAPIClient(provider) as api_client:
                    # 发送简单的测试请求
                    test_prompt = "你好，请回复'测试成功'"
                    response = await api_client.send_request(
                        model=model,
                        prompt=test_prompt
                    )
                    
                    if response.success and response.content.strip():
                        print(f"   ✅ {model.display_name}: 连接成功")
                        print(f"   📝 响应: {response.content[:50]}...")
                    else:
                        print(f"   ⚠️  {model.display_name}: 连接失败")
                        if response.error_message:
                            print(f"      错误: {response.error_message[:100]}...")
                    
            except Exception as e:
                print(f"   ❌ {model.display_name}: 连接失败 - {str(e)[:100]}...")
                
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")


def print_configuration_summary():
    """打印配置摘要"""
    print("\n📊 配置摘要")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print("\n🏢 已配置的提供商:")
        for provider in config.providers:
            status = "🔑 有密钥" if provider.api_key and not provider.api_key.startswith("${") else "🔧 需要配置"
            print(f"   - {provider.name} ({provider.id}): {status}")
        
        print("\n🤖 已配置的模型:")
        for model in config.models:
            provider = next(p for p in config.providers if p.id == model.provider_id)
            print(f"   - {model.display_name} ({model.model_name}) -> {provider.name}")
        
        print("\n📝 可用的模板:")
        for template in config.prompt_templates:
            print(f"   - {template.name} ({template.id}): {template.description}")
            
    except Exception as e:
        print(f"❌ 无法加载配置: {e}")


async def main():
    """主函数"""
    print("🚀 AI模型评估系统 - 配置验证")
    print("=" * 50)
    
    # 验证配置文件
    if not validate_config_file():
        print("\n❌ 配置验证失败，请检查配置文件")
        sys.exit(1)
    
    # 打印配置摘要
    print_configuration_summary()
    
    # 询问是否进行连接测试
    print("\n❓ 是否进行模型连接测试？(y/N): ", end="")
    response = input().strip().lower()
    
    if response in ['y', 'yes', '是']:
        await test_model_connectivity()
    else:
        print("\n⏭️  跳过连接测试")
    
    print("\n✅ 验证完成！")
    print("\n💡 提示:")
    print("   - 如需添加更多模型，请编辑 config.yaml")
    print("   - API密钥可通过环境变量或直接在配置文件中设置")
    print("   - 使用 'ai-eval --help' 查看更多命令")


if __name__ == "__main__":
    asyncio.run(main())