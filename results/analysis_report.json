{"evaluation_summary": {"total_samples": 345, "timestamp": "2025-08-04T15:42:20.816523", "models_evaluated": ["qwen-plus", "ernie-4"]}, "model_performance": {"qwen-plus": {"successful_requests": 200, "success_rate": 0.5797101449275363, "accuracy": 0.635, "status": "operational"}, "ernie-4": {"successful_requests": 0, "success_rate": 0.0, "status": "api_authentication_failed"}}, "intent_distribution": {"问价格": 39, "看车型": 30, "问优惠": 27, "问地址": 25, "问配置": 23, "要买车": 12, "问政策": 9, "撩妹": 8, "问置换": 7, "问试乘试驾": 6, "金融政策": 4, "问颜色": 4, "问车型": 3, "问库存车": 2, "问联系方式": 1}, "recommendations": ["Fix ernie-4 API configuration", "Improve qwen-plus success rate", "Add more labeled test data", "Optimize performance settings"]}