{"file_info": {"result_file": "/Users/<USER>/kiro/对比/ai-model-evaluation/evaluation_results/yitu_20250804_155937/yitu_20250804_155937_results.csv", "total_rows": 345, "columns": ["history", "live_comment", "expected_result", "qwen-plus_result", "qwen-plus_execution_time"]}, "model_performance": {"expected": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 107, "看车型": 51, "问价格": 45, "问配置": 35, "问政策": 20, "问地址": 17, "问优惠": 16, "问试乘试驾": 13, "要买车": 11, "撩妹": 10, "问置换": 8, "想卖车": 4, "问联系方式": 3, "金融政策": 2, "问颜色": 2, "问库存车": 1}, "classification_metrics": {"accuracy": 1.0, "macro_avg": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0}, "weighted_avg": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0}, "per_class_metrics": {"想卖车": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 4, "tp": 4, "fp": 0, "fn": 0}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 10, "tp": 10, "fp": 0, "fn": 0}, "看车型": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 51, "tp": 51, "fp": 0, "fn": 0}, "要买车": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 11, "tp": 11, "fp": 0, "fn": 0}, "金融政策": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0}, "问价格": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 45, "tp": 45, "fp": 0, "fn": 0}, "问优惠": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 16, "tp": 16, "fp": 0, "fn": 0}, "问地址": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 17, "tp": 17, "fp": 0, "fn": 0}, "问库存车": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问政策": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 20, "tp": 20, "fp": 0, "fn": 0}, "问置换": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 8, "tp": 8, "fp": 0, "fn": 0}, "问联系方式": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 3, "tp": 3, "fp": 0, "fn": 0}, "问试乘试驾": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 13, "tp": 13, "fp": 0, "fn": 0}, "问配置": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 35, "tp": 35, "fp": 0, "fn": 0}, "问颜色": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 2, "tp": 2, "fp": 0, "fn": 0}}, "confusion_matrix": {"想卖车": {"想卖车": 4, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"想卖车": 0, "撩妹": 10, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"想卖车": 0, "撩妹": 0, "看车型": 51, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "要买车": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 11, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "金融政策": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 2, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 45, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 16, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 17, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问库存车": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 20, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 8, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问联系方式": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 3, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 13, "问配置": 0, "问颜色": 0}, "问配置": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 35, "问颜色": 0}, "问颜色": {"想卖车": 0, "撩妹": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 238, "valid_predictions": 238, "note": "Calculated on 238 valid samples out of 345 total"}}, "qwen-plus": {"total_requests": 345, "successful_requests": 197, "success_rate": 0.5710144927536231, "average_execution_time": 0.4948750945105069, "result_distribution": {"问价格": 40, "看车型": 32, "问优惠": 25, "问配置": 25, "问地址": 22, "要买车": 10, "问政策": 8, "问置换": 7, "问试乘试驾": 7, "撩妹": 7, "金融政策": 5, "问颜色": 4, "问库存车": 2, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 1, "问车型": 1, "问联系方式": 1}, "classification_metrics": {"accuracy": 0.751412429378531, "macro_avg": {"precision": 0.6308669191360755, "recall": 0.7494297030882396, "f1_score": 0.7365436711063434}, "weighted_avg": {"precision": 0.7856551065267022, "recall": 0.751412429378531, "f1_score": 0.7559129310662187}, "per_class_metrics": {"想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 5, "tp": 5, "fp": 0, "fn": 0}, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 0, "tp": 0, "fp": 1, "fn": 0}, "看车型": {"precision": 0.8333333333333334, "recall": 0.7142857142857143, "f1_score": 0.7692307692307692, "support": 35, "tp": 25, "fp": 5, "fn": 10}, "要买车": {"precision": 0.5714285714285714, "recall": 0.6666666666666666, "f1_score": 0.6153846153846153, "support": 6, "tp": 4, "fp": 3, "fn": 2}, "金融政策": {"precision": 0.2, "recall": 0.5, "f1_score": 0.28571428571428575, "support": 2, "tp": 1, "fp": 4, "fn": 1}, "问价格": {"precision": 0.8157894736842105, "recall": 0.7560975609756098, "f1_score": 0.7848101265822786, "support": 41, "tp": 31, "fp": 7, "fn": 10}, "问优惠": {"precision": 0.5416666666666666, "recall": 0.9285714285714286, "f1_score": 0.6842105263157894, "support": 14, "tp": 13, "fp": 11, "fn": 1}, "问地址": {"precision": 0.9411764705882353, "recall": 1.0, "f1_score": 0.9696969696969697, "support": 16, "tp": 16, "fp": 1, "fn": 0}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.8571428571428571, "recall": 0.46153846153846156, "f1_score": 0.6, "support": 13, "tp": 6, "fp": 1, "fn": 7}, "问置换": {"precision": 0.5, "recall": 0.5, "f1_score": 0.5, "support": 6, "tp": 3, "fp": 3, "fn": 3}, "问联系方式": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问试乘试驾": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 5, "tp": 5, "fp": 0, "fn": 0}, "问车型": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 0, "tp": 0, "fp": 1, "fn": 0}, "问配置": {"precision": 0.8333333333333334, "recall": 0.7142857142857143, "f1_score": 0.7692307692307692, "support": 28, "tp": 20, "fp": 4, "fn": 8}, "问颜色": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 2, "tp": 2, "fp": 2, "fn": 0}}, "confusion_matrix": {"想卖车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"想卖车": 0, "撩妹": 5, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "看车型": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 25, "要买车": 1, "金融政策": 0, "问价格": 2, "问优惠": 1, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 3, "问颜色": 1}, "要买车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 4, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 1}, "金融政策": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问价格": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 2, "要买车": 1, "金融政策": 1, "问价格": 31, "问优惠": 6, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 13, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问地址": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 16, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问库存车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问政策": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 3, "问价格": 0, "问优惠": 2, "问地址": 1, "问库存车": 0, "问政策": 6, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问置换": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 3, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 1, "问颜色": 0}, "问联系方式": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 5, "问车型": 0, "问配置": 0, "问颜色": 0}, "问车型": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 0}, "问配置": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 1, "看车型": 3, "要买车": 1, "金融政策": 0, "问价格": 2, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 1, "问配置": 20, "问颜色": 0}, "问颜色": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型的版本，用户希望了解不同配置版本的情况，因此符合“看车型”的意图。\n\n看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问车型": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 177, "valid_predictions": 177, "note": "Calculated on 177 valid samples out of 345 total"}}}}