"""
Tests for CLI interface.
"""

import tempfile
import os
from unittest.mock import patch, MagicMock

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import yaml

from ai_model_evaluation.cli.main import cli


class TestCLI:
    """Test CLI interface."""
    
    def create_test_config_file(self):
        """Create a temporary test configuration file."""
        config_data = {
            'providers': [
                {
                    'id': 'test_provider',
                    'name': 'Test Provider',
                    'base_url': 'https://api.test.com/v1/',
                    'api_key': 'test_key'
                }
            ],
            'models': [
                {
                    'id': 'test_model',
                    'provider_id': 'test_provider',
                    'model_name': 'test-model',
                    'display_name': 'Test Model',
                    'temperature': 0.7,
                    'thinking_enabled': False
                }
            ],
            'prompt_templates': [
                {
                    'id': 'test_template',
                    'name': 'Test Template',
                    'template': '{original_prompt}\n\nA: {variable_a}\nB: {variable_b}',
                    'variables': ['original_prompt', 'variable_a', 'variable_b'],
                    'description': 'Test template'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            return f.name
    
    def test_cli_help(self):
        """Test CLI help command."""
        runner = CliRunner()
        result = runner.invoke(cli, ['--help'])
        
        assert result.exit_code == 0
        assert 'AI Model Evaluation System' in result.output
        assert 'config' in result.output
        assert 'info' in result.output
    
    def test_config_show(self):
        """Test config show command."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, ['--config', config_path, 'config', 'show'])
            
            assert result.exit_code == 0
            assert 'Providers:' in result.output
            assert 'Test Provider' in result.output
            assert 'Models:' in result.output
            assert 'Test Model' in result.output
            assert 'Prompt Templates:' in result.output
            assert 'Test Template' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_config_validate_valid(self):
        """Test config validate command with valid config."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, ['--config', config_path, 'config', 'validate'])
            
            assert result.exit_code == 0
            assert 'Configuration is valid' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_config_validate_invalid(self):
        """Test config validate command with invalid config."""
        # Create invalid config (missing required fields)
        invalid_config = {
            'providers': [
                {
                    'id': 'test_provider',
                    'name': 'Test Provider'
                    # Missing base_url and api_key
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(invalid_config, f)
            config_path = f.name
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, ['--config', config_path, 'config', 'validate'])
            
            assert result.exit_code == 1
            assert 'Configuration validation failed' in result.output or 'Error' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_config_add_provider(self):
        """Test config add-provider command."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'config', 'add-provider',
                '--name', 'New Provider',
                '--base-url', 'https://api.new.com/v1/',
                '--api-key', 'new_key'
            ])
            
            assert result.exit_code == 0
            assert 'Added provider' in result.output
            assert 'New Provider' in result.output
            
            # Verify provider was added
            result = runner.invoke(cli, ['--config', config_path, 'config', 'show'])
            assert 'New Provider' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_config_add_model(self):
        """Test config add-model command."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'config', 'add-model',
                '--provider-id', 'test_provider',
                '--model-name', 'new-model',
                '--display-name', 'New Model',
                '--temperature', '0.8',
                '--thinking'
            ])
            
            assert result.exit_code == 0
            assert 'Added model' in result.output
            assert 'New Model' in result.output
            
            # Verify model was added
            result = runner.invoke(cli, ['--config', config_path, 'config', 'show'])
            assert 'New Model' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_config_list_providers(self):
        """Test config list-providers command."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, ['--config', config_path, 'config', 'list-providers'])
            
            assert result.exit_code == 0
            assert 'Configured Providers' in result.output
            assert 'test_provider' in result.output
            assert 'Test Provider' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_config_list_models(self):
        """Test config list-models command."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, ['--config', config_path, 'config', 'list-models'])
            
            assert result.exit_code == 0
            assert 'All Configured Models' in result.output
            assert 'test_model' in result.output
            assert 'Test Model' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_config_list_models_filtered(self):
        """Test config list-models command with provider filter."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path, 
                'config', 'list-models', 
                '--provider-id', 'test_provider'
            ])
            
            assert result.exit_code == 0
            assert 'Models for Provider' in result.output
            assert 'test_model' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_info_system(self):
        """Test info command without file."""
        runner = CliRunner()
        result = runner.invoke(cli, ['info'])
        
        assert result.exit_code == 0
        assert 'AI Model Evaluation System' in result.output
        assert 'Version: 0.1.0' in result.output
        assert 'Features:' in result.output
    
    def test_info_file_nonexistent(self):
        """Test info command with nonexistent file."""
        runner = CliRunner()
        result = runner.invoke(cli, ['info', '--file', '/nonexistent/file.csv'])
        
        assert result.exit_code == 1
        assert 'File not found' in result.output
    
    def test_info_file_valid(self):
        """Test info command with valid file."""
        # Create a test CSV file
        import pandas as pd
        
        data = {
            'original_prompt': ['Prompt 1', 'Prompt 2'],
            'variable_a': ['A1', 'A2'],
            'variable_b': ['B1', 'B2'],
            'expected_result': ['Expected 1', 'Expected 2']
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, ['info', '--file', csv_path])
            
            assert result.exit_code == 0
            assert 'File Information:' in result.output
            assert 'Rows' in result.output
            assert 'Columns' in result.output
            assert '.csv' in result.output
            
        finally:
            os.unlink(csv_path)
    
    def test_config_nonexistent_file(self):
        """Test CLI with nonexistent config file."""
        runner = CliRunner()
        result = runner.invoke(cli, ['--config', '/nonexistent/config.yaml', 'config', 'show'])
        
        assert result.exit_code == 1
        assert 'Error' in result.output
    
    def test_config_add_model_invalid_provider(self):
        """Test adding model with invalid provider."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'config', 'add-model',
                '--provider-id', 'nonexistent_provider',
                '--model-name', 'test-model',
                '--display-name', 'Test Model'
            ])
            
            assert result.exit_code == 1
            assert 'Error' in result.output
            
        finally:
            os.unlink(config_path)
    
    @patch('ai_model_evaluation.cli.main.APIClientManager')
    def test_config_test_connection(self, mock_client_manager):
        """Test config test-connection command."""
        config_path = self.create_test_config_file()
        
        # Mock the client manager
        mock_manager_instance = MagicMock()
        mock_client_manager.return_value = mock_manager_instance
        
        # Mock connection info
        mock_manager_instance.get_all_connection_info.return_value = {
            'test_provider': {
                'is_connected': True,
                'response_time': 0.5,
                'error_message': None
            }
        }
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, ['--config', config_path, 'config', 'test-connection'])
            
            assert result.exit_code == 0
            assert 'Connection Test Results' in result.output
            assert 'test_provider' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_preview_command(self):
        """Test preview command."""
        config_path = self.create_test_config_file()
        
        # Create test CSV file
        import pandas as pd
        data = {
            'original_prompt': ['Compare options', 'Choose best'],
            'variable_a': ['Option A', 'Choice A'],
            'variable_b': ['Option B', 'Choice B'],
            'expected_result': ['A is better', 'B is better']
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'preview',
                '--input', csv_path,
                '--template', 'test_template',
                '--rows', '2'
            ])
            
            assert result.exit_code == 0
            assert 'Prompt Preview' in result.output
            assert 'Compare options' in result.output
            assert 'Template Statistics' in result.output
            
        finally:
            os.unlink(config_path)
            os.unlink(csv_path)
    
    def test_preview_command_invalid_template(self):
        """Test preview command with invalid template."""
        config_path = self.create_test_config_file()
        
        # Create test CSV file
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt'],
            'variable_a': ['A'],
            'variable_b': ['B']
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'preview',
                '--input', csv_path,
                '--template', 'nonexistent_template'
            ])
            
            assert result.exit_code == 1
            assert 'not found' in result.output
            
        finally:
            os.unlink(config_path)
            os.unlink(csv_path)
    
    @patch('ai_model_evaluation.cli.main.EvaluationTaskManager')
    def test_evaluate_command_basic(self, mock_task_manager):
        """Test basic evaluate command."""
        config_path = self.create_test_config_file()
        
        # Create test CSV file
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt'],
            'variable_a': ['A'],
            'variable_b': ['B'],
            'expected_result': ['Expected']
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        # Mock task manager
        mock_manager_instance = MagicMock()
        mock_task_manager.return_value = mock_manager_instance
        
        mock_task = MagicMock()
        mock_task.id = 'test_task_id'
        mock_manager_instance.create_task.return_value = mock_task
        
        # Mock results
        from ai_model_evaluation.models.core import ResultRow
        mock_results = [
            ResultRow(
                original_prompt='Test prompt',
                variable_a='A',
                variable_b='B',
                expected_result='Expected',
                model_results={'test_model': 'Response'},
                execution_time={'test_model': 1.0},
                error_info={'test_model': None}
            )
        ]
        mock_manager_instance.execute_task.return_value = mock_results
        
        try:
            with tempfile.TemporaryDirectory() as output_dir:
                runner = CliRunner()
                result = runner.invoke(cli, [
                    '--config', config_path,
                    'evaluate',
                    '--input', csv_path,
                    '--template', 'test_template',
                    '--models', 'test_model',
                    '--output', output_dir,
                    '--name', 'test_evaluation'
                ])
                
                # Should complete successfully
                assert result.exit_code == 0
                assert 'Evaluation completed successfully' in result.output
                assert 'Results saved to' in result.output
                
        finally:
            os.unlink(config_path)
            os.unlink(csv_path)
    
    def test_evaluate_command_invalid_input_file(self):
        """Test evaluate command with invalid input file."""
        config_path = self.create_test_config_file()
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'evaluate',
                '--input', '/nonexistent/file.csv',
                '--template', 'test_template',
                '--models', 'test_model',
                '--output', '/tmp/output'
            ])
            
            assert result.exit_code == 1
            assert 'not found' in result.output
            
        finally:
            os.unlink(config_path)
    
    def test_evaluate_command_invalid_template(self):
        """Test evaluate command with invalid template."""
        config_path = self.create_test_config_file()
        
        # Create test CSV file
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt'],
            'variable_a': ['A'],
            'variable_b': ['B']
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'evaluate',
                '--input', csv_path,
                '--template', 'nonexistent_template',
                '--models', 'test_model',
                '--output', '/tmp/output'
            ])
            
            assert result.exit_code == 1
            assert 'not found' in result.output
            
        finally:
            os.unlink(config_path)
            os.unlink(csv_path)
    
    def test_evaluate_command_invalid_model(self):
        """Test evaluate command with invalid model."""
        config_path = self.create_test_config_file()
        
        # Create test CSV file
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt'],
            'variable_a': ['A'],
            'variable_b': ['B']
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            runner = CliRunner()
            result = runner.invoke(cli, [
                '--config', config_path,
                'evaluate',
                '--input', csv_path,
                '--template', 'test_template',
                '--models', 'nonexistent_model',
                '--output', '/tmp/output'
            ])
            
            assert result.exit_code == 1
            assert 'not found' in result.output
            
        finally:
            os.unlink(config_path)
            os.unlink(csv_path)
    
    def test_cli_main_no_subcommand(self):
        """Test CLI main command without subcommand."""
        runner = CliRunner()
        result = runner.invoke(cli, [])
        
        assert result.exit_code == 0
        assert 'Welcome' in result.output
        assert 'AI Model Evaluation System' in result.output    
def test_report_generate_text(self):
        """Test generating text report."""
        # Create test results file
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt'],
            'variable_a': ['A value'],
            'variable_b': ['B value'],
            'expected_result': ['Expected'],
            'model1_result': ['Response 1'],
            'model1_execution_time': [1.0],
            'model2_result': ['Response 2'],
            'model2_execution_time': [2.0]
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            result = self.runner.invoke(cli, [
                'report', 'generate', csv_path,
                '--format', 'text'
            ])
            assert result.exit_code == 0
            assert 'Report generated successfully' in result.output
        finally:
            os.unlink(csv_path)
    
    def test_report_generate_json(self):
        """Test generating JSON report."""
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt'],
            'variable_a': ['A value'],
            'variable_b': ['B value'],
            'model1_result': ['Response 1'],
            'model1_execution_time': [1.0]
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            result = self.runner.invoke(cli, [
                'report', 'generate', csv_path,
                '--format', 'json'
            ])
            assert result.exit_code == 0
            assert 'Report generated successfully' in result.output
        finally:
            os.unlink(csv_path)
    
    def test_report_analyze(self):
        """Test analyzing results."""
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt 1', 'Test prompt 2'],
            'variable_a': ['A1', 'A2'],
            'variable_b': ['B1', 'B2'],
            'model1_result': ['Response 1', 'Response 3'],
            'model1_execution_time': [1.0, 1.5],
            'model2_result': ['Response 2', ''],
            'model2_execution_time': [2.0, 0.5],
            'model2_error': [None, 'API Error']
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            result = self.runner.invoke(cli, [
                'report', 'analyze', csv_path
            ])
            assert result.exit_code == 0
            assert 'Analysis Results' in result.output
            assert 'Overall Metrics' in result.output
            assert 'Model Performance' in result.output
        finally:
            os.unlink(csv_path)
    
    def test_report_analyze_with_models_filter(self):
        """Test analyzing results with model filter."""
        import pandas as pd
        data = {
            'original_prompt': ['Test prompt'],
            'variable_a': ['A value'],
            'variable_b': ['B value'],
            'model1_result': ['Response 1'],
            'model1_execution_time': [1.0],
            'model2_result': ['Response 2'],
            'model2_execution_time': [2.0]
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            csv_path = f.name
        
        try:
            result = self.runner.invoke(cli, [
                'report', 'analyze', csv_path,
                '--models', 'model1'
            ])
            assert result.exit_code == 0
            assert 'Filtering analysis to models: model1' in result.output
        finally:
            os.unlink(csv_path)    def t
est_history_list_empty(self):
        """Test listing empty history."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.list_tasks.return_value = []
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'list'])
            assert result.exit_code == 0
            assert 'No tasks found in history' in result.output
    
    def test_history_list_with_tasks(self):
        """Test listing history with tasks."""
        from ai_model_evaluation.models.core import EvaluationTask, TaskStatus
        from datetime import datetime
        
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            # Create mock task
            mock_task = EvaluationTask(
                id='test_task_id',
                name='Test Task',
                prompt_template_id='test_template',
                selected_models=['model1', 'model2'],
                input_file_path='test.csv',
                output_directory='output/',
                status=TaskStatus.COMPLETED,
                created_at=datetime.now()
            )
            
            mock_manager = MagicMock()
            mock_manager.list_tasks.return_value = [mock_task]
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'list'])
            assert result.exit_code == 0
            assert 'Task History' in result.output
            assert 'Test Task' in result.output
            assert 'completed' in result.output
    
    def test_history_show_existing_task(self):
        """Test showing existing task details."""
        from ai_model_evaluation.models.core import EvaluationTask, TaskStatus
        from datetime import datetime
        
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_task = EvaluationTask(
                id='test_task_id',
                name='Test Task',
                prompt_template_id='test_template',
                selected_models=['model1'],
                input_file_path='test.csv',
                output_directory='output/',
                status=TaskStatus.COMPLETED,
                created_at=datetime.now()
            )
            
            mock_manager = MagicMock()
            mock_manager.get_task.return_value = mock_task
            mock_manager.get_task_results.return_value = []
            mock_manager.get_task_logs.return_value = []
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'show', 'test_task_id'])
            assert result.exit_code == 0
            assert 'Task Details' in result.output
            assert 'Test Task' in result.output
    
    def test_history_show_nonexistent_task(self):
        """Test showing nonexistent task."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.get_task.return_value = None
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'show', 'nonexistent'])
            assert result.exit_code == 0
            assert 'not found in history' in result.output
    
    def test_history_search(self):
        """Test searching task history."""
        from ai_model_evaluation.models.core import EvaluationTask, TaskStatus
        from datetime import datetime
        
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_task = EvaluationTask(
                id='test_task_id',
                name='Model Evaluation Test',
                prompt_template_id='test_template',
                selected_models=['model1'],
                input_file_path='test.csv',
                output_directory='output/',
                status=TaskStatus.COMPLETED,
                created_at=datetime.now()
            )
            
            mock_manager = MagicMock()
            mock_manager.search_tasks.return_value = [mock_task]
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'search', 'Evaluation'])
            assert result.exit_code == 0
            assert 'Found 1 tasks matching' in result.output
            assert 'Model Evaluation Test' in result.output
    
    def test_history_search_no_results(self):
        """Test searching with no results."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.search_tasks.return_value = []
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'search', 'nonexistent'])
            assert result.exit_code == 0
            assert 'No tasks found matching' in result.output
    
    def test_history_delete_existing(self):
        """Test deleting existing task."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.delete_task.return_value = True
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'delete', 'test_task'], input='y\n')
            assert result.exit_code == 0
            assert 'deleted from history' in result.output
    
    def test_history_delete_nonexistent(self):
        """Test deleting nonexistent task."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.delete_task.return_value = False
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'delete', 'nonexistent'], input='y\n')
            assert result.exit_code == 0
            assert 'not found in history' in result.output
    
    def test_history_export(self):
        """Test exporting task data."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.export_task_data.return_value = None
            mock_history_manager.return_value = mock_manager
            
            with patch('os.path.getsize', return_value=1024):
                result = self.runner.invoke(cli, ['history', 'export', 'test_task'])
                assert result.exit_code == 0
                assert 'exported successfully' in result.output
    
    def test_history_stats(self):
        """Test showing history statistics."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.get_task_statistics.return_value = {
                'pending': 1,
                'running': 0,
                'completed': 3,
                'failed': 1,
                'cancelled': 0,
                'total': 5
            }
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'stats'])
            assert result.exit_code == 0
            assert 'Task History Statistics' in result.output
            assert 'Task Counts by Status' in result.output
    
    def test_history_cleanup_dry_run(self):
        """Test cleanup dry run."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'cleanup', '--dry-run'], input='y\n')
            assert result.exit_code == 0
            assert 'Dry run' in result.output
    
    def test_history_cleanup(self):
        """Test actual cleanup."""
        with patch('ai_model_evaluation.cli.main.HistoryManager') as mock_history_manager:
            mock_manager = MagicMock()
            mock_manager.cleanup_old_tasks.return_value = 2
            mock_history_manager.return_value = mock_manager
            
            result = self.runner.invoke(cli, ['history', 'cleanup'], input='y\n')
            assert result.exit_code == 0
            assert 'Deleted 2 old tasks' in result.output