#!/usr/bin/env python3
"""
自动化AI模型评估脚本
支持配置文件路径、模型列表，自动生成结果文件夹
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

class AutoEvaluator:
    """自动化评估器"""
    
    def __init__(self):
        """初始化评估器"""
        self.base_dir = Path.cwd()
        self.results_base_dir = self.base_dir / "evaluation_results"
        
    def run_evaluation(self, 
                      input_file: str,
                      template: str,
                      models: List[str],
                      output_name: Optional[str] = None) -> Dict:
        """
        运行评估
        
        Args:
            input_file: 输入文件路径
            template: 模板名称
            models: 模型列表
            output_name: 输出名称（可选）
            
        Returns:
            评估结果信息
        """
        # 验证输入文件
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 生成输出文件夹名称
        if output_name is None:
            file_stem = input_path.stem  # 不包含扩展名的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_name = f"{file_stem}_{timestamp}"
        
        # 创建结果目录
        output_dir = self.results_base_dir / output_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 构建模型参数
        models_str = ",".join(models)
        
        # 构建评估命令
        cmd = [
            "poetry", "run", "ai-eval", "evaluate",
            "-i", str(input_path),
            "-t", template,
            "-m", models_str,
            "-o", str(output_dir),
            "--name", output_name
        ]
        
        print(f"🚀 开始评估...")
        print(f"📁 输入文件: {input_file}")
        print(f"📋 模板: {template}")
        print(f"🤖 模型: {models}")
        print(f"📂 输出目录: {output_dir}")
        print(f"⚡ 执行命令: {' '.join(cmd)}")
        print("-" * 60)
        
        # 执行评估
        try:
            result = subprocess.run(cmd, 
                                  capture_output=True, 
                                  text=True, 
                                  cwd=self.base_dir)
            
            # 保存执行日志
            log_file = output_dir / "execution.log"
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"执行时间: {datetime.now().isoformat()}\n")
                f.write(f"命令: {' '.join(cmd)}\n")
                f.write(f"返回码: {result.returncode}\n")
                f.write(f"\n=== STDOUT ===\n{result.stdout}\n")
                f.write(f"\n=== STDERR ===\n{result.stderr}\n")
            
            if result.returncode == 0:
                print("✅ 评估完成!")
                
                # 查找生成的结果文件
                result_files = list(output_dir.glob("*_results.csv"))

                evaluation_info = {
                    "status": "success",
                    "input_file": str(input_path),
                    "template": template,
                    "models": models,
                    "output_dir": str(output_dir),
                    "result_files": [str(f) for f in result_files],
                    "execution_time": datetime.now().isoformat(),
                    "log_file": str(log_file)
                }

                # 保存评估信息
                info_file = output_dir / "evaluation_info.json"
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(evaluation_info, f, ensure_ascii=False, indent=2)

                print(f"📊 结果文件: {result_files}")
                print(f"📋 评估信息: {info_file}")

                # 自动生成分析报告
                if result_files:
                    self._generate_analysis_report(result_files[0], output_dir)

                return evaluation_info
                
            else:
                print(f"❌ 评估失败，返回码: {result.returncode}")
                print(f"错误信息: {result.stderr}")
                
                evaluation_info = {
                    "status": "failed",
                    "input_file": str(input_path),
                    "template": template,
                    "models": models,
                    "output_dir": str(output_dir),
                    "error": result.stderr,
                    "execution_time": datetime.now().isoformat(),
                    "log_file": str(log_file)
                }
                
                # 保存失败信息
                info_file = output_dir / "evaluation_info.json"
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(evaluation_info, f, ensure_ascii=False, indent=2)
                
                return evaluation_info
                
        except Exception as e:
            print(f"❌ 执行过程中发生错误: {e}")
            raise
    
    def batch_evaluate(self, evaluations: List[Dict]) -> List[Dict]:
        """
        批量评估
        
        Args:
            evaluations: 评估配置列表，每个包含 input_file, template, models, output_name
            
        Returns:
            评估结果列表
        """
        results = []
        
        for i, eval_config in enumerate(evaluations, 1):
            print(f"\n{'='*60}")
            print(f"📋 批量评估 {i}/{len(evaluations)}")
            print(f"{'='*60}")
            
            try:
                result = self.run_evaluation(**eval_config)
                results.append(result)
            except Exception as e:
                print(f"❌ 评估 {i} 失败: {e}")
                results.append({
                    "status": "error",
                    "error": str(e),
                    "config": eval_config
                })
        
        # 保存批量评估总结
        summary_file = self.results_base_dir / f"batch_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                "total_evaluations": len(evaluations),
                "successful": len([r for r in results if r.get("status") == "success"]),
                "failed": len([r for r in results if r.get("status") != "success"]),
                "results": results,
                "summary_time": datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 批量评估总结保存到: {summary_file}")
        return results

    def _generate_analysis_report(self, result_file: Path, output_dir: Path):
        """
        生成分析报告

        Args:
            result_file: 结果CSV文件路径
            output_dir: 输出目录
        """
        try:
            import pandas as pd

            print("📈 正在生成分析报告...")

            # 读取结果文件
            df = pd.read_csv(result_file)

            # 基本统计
            total_rows = len(df)
            model_columns = [col for col in df.columns if col.endswith('_result')]

            analysis = {
                "file_info": {
                    "result_file": str(result_file),
                    "total_rows": total_rows,
                    "columns": list(df.columns)
                },
                "model_performance": {}
            }

            # 分析每个模型的表现
            for model_col in model_columns:
                model_name = model_col.replace('_result', '')

                # 计算成功率
                valid_results = df[model_col].notna() & (df[model_col] != 'None') & (df[model_col] != '')
                success_count = valid_results.sum()
                success_rate = success_count / total_rows if total_rows > 0 else 0

                # 获取执行时间信息
                time_col = f"{model_name}_execution_time"
                avg_time = df[time_col].mean() if time_col in df.columns else 0

                # 结果分布
                if success_count > 0:
                    result_distribution = df[valid_results][model_col].value_counts().to_dict()
                else:
                    result_distribution = {}

                # 计算分类指标（准确率、精确率、召回率、F1）
                classification_metrics = self._calculate_classification_metrics(df, model_col)

                analysis["model_performance"][model_name] = {
                    "total_requests": total_rows,
                    "successful_requests": int(success_count),
                    "success_rate": float(success_rate),
                    "average_execution_time": float(avg_time),
                    "result_distribution": result_distribution,
                    "classification_metrics": classification_metrics
                }

            # 保存分析结果
            analysis_file = output_dir / "analysis_report.json"
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)

            # 生成简单的HTML报告
            html_content = self._generate_html_report(analysis)
            html_file = output_dir / "analysis_report.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"📊 分析报告已生成:")
            print(f"   - JSON: {analysis_file}")
            print(f"   - HTML: {html_file}")

        except Exception as e:
            print(f"⚠️ 生成分析报告时出错: {e}")

    def _calculate_classification_metrics(self, df, model_col: str) -> Dict:
        """
        计算分类指标：准确率、精确率、召回率、F1分数

        Args:
            df: 数据框
            model_col: 模型结果列名

        Returns:
            分类指标字典
        """
        try:
            import pandas as pd
            from collections import defaultdict

            # 获取预测结果和真实标签
            predicted = df[model_col]
            actual = df['expected_result'] if 'expected_result' in df.columns else None

            if actual is None:
                return {
                    "accuracy": None,
                    "precision": None,
                    "recall": None,
                    "f1_score": None,
                    "per_class_metrics": {},
                    "confusion_matrix": {},
                    "note": "No expected_result column found"
                }

            # 过滤有效数据（both predicted and actual are not null/none)
            valid_mask = (
                predicted.notna() &
                actual.notna() &
                (predicted != 'None') &
                (predicted != '') &
                (actual != 'none') &
                (actual != '')
            )

            if valid_mask.sum() == 0:
                return {
                    "accuracy": 0.0,
                    "precision": 0.0,
                    "recall": 0.0,
                    "f1_score": 0.0,
                    "per_class_metrics": {},
                    "confusion_matrix": {},
                    "note": "No valid predictions found"
                }

            y_true = actual[valid_mask].astype(str)
            y_pred = predicted[valid_mask].astype(str)

            # 获取所有类别
            all_classes = sorted(set(y_true) | set(y_pred))

            # 计算混淆矩阵
            confusion_matrix = {}
            for true_class in all_classes:
                confusion_matrix[true_class] = {}
                for pred_class in all_classes:
                    count = ((y_true == true_class) & (y_pred == pred_class)).sum()
                    confusion_matrix[true_class][pred_class] = int(count)

            # 计算整体准确率
            accuracy = (y_true == y_pred).mean()

            # 计算每个类别的指标
            per_class_metrics = {}
            class_precisions = []
            class_recalls = []
            class_f1s = []

            for class_name in all_classes:
                # True Positive, False Positive, False Negative
                tp = ((y_true == class_name) & (y_pred == class_name)).sum()
                fp = ((y_true != class_name) & (y_pred == class_name)).sum()
                fn = ((y_true == class_name) & (y_pred != class_name)).sum()

                # 精确率 = TP / (TP + FP)
                precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0

                # 召回率 = TP / (TP + FN)
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0

                # F1分数 = 2 * (precision * recall) / (precision + recall)
                f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

                per_class_metrics[class_name] = {
                    "precision": float(precision),
                    "recall": float(recall),
                    "f1_score": float(f1),
                    "support": int((y_true == class_name).sum()),
                    "tp": int(tp),
                    "fp": int(fp),
                    "fn": int(fn)
                }

                # 收集用于计算宏平均的指标
                if (tp + fp) > 0:  # 只有当该类别有预测时才计入
                    class_precisions.append(precision)
                if (tp + fn) > 0:  # 只有当该类别有真实样本时才计入
                    class_recalls.append(recall)
                if f1 > 0:
                    class_f1s.append(f1)

            # 计算宏平均
            macro_precision = sum(class_precisions) / len(class_precisions) if class_precisions else 0.0
            macro_recall = sum(class_recalls) / len(class_recalls) if class_recalls else 0.0
            macro_f1 = sum(class_f1s) / len(class_f1s) if class_f1s else 0.0

            # 计算加权平均（按支持度加权）
            total_support = sum(metrics["support"] for metrics in per_class_metrics.values())
            if total_support > 0:
                weighted_precision = sum(
                    metrics["precision"] * metrics["support"]
                    for metrics in per_class_metrics.values()
                ) / total_support
                weighted_recall = sum(
                    metrics["recall"] * metrics["support"]
                    for metrics in per_class_metrics.values()
                ) / total_support
                weighted_f1 = sum(
                    metrics["f1_score"] * metrics["support"]
                    for metrics in per_class_metrics.values()
                ) / total_support
            else:
                weighted_precision = weighted_recall = weighted_f1 = 0.0

            return {
                "accuracy": float(accuracy),
                "macro_avg": {
                    "precision": float(macro_precision),
                    "recall": float(macro_recall),
                    "f1_score": float(macro_f1)
                },
                "weighted_avg": {
                    "precision": float(weighted_precision),
                    "recall": float(weighted_recall),
                    "f1_score": float(weighted_f1)
                },
                "per_class_metrics": per_class_metrics,
                "confusion_matrix": confusion_matrix,
                "total_samples": int(len(y_true)),
                "valid_predictions": int(valid_mask.sum()),
                "note": f"Calculated on {valid_mask.sum()} valid samples out of {len(df)} total"
            }

        except Exception as e:
            return {
                "accuracy": None,
                "precision": None,
                "recall": None,
                "f1_score": None,
                "per_class_metrics": {},
                "confusion_matrix": {},
                "error": str(e),
                "note": "Error calculating classification metrics"
            }

    def _generate_html_report(self, analysis: Dict) -> str:
        """生成HTML分析报告"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型评估报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .section {{ margin: 20px 0; }}
        .stats {{ display: flex; gap: 20px; flex-wrap: wrap; }}
        .stat-card {{ background-color: #e9f5ff; padding: 15px; border-radius: 5px; min-width: 200px; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #0066cc; }}
        table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .success {{ color: #28a745; }}
        .warning {{ color: #ffc107; }}
        .error {{ color: #dc3545; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>AI模型评估报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>数据文件: {analysis['file_info']['result_file']}</p>
    </div>

    <div class="section">
        <h2>评估概览</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{analysis['file_info']['total_rows']}</div>
                <div>总测试样本</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(analysis['model_performance'])}</div>
                <div>评估模型数</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>模型表现</h2>
        <table>
            <tr>
                <th>模型名称</th>
                <th>成功请求数</th>
                <th>成功率</th>
                <th>平均响应时间</th>
                <th>状态</th>
            </tr>
"""

        for model_name, perf in analysis['model_performance'].items():
            success_rate = perf['success_rate']
            status_class = "success" if success_rate > 0.8 else "warning" if success_rate > 0.5 else "error"
            status_text = "优秀" if success_rate > 0.8 else "良好" if success_rate > 0.5 else "需改进"

            html += f"""
            <tr>
                <td>{model_name}</td>
                <td>{perf['successful_requests']}/{perf['total_requests']}</td>
                <td>{success_rate:.1%}</td>
                <td>{perf['average_execution_time']:.2f}s</td>
                <td class="{status_class}">{status_text}</td>
            </tr>
"""

        html += """
        </table>
    </div>

    <div class="section">
        <h2>分类指标详情</h2>
"""

        # 为每个模型添加分类指标
        for model_name, perf in analysis['model_performance'].items():
            metrics = perf.get('classification_metrics', {})
            if not metrics or metrics.get('accuracy') is None:
                html += f"""
        <h3>{model_name} 模型</h3>
        <p class="warning">⚠️ 无法计算分类指标：{metrics.get('note', '数据不足')}</p>
"""
                continue

            html += f"""
        <h3>{model_name} 模型分类指标</h3>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{metrics['accuracy']:.3f}</div>
                <div>准确率 (Accuracy)</div>
            </div>
"""

            if 'macro_avg' in metrics:
                html += f"""
            <div class="stat-card">
                <div class="stat-number">{metrics['macro_avg']['precision']:.3f}</div>
                <div>宏平均精确率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{metrics['macro_avg']['recall']:.3f}</div>
                <div>宏平均召回率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{metrics['macro_avg']['f1_score']:.3f}</div>
                <div>宏平均F1分数</div>
            </div>
"""

            html += """
        </div>

        <h4>各类别详细指标</h4>
        <table>
            <tr>
                <th>类别</th>
                <th>精确率</th>
                <th>召回率</th>
                <th>F1分数</th>
                <th>支持度</th>
            </tr>
"""

            # 添加每个类别的指标
            for class_name, class_metrics in metrics.get('per_class_metrics', {}).items():
                html += f"""
            <tr>
                <td>{class_name}</td>
                <td>{class_metrics['precision']:.3f}</td>
                <td>{class_metrics['recall']:.3f}</td>
                <td>{class_metrics['f1_score']:.3f}</td>
                <td>{class_metrics['support']}</td>
            </tr>
"""

            html += """
        </table>

        <h4>混淆矩阵</h4>
        <table>
            <tr>
                <th>真实\\预测</th>
"""

            # 混淆矩阵表头
            confusion_matrix = metrics.get('confusion_matrix', {})
            if confusion_matrix:
                classes = sorted(confusion_matrix.keys())
                for pred_class in classes:
                    html += f"<th>{pred_class}</th>"
                html += "</tr>"

                # 混淆矩阵内容
                for true_class in classes:
                    html += f"<tr><td><strong>{true_class}</strong></td>"
                    for pred_class in classes:
                        count = confusion_matrix.get(true_class, {}).get(pred_class, 0)
                        html += f"<td>{count}</td>"
                    html += "</tr>"

            html += "</table><br>"

            # 添加说明
            if 'note' in metrics:
                html += f'<p><small>📝 {metrics["note"]}</small></p>'

        html += """
    </div>
</body>
</html>
"""
        return html


def main():
    """主函数 - 配置和运行评估"""
    
    # ==================== 配置区域 ====================
    
    # 1. 单个评估配置
    SINGLE_EVALUATION = {
        "input_file": "yitu.xlsx",  # 输入文件路径
        "template": "yitu",         # 模板名称
        "models": ["qwen-plus"],    # 模型列表
        "output_name": None         # 输出名称（None为自动生成）
    }
    
    # 2. 批量评估配置
    BATCH_EVALUATIONS = [
        {
            "input_file": "yitu.xlsx",
            "template": "yitu", 
            "models": ["qwen-plus","glm-4.5"],
            "output_name": "yitu_qwen_only"
        },
        {
            "input_file": "yitu.xlsx",
            "template": "yitu",
            "models": ["qwen-plus", "ernie-4"],
            "output_name": "yitu_multi_models"
        },
        # 可以添加更多评估配置...
    ]
    
    # 3. 运行模式选择
    RUN_MODE = "single"  # "single" 或 "batch"
    
    # ==================== 执行区域 ====================
    
    evaluator = AutoEvaluator()
    
    try:
        if RUN_MODE == "single":
            print("🔄 运行单个评估...")
            result = evaluator.run_evaluation(**SINGLE_EVALUATION)
            print(f"\n✅ 评估完成! 状态: {result['status']}")
            
        elif RUN_MODE == "batch":
            print("🔄 运行批量评估...")
            results = evaluator.batch_evaluate(BATCH_EVALUATIONS)
            successful = len([r for r in results if r.get("status") == "success"])
            print(f"\n✅ 批量评估完成! 成功: {successful}/{len(results)}")
            
        else:
            print("❌ 无效的运行模式，请选择 'single' 或 'batch'")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断评估")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 评估过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
