# AI模型评估分类指标分析

## 功能概述

现在的评估系统支持完整的分类指标分析，包括：

- ✅ **准确率 (Accuracy)**：正确预测的样本占总样本的比例
- ✅ **精确率 (Precision)**：预测为正类的样本中实际为正类的比例
- ✅ **召回率 (Recall)**：实际为正类的样本中被正确预测的比例
- ✅ **F1分数 (F1-Score)**：精确率和召回率的调和平均数
- ✅ **宏平均 (Macro Average)**：各类别指标的简单平均
- ✅ **加权平均 (Weighted Average)**：按类别样本数加权的平均
- ✅ **混淆矩阵 (Confusion Matrix)**：详细的预测结果矩阵
- ✅ **各类别详细指标**：每个意图类别的具体表现

## 使用方法

### 1. 自动评估脚本 (推荐)

使用 `auto_evaluate.py` 进行评估，会自动生成包含分类指标的分析报告：

```bash
# 配置评估参数
SINGLE_EVALUATION = {
    "input_file": "yitu.xlsx",
    "template": "yitu",
    "models": ["qwen-plus", "ernie-4"],  # 支持多模型对比
    "output_name": "classification_analysis"
}

# 运行评估
poetry run python auto_evaluate.py
```

### 2. 专门的分类指标分析脚本

使用 `analyze_classification_metrics.py` 对已有结果进行深度分析：

```bash
# 分析指定结果文件
poetry run python analyze_classification_metrics.py evaluation_results/multi_model_test/multi_model_test_results.csv

# 或使用默认路径
poetry run python analyze_classification_metrics.py
```

## 输出结果

### 1. 控制台输出示例

```
📊 分析文件: evaluation_results/multi_model_test/multi_model_test_results.csv
📋 总样本数: 345
🤖 发现模型: ['qwen-plus', 'ernie-4']

==================================================
🔍 分析模型: qwen-plus
==================================================
📈 预测成功: 198/345 (57.4%)
📊 有效对比样本: 177

📊 整体指标:
   准确率 (Accuracy): 0.746

📈 宏平均 (Macro Average):
   精确率 (Precision): 0.661
   召回率 (Recall): 0.747
   F1分数 (F1-Score): 0.731

⚖️ 加权平均 (Weighted Average):
   精确率 (Precision): 0.768
   召回率 (Recall): 0.746
   F1分数 (F1-Score): 0.747

🎯 各类别详细指标:
类别              精确率      召回率      F1分数     支持度   
--------------------------------------------------
问价格             0.865    0.780    0.821    41    
看车型             0.733    0.647    0.688    34    
问配置             0.840    0.724    0.778    29    
问地址             0.889    1.000    0.941    16    
问优惠             0.565    0.929    0.703    14    
```

### 2. JSON分析报告

生成详细的JSON格式分析报告：

```json
{
  "qwen-plus": {
    "model_name": "qwen-plus",
    "accuracy": 0.746,
    "macro_avg": {
      "precision": 0.661,
      "recall": 0.747,
      "f1_score": 0.731
    },
    "weighted_avg": {
      "precision": 0.768,
      "recall": 0.746,
      "f1_score": 0.747
    },
    "per_class_metrics": {
      "问价格": {
        "precision": 0.865,
        "recall": 0.780,
        "f1_score": 0.821,
        "support": 41,
        "tp": 32,
        "fp": 5,
        "fn": 9
      }
    },
    "confusion_matrix": {
      "问价格": {
        "问价格": 32,
        "看车型": 3,
        "问配置": 6
      }
    }
  }
}
```

### 3. HTML可视化报告

自动生成美观的HTML报告，包含：
- 📊 整体指标概览
- 📈 各模型对比表格
- 🎯 详细分类指标
- 📋 混淆矩阵可视化

## 指标解释

### 基本指标

1. **准确率 (Accuracy)**
   - 公式：`(TP + TN) / (TP + TN + FP + FN)`
   - 含义：所有预测中正确的比例
   - 适用：类别平衡的数据集

2. **精确率 (Precision)**
   - 公式：`TP / (TP + FP)`
   - 含义：预测为正类中实际为正类的比例
   - 关注：减少误报 (False Positive)

3. **召回率 (Recall)**
   - 公式：`TP / (TP + FN)`
   - 含义：实际正类中被正确识别的比例
   - 关注：减少漏报 (False Negative)

4. **F1分数 (F1-Score)**
   - 公式：`2 × (Precision × Recall) / (Precision + Recall)`
   - 含义：精确率和召回率的调和平均
   - 适用：需要平衡精确率和召回率

### 平均方式

1. **宏平均 (Macro Average)**
   - 计算：每个类别指标的简单平均
   - 特点：每个类别权重相等
   - 适用：关注少数类别的表现

2. **加权平均 (Weighted Average)**
   - 计算：按类别样本数加权平均
   - 特点：样本多的类别权重大
   - 适用：反映整体数据分布

## 实际应用场景

### 意图识别评估

在汽车销售意图识别场景中：

```
🎯 各类别表现分析:
- 问价格: 精确率0.865, 召回率0.780 → 识别准确，少量漏识别
- 看车型: 精确率0.733, 召回率0.647 → 有一定误识别和漏识别
- 问地址: 精确率0.889, 召回率1.000 → 表现优秀，几乎无漏报
- 问优惠: 精确率0.565, 召回率0.929 → 召回率高但误报较多
```

### 模型对比

```
模型对比分析:
qwen-plus:  准确率0.746, 宏平均F1: 0.731
ernie-4:    准确率0.000, 无有效预测

结论: qwen-plus表现良好，ernie-4需要检查配置
```

## 文件结构

评估完成后生成的文件：

```
evaluation_results/
└── model_analysis_20250804/
    ├── model_analysis_20250804_results.csv      # 原始结果
    ├── analysis_report.json                     # 包含分类指标的分析报告
    ├── analysis_report.html                     # HTML可视化报告
    ├── classification_metrics_analysis.json     # 专门的分类指标分析
    └── evaluation_info.json                     # 评估元信息
```

## 注意事项

1. **数据质量**：确保 `expected_result` 列包含正确的标签
2. **有效样本**：只有同时包含预测结果和期望结果的样本才参与计算
3. **类别不平衡**：关注宏平均和加权平均的差异
4. **混淆矩阵**：查看具体的误分类模式
5. **支持度**：注意样本数较少的类别指标可能不稳定

## 高级功能

### 自定义分析

可以修改 `analyze_classification_metrics.py` 来：
- 添加更多评估指标
- 自定义可视化图表
- 导出不同格式的报告
- 进行统计显著性测试

### 批量对比

支持批量分析多个模型和数据集：
- 不同模型在同一数据集上的表现
- 同一模型在不同数据集上的表现
- 时间序列的模型性能变化

这套分类指标分析系统为AI模型评估提供了全面、专业的性能分析能力！🚀
