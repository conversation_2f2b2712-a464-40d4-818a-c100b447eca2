# API 参考文档

本文档提供了AI模型评估系统内部API和模块的详细信息。

## 核心模块

### 配置管理

#### `ai_model_evaluation.config.manager.ConfigManager`

管理系统配置，包括提供商、模型和模板。

```python
from ai_model_evaluation.config.manager import ConfigManager

# Initialize with default config file
config_manager = ConfigManager()

# Initialize with custom config file
config_manager = ConfigManager("custom_config.yaml")

# Load configuration
config = config_manager.load_config()

# Validate configuration
config_manager.load_config(validate=True)
```

**Methods:**

- `load_config(validate: bool = False) -> ConfigData`
  - Loads configuration from YAML file
  - Optionally validates configuration structure
  - Returns ConfigData object

- `save_config() -> None`
  - Saves current configuration to file
  - Creates backup of existing file

- `add_provider(name: str, base_url: str, api_key: str, provider_id: str = None) -> Provider`
  - Adds new provider to configuration
  - Auto-generates ID if not provided
  - Returns created Provider object

- `add_model(provider_id: str, model: ModelConfig) -> None`
  - Adds new model to configuration
  - Validates provider exists
  - Raises ConfigurationError if provider not found

#### `ai_model_evaluation.config.loader.ConfigLoader`

Low-level configuration file operations.

```python
from ai_model_evaluation.config.loader import ConfigLoader

loader = ConfigLoader("config.yaml")
raw_config = loader.load()
```

**Methods:**

- `load() -> Dict[str, Any]`
  - Loads raw configuration from YAML
  - Performs environment variable substitution
  - Returns dictionary representation

- `validate_schema(config_data: Dict[str, Any]) -> None`
  - Validates configuration against schema
  - Raises ValidationError for invalid configuration

### Data Models

#### `ai_model_evaluation.models.core`

Core data structures used throughout the system.

**Provider**
```python
from ai_model_evaluation.models.core import Provider

provider = Provider(
    id="openai",
    name="OpenAI",
    base_url="https://api.openai.com/v1/",
    api_key="sk-...",
    is_active=True
)
```

**ModelConfig**
```python
from ai_model_evaluation.models.core import ModelConfig

model = ModelConfig(
    id="gpt4",
    provider_id="openai",
    model_name="gpt-4",
    display_name="GPT-4",
    temperature=0.7,
    thinking_enabled=False,
    max_tokens=4096
)
```

**PromptTemplate**
```python
from ai_model_evaluation.models.core import PromptTemplate

template = PromptTemplate(
    id="qa_template",
    name="Q&A Template",
    template="Question: {original_prompt}\nAnswer:",
    variables=["original_prompt"],
    description="Basic Q&A template"
)
```

**EvaluationTask**
```python
from ai_model_evaluation.models.core import EvaluationTask, TaskStatus

task = EvaluationTask(
    id="task_123",
    name="My Evaluation",
    prompt_template_id="qa_template",
    selected_models=["gpt4", "claude3"],
    input_file_path="data.csv",
    output_directory="results/",
    status=TaskStatus.PENDING
)
```

**ResultRow**
```python
from ai_model_evaluation.models.core import ResultRow

result = ResultRow(
    original_prompt="What is AI?",
    variable_a="Technology",
    variable_b="Computer Science",
    expected_result="Artificial Intelligence",
    model_results={"gpt4": "AI is artificial intelligence..."},
    execution_time={"gpt4": 1.23},
    error_info={"gpt4": None}
)
```

### File Handling

#### `ai_model_evaluation.services.file_handler.FileHandler`

Handles reading and writing of evaluation data files.

```python
from ai_model_evaluation.services.file_handler import FileHandler

file_handler = FileHandler()

# Read evaluation data
data = file_handler.read_evaluation_data("input.csv")

# Write results
file_handler.write_results(results, "output.csv")

# Get file information
info = file_handler.get_file_info("data.csv")
```

**Methods:**

- `read_evaluation_data(file_path: str) -> List[EvaluationRow]`
  - Reads CSV/Excel files
  - Converts to EvaluationRow objects
  - Handles missing columns gracefully

- `write_results(results: List[ResultRow], output_path: str) -> None`
  - Writes results to CSV format
  - Includes model responses, execution times, and errors
  - Creates directory if it doesn't exist

- `get_file_info(file_path: str) -> Dict[str, Any]`
  - Returns file metadata and validation info
  - Checks format compatibility
  - Provides column information

- `validate_file_format(file_path: str) -> FileValidationResult`
  - Validates file structure and content
  - Returns detailed validation results
  - Identifies potential issues

### API Client

#### `ai_model_evaluation.services.api_client.APIClient`

Handles communication with AI provider APIs.

```python
from ai_model_evaluation.services.api_client import APIClient
from ai_model_evaluation.models.core import Provider, ModelConfig

provider = Provider(...)
model = ModelConfig(...)

client = APIClient(provider)
response = await client.call_api("Hello, world!", model)
```

**Methods:**

- `call_api(prompt: str, model_config: ModelConfig) -> APIResponse`
  - Makes async API call to provider
  - Handles retries and error recovery
  - Returns structured response object

- `test_connection() -> bool`
  - Tests connectivity to provider
  - Validates API key and endpoint
  - Returns success status

#### `ai_model_evaluation.services.api_client.APIClientManager`

Manages multiple API clients for different providers.

```python
from ai_model_evaluation.services.api_client import APIClientManager

manager = APIClientManager()
manager.initialize_clients(providers)

# Get client for specific provider
client = manager.get_client("openai")
```

### Template Processing

#### `ai_model_evaluation.services.template_engine.TemplateEngine`

Processes prompt templates with variable substitution.

```python
from ai_model_evaluation.services.template_engine import TemplateEngine

engine = TemplateEngine()

# Process template
result = engine.process_template(
    template="Hello {name}!",
    variables={"name": "World"}
)
```

**Methods:**

- `process_template(template: str, variables: Dict[str, str]) -> str`
  - Substitutes variables in template
  - Handles missing variables gracefully
  - Returns processed template string

- `validate_template(template: str, required_variables: List[str]) -> List[str]`
  - Validates template syntax
  - Checks for required variables
  - Returns list of validation errors

#### `ai_model_evaluation.services.prompt_generator.PromptGenerator`

Generates prompts from templates and evaluation data.

```python
from ai_model_evaluation.services.prompt_generator import PromptGenerator

generator = PromptGenerator()

# Generate single prompt
prompt = generator.generate_prompt(template, evaluation_row)

# Generate batch of prompts
prompts = generator.generate_prompts(template, evaluation_data)
```

### Evaluation Engine

#### `ai_model_evaluation.services.evaluation.EvaluationEngine`

Core evaluation engine that orchestrates the evaluation process.

```python
from ai_model_evaluation.services.evaluation import EvaluationEngine

engine = EvaluationEngine(max_concurrent_requests=10)

# Run evaluation
results = await engine.run_evaluation(
    task, providers, models, template, evaluation_data
)
```

**Methods:**

- `run_evaluation(task, providers, models, template, data) -> List[ResultRow]`
  - Executes complete evaluation workflow
  - Handles concurrent API calls
  - Returns collected results

- `validate_models_connectivity(providers, models) -> Dict[str, bool]`
  - Tests connectivity for all models
  - Returns success status for each model

#### `ai_model_evaluation.services.evaluation.EvaluationTaskManager`

Manages evaluation tasks and their lifecycle.

```python
from ai_model_evaluation.services.evaluation import EvaluationTaskManager

task_manager = EvaluationTaskManager()

# Create new task
task = task_manager.create_task(
    name="My Evaluation",
    prompt_template_id="qa_template",
    selected_models=["gpt4"],
    input_file_path="data.csv",
    output_directory="results/"
)

# Execute task
results = await task_manager.execute_task(
    task.id, providers, models, template, data
)
```

### Analysis and Reporting

#### `ai_model_evaluation.services.analysis.AnalysisEngine`

Analyzes evaluation results and generates insights.

```python
from ai_model_evaluation.services.analysis import AnalysisEngine

engine = AnalysisEngine()

# Load results from file
results = engine.load_results("results.csv")

# Analyze results
analysis = engine.analyze_results(results)

# Compare models
comparison = engine.compare_models(results, ["gpt4", "claude3"])

# Generate statistics
stats = engine.generate_statistics(results)
```

**Methods:**

- `analyze_results(results: List[ResultRow]) -> AnalysisResult`
  - Calculates success rates, execution times, error counts
  - Returns comprehensive analysis object

- `compare_models(results: List[ResultRow], model_ids: List[str]) -> ComparisonReport`
  - Compares performance across specified models
  - Generates detailed comparison metrics

- `find_best_performing_model(results, metric) -> Tuple[str, float]`
  - Identifies best model based on specified metric
  - Supports 'success_rate' and 'response_time' metrics

- `identify_problematic_rows(results, threshold) -> List[Dict]`
  - Finds rows with high failure rates
  - Returns detailed information about problematic cases

#### `ai_model_evaluation.services.report_generator.ReportGenerator`

Generates reports in various formats.

```python
from ai_model_evaluation.services.report_generator import ReportGenerator

generator = ReportGenerator()

# Generate text report
text_report = generator.generate_text_report(results, "report.txt")

# Generate JSON report
json_report = generator.generate_json_report(results, "report.json")

# Generate HTML report
html_report = generator.generate_html_report(results, "report.html")
```

### History Management

#### `ai_model_evaluation.services.history_manager.HistoryManager`

Manages task history and persistent storage.

```python
from ai_model_evaluation.services.history_manager import HistoryManager

history = HistoryManager()

# Save task
history.save_task(task)

# Get task
task = history.get_task(task_id)

# List tasks
tasks = history.list_tasks(status=TaskStatus.COMPLETED, limit=10)

# Save results
history.save_task_results(task_id, results)

# Log events
history.log_task_event(task_id, "INFO", "Task completed")
```

**Methods:**

- `save_task(task: EvaluationTask) -> None`
  - Saves task to database
  - Updates existing task if ID matches

- `get_task(task_id: str) -> Optional[EvaluationTask]`
  - Retrieves task by ID
  - Returns None if not found

- `list_tasks(status, limit, offset) -> List[EvaluationTask]`
  - Lists tasks with optional filtering
  - Supports pagination

- `save_task_results(task_id: str, results: List[ResultRow]) -> None`
  - Saves evaluation results for task
  - Replaces existing results

- `get_task_results(task_id: str) -> List[Dict]`
  - Retrieves results for task
  - Returns list of result dictionaries

- `log_task_event(task_id: str, level: str, message: str, details: Dict = None) -> None`
  - Logs event for task
  - Supports INFO, WARNING, ERROR levels

- `export_task_data(task_id: str, output_path: str) -> None`
  - Exports complete task data to JSON
  - Includes task info, results, and logs

## Utility Functions

### Exception Handling

```python
from ai_model_evaluation.utils.exceptions import (
    EvaluationError,
    ConfigurationError,
    FileProcessingError,
    APIError,
    ValidationError,
    TemplateError,
    HistoryError
)

try:
    # Some operation
    pass
except ConfigurationError as e:
    print(f"Configuration error: {e}")
except APIError as e:
    print(f"API error: {e.model_id}, retries: {e.retry_count}")
```

### Helper Functions

```python
from ai_model_evaluation.utils.helpers import (
    get_file_extension,
    is_csv_file,
    is_excel_file,
    validate_file_path,
    generate_task_id,
    format_execution_time
)

# File utilities
ext = get_file_extension("data.csv")  # Returns ".csv"
is_csv = is_csv_file("data.csv")      # Returns True

# Task utilities
task_id = generate_task_id()          # Returns unique ID
formatted_time = format_execution_time(1.234)  # Returns "1.23s"
```

## CLI Integration

### Command Structure

The CLI is built using Click and follows this structure:

```
ai-eval
├── config
│   ├── show
│   ├── validate
│   ├── add-provider
│   └── add-model
├── evaluate
├── task
│   ├── list
│   ├── show
│   ├── cancel
│   ├── delete
│   └── stats
├── history
│   ├── list
│   ├── show
│   ├── search
│   ├── delete
│   ├── export
│   ├── stats
│   └── cleanup
├── report
│   ├── generate
│   ├── analyze
│   └── compare
├── validate-data
├── preview-template
├── file-info
└── test-connectivity
```

### Adding Custom Commands

To add custom CLI commands:

```python
from ai_model_evaluation.cli.main import cli
import click

@cli.command("custom-command")
@click.option("--param", help="Custom parameter")
def custom_command(param):
    """Custom command description."""
    print(f"Custom command with param: {param}")
```

## Extension Points

### Custom Providers

To add support for new AI providers:

1. Ensure API is OpenAI-compatible
2. Add provider configuration
3. Test connectivity
4. No code changes needed if API is compatible

### Custom Analysis

To add custom analysis functions:

```python
from ai_model_evaluation.services.analysis import AnalysisEngine

class CustomAnalysisEngine(AnalysisEngine):
    def custom_analysis(self, results):
        # Your custom analysis logic
        return custom_results

# Use custom engine
engine = CustomAnalysisEngine()
custom_results = engine.custom_analysis(results)
```

### Custom Report Formats

To add new report formats:

```python
from ai_model_evaluation.services.report_generator import ReportGenerator

class CustomReportGenerator(ReportGenerator):
    def generate_custom_report(self, results, output_path=None):
        # Your custom report generation logic
        return custom_report

# Use custom generator
generator = CustomReportGenerator()
report = generator.generate_custom_report(results)
```

## Error Handling Patterns

### Async Operations

```python
import asyncio
from ai_model_evaluation.utils.exceptions import APIError

async def safe_api_call():
    try:
        result = await api_client.call_api(prompt, model)
        return result
    except APIError as e:
        if e.retry_count < 3:
            await asyncio.sleep(2 ** e.retry_count)  # Exponential backoff
            return await safe_api_call()
        raise
```

### Configuration Validation

```python
from ai_model_evaluation.utils.exceptions import ConfigurationError

def validate_custom_config(config):
    if not config.get('required_field'):
        raise ConfigurationError("Required field missing")
    
    if config.get('numeric_field', 0) < 0:
        raise ConfigurationError("Numeric field must be positive")
```

### File Processing

```python
from ai_model_evaluation.utils.exceptions import FileProcessingError

def safe_file_operation(file_path):
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except FileNotFoundError:
        raise FileProcessingError(f"File not found: {file_path}")
    except PermissionError:
        raise FileProcessingError(f"Permission denied: {file_path}")
    except Exception as e:
        raise FileProcessingError(f"Unexpected error reading {file_path}: {e}")
```

## Performance Considerations

### Memory Management

- Use generators for large datasets
- Clean up resources after use
- Monitor memory usage during long evaluations

### Concurrency

- Respect API rate limits
- Use appropriate semaphore limits
- Handle backpressure gracefully

### Database Operations

- Use transactions for related operations
- Index frequently queried columns
- Clean up old data regularly

This API reference provides the foundation for extending and customizing the AI Model Evaluation System. For specific implementation details, refer to the source code and inline documentation.