# 故障排除指南

本指南帮助您诊断和解决AI模型评估系统的常见问题。

## 快速诊断命令

在深入研究具体问题之前，运行这些命令来获取系统状态：

```bash
# Check system status
ai-eval config validate
ai-eval test-connectivity
ai-eval history stats

# Check specific components
ai-eval file-info your_data.csv
ai-eval preview-template --template your_template --input your_data.csv --rows 1
```

## Configuration Issues

### Provider Configuration Problems

#### Issue: "Provider not found"
**Symptoms:**
- Error when running evaluations
- Provider not listed in `ai-eval config show`

**Diagnosis:**
```bash
ai-eval config show
ai-eval config validate
```

**Solutions:**
1. Check provider ID spelling in configuration
2. Ensure provider is marked as `is_active: true`
3. Verify YAML syntax is correct

**Example Fix:**
```yaml
# Wrong
providers:
  - id: openai_wrong
    name: OpenAI
    # ...

# Correct
providers:
  - id: openai
    name: OpenAI
    # ...
```

#### Issue: "Authentication failed" or "Invalid API key"
**Symptoms:**
- 401/403 HTTP errors
- "Unauthorized" messages
- API calls failing immediately

**Diagnosis:**
```bash
# Check environment variables
echo $OPENAI_API_KEY
echo $ANTHROPIC_API_KEY

# Test connectivity
ai-eval test-connectivity --provider openai
```

**Solutions:**
1. Verify API key is correct and active
2. Check environment variable names match configuration
3. Ensure API key has necessary permissions
4. Try regenerating API key from provider dashboard

**Example Fix:**
```bash
# Set environment variable correctly
export OPENAI_API_KEY="sk-your-actual-key-here"

# Verify in configuration
ai-eval config show
```

#### Issue: "Provider not responding" or connection timeouts
**Symptoms:**
- Long delays before errors
- Timeout messages
- Intermittent failures

**Diagnosis:**
```bash
# Test network connectivity
curl -I https://api.openai.com/v1/models
ping api.openai.com

# Check system connectivity
ai-eval test-connectivity
```

**Solutions:**
1. Check internet connection
2. Verify provider URLs are correct
3. Check for firewall/proxy issues
4. Try different provider endpoints
5. Increase timeout values

### Model Configuration Problems

#### Issue: "Model not found"
**Symptoms:**
- Error when specifying model in evaluation
- Model not listed in available models

**Diagnosis:**
```bash
ai-eval config show
grep -A 10 "models:" config.yaml
```

**Solutions:**
1. Check model ID spelling
2. Ensure model references valid provider
3. Verify model name matches provider's API

**Example Fix:**
```yaml
models:
  - id: gpt4  # Use this ID in commands
    provider_id: openai  # Must match provider ID
    model_name: gpt-4    # Must match provider's model name
    display_name: GPT-4
```

#### Issue: "Model parameters invalid"
**Symptoms:**
- API errors about temperature, max_tokens, etc.
- Unexpected model behavior

**Diagnosis:**
```bash
ai-eval config validate
```

**Solutions:**
1. Check parameter ranges (temperature: 0.0-2.0)
2. Verify max_tokens is within model limits
3. Ensure boolean values are true/false, not strings

**Example Fix:**
```yaml
models:
  - id: gpt4
    # Wrong
    temperature: "0.7"  # String instead of number
    thinking_enabled: "true"  # String instead of boolean
    
    # Correct
    temperature: 0.7
    thinking_enabled: true
```

## Data Format Issues

### CSV File Problems

#### Issue: "File format not supported" or "Cannot read file"
**Symptoms:**
- Error when loading data file
- File appears corrupted

**Diagnosis:**
```bash
ai-eval file-info your_data.csv
head -5 your_data.csv
file your_data.csv
```

**Solutions:**
1. Ensure file is valid CSV format
2. Check file encoding (should be UTF-8)
3. Verify file is not corrupted
4. Check file permissions

**Example Fix:**
```bash
# Convert encoding if needed
iconv -f ISO-8859-1 -t UTF-8 data.csv > data_utf8.csv

# Check CSV format
csvlint data.csv  # If you have csvlint installed
```

#### Issue: "Required column missing"
**Symptoms:**
- Error about missing `original_prompt` column
- Template variables not found

**Diagnosis:**
```bash
ai-eval validate-data your_data.csv
head -1 your_data.csv  # Check headers
```

**Solutions:**
1. Ensure `original_prompt` column exists
2. Check column name spelling (case-sensitive)
3. Remove extra spaces in column names
4. Ensure no special characters in headers

**Example Fix:**
```csv
# Wrong
Original Prompt,Variable A,Variable B
"Original_Prompt","variable_a","variable_b"

# Correct
original_prompt,variable_a,variable_b
```

#### Issue: "Data validation failed"
**Symptoms:**
- Warnings about data quality
- Some rows being skipped

**Diagnosis:**
```bash
ai-eval validate-data your_data.csv --verbose
ai-eval preview-template --template your_template --input your_data.csv --rows 5
```

**Solutions:**
1. Check for empty required fields
2. Remove or fix malformed rows
3. Ensure consistent data types
4. Handle special characters properly

### Template Issues

#### Issue: "Template variable not found"
**Symptoms:**
- Error during prompt generation
- Variables showing as `{variable_name}` in output

**Diagnosis:**
```bash
ai-eval preview-template --template your_template --input your_data.csv --rows 1
ai-eval validate-data your_data.csv --template your_template
```

**Solutions:**
1. Ensure all template variables exist as CSV columns
2. Check variable name spelling and case
3. Add missing columns to CSV file
4. Update template to match available data

**Example Fix:**
```yaml
# Template expects these variables
template: "Question: {original_prompt}\nContext: {context_info}"
variables: [original_prompt, context_info]

# CSV must have these columns
# original_prompt,context_info,expected_result
```

#### Issue: "Template rendering failed"
**Symptoms:**
- Malformed prompts
- Template syntax errors

**Diagnosis:**
```bash
ai-eval preview-template --template your_template --input your_data.csv --rows 1
```

**Solutions:**
1. Check template syntax (use `{variable}` format)
2. Escape special characters if needed
3. Verify YAML formatting for multi-line templates
4. Test template with sample data

## Evaluation Runtime Issues

### Performance Problems

#### Issue: "Evaluation running very slowly"
**Symptoms:**
- Low requests per second
- Long evaluation times
- System appears stuck

**Diagnosis:**
```bash
# Check system resources
top
htop  # If available
df -h  # Check disk space

# Check current tasks
ai-eval task list --status running
```

**Solutions:**
1. Reduce concurrency if hitting rate limits
2. Increase concurrency if system can handle it
3. Check network connection speed
4. Monitor API provider status
5. Free up system resources

**Performance Tuning:**
```bash
# Start conservative
ai-eval evaluate --input data.csv --template template --models model --concurrent 3

# Gradually increase
ai-eval evaluate --input data.csv --template template --models model --concurrent 10

# Monitor and adjust
ai-eval evaluate --input data.csv --template template --models model --concurrent 15
```

#### Issue: "High memory usage" or "Out of memory"
**Symptoms:**
- System becomes unresponsive
- Memory usage keeps growing
- Process killed by system

**Diagnosis:**
```bash
# Check memory usage
free -h
ps aux | grep ai-eval

# Check data file size
ls -lh your_data.csv
wc -l your_data.csv
```

**Solutions:**
1. Process smaller batches of data
2. Reduce concurrent requests
3. Clean up old results and history
4. Use more efficient data formats
5. Add more system memory

**Example Fix:**
```bash
# Split large file into smaller chunks
split -l 100 large_data.csv chunk_
for chunk in chunk_*; do
    ai-eval evaluate --input $chunk --template template --models model --output results_$(basename $chunk)/
done
```

### API and Network Issues

#### Issue: "Rate limit exceeded"
**Symptoms:**
- 429 HTTP status codes
- "Too many requests" errors
- Requests being rejected

**Diagnosis:**
```bash
# Check current concurrency settings
ai-eval config show

# Monitor request patterns
ai-eval history show <task-id> --show-logs
```

**Solutions:**
1. Reduce concurrent requests
2. Add delays between requests
3. Check provider rate limits
4. Upgrade API plan if needed
5. Distribute load across multiple providers

**Example Fix:**
```bash
# Reduce concurrency
ai-eval evaluate --input data.csv --template template --models model --concurrent 2

# Add delays (if supported)
ai-eval evaluate --input data.csv --template template --models model --concurrent 5 --delay 1
```

#### Issue: "Request timeout" or "Connection reset"
**Symptoms:**
- Requests timing out
- Network connection errors
- Intermittent failures

**Diagnosis:**
```bash
# Test network connectivity
ping api.openai.com
curl -I https://api.openai.com/v1/models

# Check timeout settings
ai-eval config show
```

**Solutions:**
1. Increase timeout values
2. Check network stability
3. Try different network connection
4. Use VPN if geographic restrictions apply
5. Contact provider support

**Example Fix:**
```bash
# Increase timeout
ai-eval evaluate --input data.csv --template template --models model --timeout 60

# Test with single request
ai-eval evaluate --input small_test.csv --template template --models model --concurrent 1
```

## Analysis and Reporting Issues

### Report Generation Problems

#### Issue: "Cannot generate report" or "Report is empty"
**Symptoms:**
- Report files are empty or malformed
- Error during report generation
- Missing data in reports

**Diagnosis:**
```bash
# Check results file
ai-eval file-info results.csv
head -5 results.csv

# Test analysis
ai-eval report analyze results.csv
```

**Solutions:**
1. Verify results file exists and is valid
2. Check file permissions
3. Ensure results contain actual data
4. Try different report formats

**Example Fix:**
```bash
# Check if results file has data
wc -l results.csv
head -10 results.csv

# Try different report format
ai-eval report generate results.csv --format json
ai-eval report generate results.csv --format text
```

#### Issue: "Analysis shows unexpected results"
**Symptoms:**
- Success rates seem wrong
- Missing model data
- Incorrect statistics

**Diagnosis:**
```bash
# Examine raw results
ai-eval report analyze results.csv --verbose
head -20 results.csv
```

**Solutions:**
1. Check for data corruption in results file
2. Verify all models completed successfully
3. Look for patterns in failed requests
4. Re-run evaluation if necessary

### History and Task Management Issues

#### Issue: "Cannot access task history"
**Symptoms:**
- History commands fail
- Database errors
- Missing task data

**Diagnosis:**
```bash
# Check history database
ai-eval history stats
ls -la ~/.ai_eval_history.db

# Test database access
ai-eval history list --limit 1
```

**Solutions:**
1. Check database file permissions
2. Verify disk space availability
3. Backup and recreate database if corrupted
4. Check for file system issues

**Example Fix:**
```bash
# Check database file
ls -la ~/.ai_eval_history.db
file ~/.ai_eval_history.db

# Backup and reset if needed
cp ~/.ai_eval_history.db ~/.ai_eval_history.db.backup
rm ~/.ai_eval_history.db
ai-eval history stats  # Will recreate database
```

## System-Level Issues

### Installation Problems

#### Issue: "Command not found: ai-eval"
**Symptoms:**
- Shell cannot find ai-eval command
- Import errors when running

**Diagnosis:**
```bash
# Check installation
pip list | grep ai-model-evaluation
which ai-eval
echo $PATH
```

**Solutions:**
1. Reinstall package: `pip install -e .`
2. Check Python path and virtual environment
3. Verify installation completed successfully
4. Add installation directory to PATH

#### Issue: "Import errors" or "Module not found"
**Symptoms:**
- Python import errors
- Missing dependency errors

**Diagnosis:**
```bash
# Check dependencies
pip check
pip list

# Test imports
python -c "import ai_model_evaluation"
```

**Solutions:**
1. Install missing dependencies: `pip install -r requirements.txt`
2. Check Python version compatibility (3.8+)
3. Verify virtual environment is activated
4. Reinstall package completely

### Environment Issues

#### Issue: "Environment variables not found"
**Symptoms:**
- API key errors despite setting variables
- Configuration not loading environment variables

**Diagnosis:**
```bash
# Check environment variables
env | grep API
echo $OPENAI_API_KEY
printenv | grep -i api
```

**Solutions:**
1. Set variables in current shell session
2. Add to shell profile (.bashrc, .zshrc)
3. Use absolute paths in configuration
4. Check variable names match exactly

**Example Fix:**
```bash
# Set for current session
export OPENAI_API_KEY="your-key-here"

# Add to shell profile
echo 'export OPENAI_API_KEY="your-key-here"' >> ~/.bashrc
source ~/.bashrc

# Verify
ai-eval config validate
```

## Getting Additional Help

### Diagnostic Information to Collect

When reporting issues, include:

1. **System Information:**
   ```bash
   python --version
   pip list | grep ai-model-evaluation
   uname -a  # On Unix systems
   ```

2. **Configuration Status:**
   ```bash
   ai-eval config validate
   ai-eval config show  # Remove API keys before sharing
   ```

3. **Error Messages:**
   - Full error output
   - Stack traces if available
   - Log files from failed tasks

4. **Data Samples:**
   - Sample of input data (anonymized)
   - Template configuration
   - Expected vs actual behavior

### Self-Help Resources

1. **Check Documentation:**
   - User Guide: `docs/user_guide.md`
   - Configuration Examples: `examples/`
   - README: `README.md`

2. **Test with Minimal Examples:**
   - Use provided sample data
   - Test with single model
   - Try basic templates first

3. **Community Resources:**
   - Search existing issues
   - Check provider documentation
   - Review API status pages

### Reporting Bugs

When reporting bugs, please include:

1. **Steps to reproduce**
2. **Expected behavior**
3. **Actual behavior**
4. **System information**
5. **Configuration (without API keys)**
6. **Error messages and logs**

This helps maintainers diagnose and fix issues quickly.

## Prevention Tips

### Regular Maintenance

1. **Keep software updated:**
   ```bash
   pip install --upgrade ai-model-evaluation
   ```

2. **Clean up regularly:**
   ```bash
   ai-eval history cleanup --days 30
   ```

3. **Monitor usage:**
   ```bash
   ai-eval history stats
   ai-eval task stats
   ```

### Best Practices

1. **Test before production:**
   - Validate configuration
   - Test with small datasets
   - Monitor API costs

2. **Monitor system resources:**
   - Check memory usage
   - Monitor disk space
   - Watch network bandwidth

3. **Backup important data:**
   - Export critical evaluations
   - Save configuration files
   - Document custom templates

Following these practices will help prevent many common issues and ensure smooth operation of the AI Model Evaluation System.