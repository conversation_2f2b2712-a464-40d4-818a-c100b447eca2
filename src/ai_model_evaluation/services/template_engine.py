"""
Template processing engine for prompt templates.
"""

import re
from typing import Dict, List, Optional, Set

from ..models.core import PromptTemplate, EvaluationRow
from ..utils.exceptions import TemplateError


class TemplateEngine:
    """Engine for processing prompt templates with variable substitution."""
    
    def __init__(self):
        """Initialize template engine."""
        self.variable_pattern = re.compile(r'\{(\w+)\}')
    
    def render_template(self, template: PromptTemplate, variables: Dict[str, str]) -> str:
        """
        Render a template with provided variables.
        
        Args:
            template: Prompt template to render
            variables: Dictionary of variable values
            
        Returns:
            Rendered template string
            
        Raises:
            TemplateError: If template rendering fails
        """
        try:
            # Validate that all required variables are provided
            missing_vars = self._get_missing_variables(template, variables)
            if missing_vars:
                raise TemplateError(f"Missing required variables: {', '.join(missing_vars)}")
            
            # Perform variable substitution
            rendered = template.template
            for var_name, var_value in variables.items():
                placeholder = f"{{{var_name}}}"
                rendered = rendered.replace(placeholder, str(var_value))
            
            return rendered
            
        except Exception as e:
            if isinstance(e, TemplateError):
                raise
            raise TemplateError(f"Failed to render template {template.id}: {e}")
    
    def render_template_for_evaluation(self, template: PromptTemplate, evaluation_row: EvaluationRow) -> str:
        """
        Render a template using data from an evaluation row.
        
        Args:
            template: Prompt template to render
            evaluation_row: Evaluation data row
            
        Returns:
            Rendered template string
        """
        variables = evaluation_row.to_dict()
        return self.render_template(template, variables)
    
    def validate_template(self, template: PromptTemplate) -> List[str]:
        """
        Validate a template for correctness.
        
        Args:
            template: Template to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check if template is empty
        if not template.template.strip():
            errors.append("Template content cannot be empty")
        
        # Extract variables from template
        template_vars = self._extract_variables_from_template(template.template)
        declared_vars = set(template.variables)
        
        # Check for missing variable declarations
        missing_declarations = template_vars - declared_vars
        if missing_declarations:
            errors.append(f"Variables used in template but not declared: {', '.join(missing_declarations)}")
        
        # Check for unused variable declarations
        unused_declarations = declared_vars - template_vars
        if unused_declarations:
            errors.append(f"Variables declared but not used in template: {', '.join(unused_declarations)}")
        
        # Check for invalid variable names
        for var_name in template_vars:
            if not self._is_valid_variable_name(var_name):
                errors.append(f"Invalid variable name: {var_name}")
        
        # Check for nested braces or malformed placeholders
        malformed = self._find_malformed_placeholders(template.template)
        if malformed:
            errors.append(f"Malformed placeholders found: {', '.join(malformed)}")
        
        return errors
    
    def preview_template(self, template: PromptTemplate, sample_variables: Optional[Dict[str, str]] = None) -> str:
        """
        Generate a preview of the template with sample data.
        
        Args:
            template: Template to preview
            sample_variables: Optional sample variable values
            
        Returns:
            Preview string with sample data or placeholders
        """
        if sample_variables:
            try:
                return self.render_template(template, sample_variables)
            except TemplateError:
                pass  # Fall back to placeholder preview
        
        # Generate preview with placeholder values
        preview_vars = {}
        for var_name in template.variables:
            preview_vars[var_name] = f"[{var_name.upper()}]"
        
        try:
            return self.render_template(template, preview_vars)
        except TemplateError:
            return template.template  # Return original if preview fails
    
    def get_template_statistics(self, template: PromptTemplate) -> Dict[str, any]:
        """
        Get statistics about a template.
        
        Args:
            template: Template to analyze
            
        Returns:
            Dictionary with template statistics
        """
        template_vars = self._extract_variables_from_template(template.template)
        
        return {
            'template_id': template.id,
            'template_name': template.name,
            'character_count': len(template.template),
            'word_count': len(template.template.split()),
            'line_count': len(template.template.splitlines()),
            'variable_count': len(template_vars),
            'variables_used': sorted(template_vars),
            'variables_declared': sorted(template.variables),
            'is_valid': len(self.validate_template(template)) == 0
        }
    
    def _extract_variables_from_template(self, template_text: str) -> Set[str]:
        """Extract variable names from template text."""
        matches = self.variable_pattern.findall(template_text)
        return set(matches)
    
    def _get_missing_variables(self, template: PromptTemplate, variables: Dict[str, str]) -> List[str]:
        """Get list of missing required variables."""
        required_vars = set(template.variables)
        provided_vars = set(variables.keys())
        missing = required_vars - provided_vars
        return sorted(missing)
    
    def _is_valid_variable_name(self, var_name: str) -> bool:
        """Check if variable name is valid (alphanumeric and underscore only)."""
        return re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', var_name) is not None
    
    def _find_malformed_placeholders(self, template_text: str) -> List[str]:
        """Find malformed placeholders in template."""
        malformed = []
        
        # Find unmatched opening braces
        open_braces = []
        for i, char in enumerate(template_text):
            if char == '{':
                open_braces.append(i)
            elif char == '}':
                if open_braces:
                    start = open_braces.pop()
                    # Check if the content between braces is valid
                    content = template_text[start+1:i]
                    if not content or not self._is_valid_variable_name(content):
                        malformed.append(template_text[start:i+1])
                else:
                    # Unmatched closing brace
                    malformed.append(f"Unmatched '}}' at position {i}")
        
        # Remaining open braces are unmatched
        for pos in open_braces:
            malformed.append(f"Unmatched '{{' at position {pos}")
        
        return malformed
    
    def create_template_from_text(self, template_id: str, name: str, template_text: str, description: str = "") -> PromptTemplate:
        """
        Create a PromptTemplate from text, automatically extracting variables.
        
        Args:
            template_id: Unique template ID
            name: Template name
            template_text: Template content
            description: Template description
            
        Returns:
            Created PromptTemplate
            
        Raises:
            TemplateError: If template is invalid
        """
        # Extract variables from template
        variables = sorted(self._extract_variables_from_template(template_text))
        
        # Create template
        template = PromptTemplate(
            id=template_id,
            name=name,
            template=template_text,
            variables=variables,
            description=description
        )
        
        # Validate template
        errors = self.validate_template(template)
        if errors:
            raise TemplateError(f"Invalid template: {'; '.join(errors)}")
        
        return template
    
    def suggest_improvements(self, template: PromptTemplate) -> List[str]:
        """
        Suggest improvements for a template.
        
        Args:
            template: Template to analyze
            
        Returns:
            List of improvement suggestions
        """
        suggestions = []
        
        # Check template length
        if len(template.template) < 10:
            suggestions.append("Template is very short. Consider adding more context or instructions.")
        elif len(template.template) > 2000:
            suggestions.append("Template is very long. Consider breaking it into smaller, more focused templates.")
        
        # Check for common issues
        if template.template.count('{') != template.template.count('}'):
            suggestions.append("Unbalanced braces detected. Check for missing opening or closing braces.")
        
        # Check variable usage
        template_vars = self._extract_variables_from_template(template.template)
        if len(template_vars) == 0:
            suggestions.append("Template has no variables. Consider adding variables for dynamic content.")
        elif len(template_vars) > 10:
            suggestions.append("Template has many variables. Consider simplifying or breaking into multiple templates.")
        
        # Check for common variable names
        common_vars = {'input', 'text', 'content', 'data'}
        used_common = template_vars.intersection(common_vars)
        if used_common:
            suggestions.append(f"Consider using more descriptive variable names instead of: {', '.join(used_common)}")
        
        # Check for repeated text
        words = template.template.split()
        if len(words) != len(set(words)):
            suggestions.append("Template contains repeated words. Consider using variables for repeated content.")
        
        return suggestions