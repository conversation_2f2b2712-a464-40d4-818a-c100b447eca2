"""
Tests for result collector service.
"""

import time
from unittest.mock import patch

import pytest

from ai_model_evaluation.services.result_collector import ResultCollector
from ai_model_evaluation.models.core import EvaluationRow, APIResponse


class TestResultCollector:
    """Test ResultCollector class."""
    
    def create_test_evaluation_row(self, prompt="Test prompt", var_a="A", var_b="B", expected="Expected"):
        """Create a test evaluation row."""
        return EvaluationRow(
            original_prompt=prompt,
            variable_a=var_a,
            variable_b=var_b,
            expected_result=expected
        )
    
    def create_test_api_response(self, content="Response", success=True, execution_time=1.0, error=None):
        """Create a test API response."""
        return APIResponse(
            request_id="test_request",
            content=content,
            success=success,
            execution_time=execution_time,
            error_message=error
        )
    
    def test_start_and_end_collection(self):
        """Test starting and ending collection timing."""
        collector = ResultCollector()
        
        # Initially no timing
        assert collector.start_time is None
        assert collector.end_time is None
        
        # Start collection
        collector.start_collection()
        assert collector.start_time is not None
        assert collector.end_time is None
        
        # End collection
        time.sleep(0.01)  # Small delay
        collector.end_collection()
        assert collector.end_time is not None
        assert collector.end_time > collector.start_time
    
    def test_add_result(self):
        """Test adding a single result."""
        collector = ResultCollector()
        
        evaluation_row = self.create_test_evaluation_row()
        api_response = self.create_test_api_response()
        
        collector.add_result(evaluation_row, "model1", api_response)
        
        results = collector.get_results()
        assert len(results) == 1
        
        result = results[0]
        assert result.original_prompt == "Test prompt"
        assert result.variable_a == "A"
        assert result.variable_b == "B"
        assert result.expected_result == "Expected"
        assert result.model_results["model1"] == "Response"
        assert result.execution_time["model1"] == 1.0
        assert result.error_info["model1"] is None
    
    def test_add_result_with_error(self):
        """Test adding a result with error."""
        collector = ResultCollector()
        
        evaluation_row = self.create_test_evaluation_row()
        api_response = self.create_test_api_response(
            content="",
            success=False,
            execution_time=0.5,
            error="API Error"
        )
        
        collector.add_result(evaluation_row, "model1", api_response)
        
        results = collector.get_results()
        result = results[0]
        assert result.model_results["model1"] == ""
        assert result.execution_time["model1"] == 0.5
        assert result.error_info["model1"] == "API Error"
    
    def test_add_multiple_models_same_row(self):
        """Test adding results from multiple models for the same evaluation row."""
        collector = ResultCollector()
        
        evaluation_row = self.create_test_evaluation_row()
        
        # Add result from model1
        api_response1 = self.create_test_api_response("Response 1", execution_time=1.0)
        collector.add_result(evaluation_row, "model1", api_response1)
        
        # Add result from model2 for the same evaluation row
        api_response2 = self.create_test_api_response("Response 2", execution_time=1.5)
        collector.add_result(evaluation_row, "model2", api_response2)
        
        results = collector.get_results()
        assert len(results) == 1  # Should be same result row
        
        result = results[0]
        assert result.model_results["model1"] == "Response 1"
        assert result.model_results["model2"] == "Response 2"
        assert result.execution_time["model1"] == 1.0
        assert result.execution_time["model2"] == 1.5
    
    def test_add_batch_results(self):
        """Test adding batch results."""
        collector = ResultCollector()
        
        evaluation_rows = [
            self.create_test_evaluation_row("Prompt 1", "A1", "B1"),
            self.create_test_evaluation_row("Prompt 2", "A2", "B2")
        ]
        
        api_responses = [
            self.create_test_api_response("Response 1", execution_time=1.0),
            self.create_test_api_response("Response 2", execution_time=1.5)
        ]
        
        collector.add_batch_results(evaluation_rows, "model1", api_responses)
        
        results = collector.get_results()
        assert len(results) == 2
        
        assert results[0].original_prompt == "Prompt 1"
        assert results[0].model_results["model1"] == "Response 1"
        assert results[1].original_prompt == "Prompt 2"
        assert results[1].model_results["model1"] == "Response 2"
    
    def test_add_batch_results_mismatched_length(self):
        """Test adding batch results with mismatched lengths."""
        collector = ResultCollector()
        
        evaluation_rows = [self.create_test_evaluation_row()]
        api_responses = [
            self.create_test_api_response("Response 1"),
            self.create_test_api_response("Response 2")  # Extra response
        ]
        
        with pytest.raises(ValueError, match="Number of evaluation rows must match"):
            collector.add_batch_results(evaluation_rows, "model1", api_responses)
    
    def test_add_error_for_model(self):
        """Test adding error for all rows for a specific model."""
        collector = ResultCollector()
        
        evaluation_rows = [
            self.create_test_evaluation_row("Prompt 1"),
            self.create_test_evaluation_row("Prompt 2")
        ]
        
        collector.add_error_for_model(evaluation_rows, "model1", "Connection failed")
        
        results = collector.get_results()
        assert len(results) == 2
        
        for result in results:
            assert result.model_results["model1"] == ""
            assert result.execution_time["model1"] == 0.0
            assert result.error_info["model1"] == "Connection failed"
    
    def test_get_results_summary_empty(self):
        """Test getting results summary with no results."""
        collector = ResultCollector()
        
        summary = collector.get_results_summary()
        
        assert summary['total_rows'] == 0
        assert summary['total_models'] == 0
        assert summary['total_requests'] == 0
        assert summary['successful_requests'] == 0
        assert summary['failed_requests'] == 0
        assert summary['success_rate'] == 0.0
        assert summary['average_execution_time'] == 0.0
    
    def test_get_results_summary_with_data(self):
        """Test getting results summary with data."""
        collector = ResultCollector()
        collector.start_collection()
        
        # Add successful results
        evaluation_row1 = self.create_test_evaluation_row("Prompt 1")
        collector.add_result(evaluation_row1, "model1", self.create_test_api_response("Response 1", execution_time=1.0))
        collector.add_result(evaluation_row1, "model2", self.create_test_api_response("Response 2", execution_time=2.0))
        
        # Add failed result
        evaluation_row2 = self.create_test_evaluation_row("Prompt 2")
        collector.add_result(evaluation_row2, "model1", self.create_test_api_response("", success=False, execution_time=0.5, error="Error"))
        
        time.sleep(0.01)
        collector.end_collection()
        
        summary = collector.get_results_summary()
        
        assert summary['total_rows'] == 2
        assert summary['total_models'] == 2
        assert summary['total_requests'] == 3
        assert summary['successful_requests'] == 2
        assert summary['failed_requests'] == 1
        assert summary['success_rate'] == 2/3
        assert summary['total_execution_time'] == 3.5  # 1.0 + 2.0 + 0.5
        assert summary['average_execution_time'] == 3.5/3
        assert summary['collection_duration'] > 0
    
    def test_get_model_statistics(self):
        """Test getting model statistics."""
        collector = ResultCollector()
        
        # Add results for model1
        evaluation_row1 = self.create_test_evaluation_row("Prompt 1")
        collector.add_result(evaluation_row1, "model1", self.create_test_api_response("Response 1", execution_time=1.0))
        
        evaluation_row2 = self.create_test_evaluation_row("Prompt 2")
        collector.add_result(evaluation_row2, "model1", self.create_test_api_response("", success=False, execution_time=0.5, error="Error"))
        
        # Add results for model2
        collector.add_result(evaluation_row1, "model2", self.create_test_api_response("Response 2", execution_time=2.0))
        collector.add_result(evaluation_row2, "model2", self.create_test_api_response("Response 3", execution_time=1.5))
        
        stats = collector.get_model_statistics()
        
        # Check model1 stats
        model1_stats = stats["model1"]
        assert model1_stats['total_requests'] == 2
        assert model1_stats['successful_requests'] == 1
        assert model1_stats['failed_requests'] == 1
        assert model1_stats['success_rate'] == 0.5
        assert model1_stats['average_execution_time'] == 0.75  # (1.0 + 0.5) / 2
        assert model1_stats['error_count'] == 1
        assert "Error" in model1_stats['unique_errors']
        
        # Check model2 stats
        model2_stats = stats["model2"]
        assert model2_stats['total_requests'] == 2
        assert model2_stats['successful_requests'] == 2
        assert model2_stats['failed_requests'] == 0
        assert model2_stats['success_rate'] == 1.0
        assert model2_stats['average_execution_time'] == 1.75  # (2.0 + 1.5) / 2
        assert model2_stats['error_count'] == 0
    
    def test_get_failed_requests(self):
        """Test getting failed requests."""
        collector = ResultCollector()
        
        # Add successful result
        evaluation_row1 = self.create_test_evaluation_row("Prompt 1", "A1", "B1")
        collector.add_result(evaluation_row1, "model1", self.create_test_api_response("Success"))
        
        # Add failed result
        evaluation_row2 = self.create_test_evaluation_row("Prompt 2", "A2", "B2")
        collector.add_result(evaluation_row2, "model1", self.create_test_api_response("", success=False, error="API Error"))
        collector.add_result(evaluation_row2, "model2", self.create_test_api_response("", success=False, error="Timeout"))
        
        failed_requests = collector.get_failed_requests()
        
        assert len(failed_requests) == 2
        
        # Check first failed request
        failed1 = failed_requests[0]
        assert failed1['row_index'] == 2  # Second row
        assert failed1['model_id'] == "model1"
        assert failed1['original_prompt'] == "Prompt 2"
        assert failed1['error_message'] == "API Error"
        
        # Check second failed request
        failed2 = failed_requests[1]
        assert failed2['row_index'] == 2  # Same row
        assert failed2['model_id'] == "model2"
        assert failed2['error_message'] == "Timeout"
    
    def test_get_performance_metrics(self):
        """Test getting performance metrics."""
        collector = ResultCollector()
        collector.start_collection()
        
        # Add results with different performance characteristics
        evaluation_row = self.create_test_evaluation_row()
        
        # Fast but unreliable model
        collector.add_result(evaluation_row, "fast_model", self.create_test_api_response("Response", execution_time=0.5))
        
        # Slow but reliable model
        collector.add_result(evaluation_row, "slow_model", self.create_test_api_response("Response", execution_time=2.0))
        
        # Add another row with failure for fast model
        evaluation_row2 = self.create_test_evaluation_row("Prompt 2")
        collector.add_result(evaluation_row2, "fast_model", self.create_test_api_response("", success=False, error="Error"))
        collector.add_result(evaluation_row2, "slow_model", self.create_test_api_response("Response", execution_time=1.8))
        
        time.sleep(0.01)
        collector.end_collection()
        
        metrics = collector.get_performance_metrics()
        
        assert metrics['fastest_model']['model_id'] == "fast_model"
        assert metrics['slowest_model']['model_id'] == "slow_model"
        assert metrics['most_reliable_model']['model_id'] == "slow_model"
        assert metrics['least_reliable_model']['model_id'] == "fast_model"
        assert metrics['total_evaluation_time'] > 0
    
    def test_export_detailed_report(self):
        """Test exporting detailed report."""
        collector = ResultCollector()
        collector.start_collection()
        
        evaluation_row = self.create_test_evaluation_row()
        collector.add_result(evaluation_row, "model1", self.create_test_api_response("Response"))
        
        time.sleep(0.01)
        collector.end_collection()
        
        report = collector.export_detailed_report()
        
        assert 'summary' in report
        assert 'model_statistics' in report
        assert 'performance_metrics' in report
        assert 'failed_requests' in report
        assert 'collection_info' in report
        
        # Check collection info
        collection_info = report['collection_info']
        assert collection_info['start_time'] is not None
        assert collection_info['end_time'] is not None
        assert collection_info['duration'] > 0
        assert 'ms' in collection_info['duration_formatted'] or 's' in collection_info['duration_formatted']
    
    def test_clear_results(self):
        """Test clearing results."""
        collector = ResultCollector()
        collector.start_collection()
        
        evaluation_row = self.create_test_evaluation_row()
        collector.add_result(evaluation_row, "model1", self.create_test_api_response("Response"))
        
        collector.end_collection()
        
        # Should have results and timing
        assert len(collector.get_results()) == 1
        assert collector.start_time is not None
        assert collector.end_time is not None
        
        # Clear results
        collector.clear_results()
        
        # Should be empty
        assert len(collector.get_results()) == 0
        assert collector.start_time is None
        assert collector.end_time is None
    
    def test_merge_results(self):
        """Test merging results from another collector."""
        collector1 = ResultCollector()
        collector2 = ResultCollector()
        
        # Add results to first collector
        evaluation_row1 = self.create_test_evaluation_row("Prompt 1")
        collector1.add_result(evaluation_row1, "model1", self.create_test_api_response("Response 1"))
        
        # Add results to second collector
        evaluation_row2 = self.create_test_evaluation_row("Prompt 2")
        collector2.add_result(evaluation_row2, "model2", self.create_test_api_response("Response 2"))
        
        # Add result for same row as collector1 but different model
        collector2.add_result(evaluation_row1, "model2", self.create_test_api_response("Response 3"))
        
        # Merge collector2 into collector1
        collector1.merge_results(collector2)
        
        results = collector1.get_results()
        assert len(results) == 2
        
        # Find the merged result for Prompt 1
        prompt1_result = next(r for r in results if r.original_prompt == "Prompt 1")
        assert "model1" in prompt1_result.model_results
        assert "model2" in prompt1_result.model_results
        assert prompt1_result.model_results["model1"] == "Response 1"
        assert prompt1_result.model_results["model2"] == "Response 3"
        
        # Check Prompt 2 result
        prompt2_result = next(r for r in results if r.original_prompt == "Prompt 2")
        assert "model2" in prompt2_result.model_results
        assert prompt2_result.model_results["model2"] == "Response 2"