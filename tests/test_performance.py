"""
Performance tests for the AI Model Evaluation System.
"""

import asyncio
import time
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, patch

import pandas as pd
import pytest

from ai_model_evaluation.services.evaluation import EvaluationEngine
from ai_model_evaluation.services.analysis import AnalysisEngine
from ai_model_evaluation.services.file_handler import <PERSON><PERSON><PERSON><PERSON>
from ai_model_evaluation.models.core import ResultRow, Provider, ModelConfig, PromptTemplate


class TestPerformance:
    """Performance tests for various system components."""
    
    def create_large_dataset(self, size: int) -> pd.DataFrame:
        """Create a large test dataset."""
        return pd.DataFrame({
            'original_prompt': [f'Test prompt {i} with some additional context to make it longer' for i in range(size)],
            'variable_a': [f'Variable A content {i}' for i in range(size)],
            'variable_b': [f'Variable B content {i}' for i in range(size)],
            'expected_result': [f'Expected result {i}' for i in range(size)]
        })
    
    def create_large_results(self, size: int) -> list:
        """Create large result dataset for analysis testing."""
        results = []
        for i in range(size):
            result = ResultRow(
                original_prompt=f'Test prompt {i}',
                variable_a=f'Variable A {i}',
                variable_b=f'Variable B {i}',
                expected_result=f'Expected {i}',
                model_results={
                    'model1': f'Response from model 1 for prompt {i}',
                    'model2': f'Response from model 2 for prompt {i}',
                    'model3': f'Response from model 3 for prompt {i}'
                },
                execution_time={'model1': 1.0 + (i % 3) * 0.5, 'model2': 1.2 + (i % 3) * 0.3, 'model3': 0.8 + (i % 3) * 0.7},
                error_info={'model1': None, 'model2': 'Error' if i % 10 == 0 else None, 'model3': None}
            )
            results.append(result)
        return results
    
    @pytest.mark.performance
    def test_file_handling_performance(self):
        """Test file handling performance with large datasets."""
        sizes = [100, 500, 1000]
        
        for size in sizes:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                data_path = temp_path / f'large_data_{size}.csv'
                
                # Create large dataset
                large_data = self.create_large_dataset(size)
                large_data.to_csv(data_path, index=False)
                
                file_handler = FileHandler()
                
                # Test read performance
                start_time = time.time()
                evaluation_data = file_handler.read_evaluation_data(str(data_path))
                read_time = time.time() - start_time
                
                assert len(evaluation_data) == size
                print(f"Read {size} rows in {read_time:.3f} seconds ({size/read_time:.1f} rows/sec)")
                
                # Performance thresholds
                if size <= 100:
                    assert read_time < 1.0, f"Reading {size} rows took too long: {read_time:.3f}s"
                elif size <= 500:
                    assert read_time < 3.0, f"Reading {size} rows took too long: {read_time:.3f}s"
                else:
                    assert read_time < 10.0, f"Reading {size} rows took too long: {read_time:.3f}s"
                
                # Test write performance
                results = self.create_large_results(size)
                output_path = temp_path / f'output_{size}.csv'
                
                start_time = time.time()
                file_handler.write_results(results, str(output_path))
                write_time = time.time() - start_time
                
                print(f"Wrote {size} results in {write_time:.3f} seconds ({size/write_time:.1f} rows/sec)")
                
                # Verify output file
                assert output_path.exists()
                output_df = pd.read_csv(output_path)
                assert len(output_df) == size
    
    @pytest.mark.performance
    def test_analysis_performance(self):
        """Test analysis engine performance with large result sets."""
        sizes = [100, 500, 1000, 2000]
        
        analysis_engine = AnalysisEngine()
        
        for size in sizes:
            results = self.create_large_results(size)
            
            # Test basic analysis performance
            start_time = time.time()
            analysis = analysis_engine.analyze_results(results)
            analysis_time = time.time() - start_time
            
            assert analysis.total_rows == size
            assert analysis.model_count == 3
            print(f"Analyzed {size} results in {analysis_time:.3f} seconds ({size/analysis_time:.1f} results/sec)")
            
            # Performance thresholds
            if size <= 100:
                assert analysis_time < 0.5, f"Analysis of {size} results took too long: {analysis_time:.3f}s"
            elif size <= 500:
                assert analysis_time < 2.0, f"Analysis of {size} results took too long: {analysis_time:.3f}s"
            elif size <= 1000:
                assert analysis_time < 5.0, f"Analysis of {size} results took too long: {analysis_time:.3f}s"
            else:
                assert analysis_time < 15.0, f"Analysis of {size} results took too long: {analysis_time:.3f}s"
            
            # Test comparison performance
            start_time = time.time()
            comparison = analysis_engine.compare_models(results)
            comparison_time = time.time() - start_time
            
            assert len(comparison.models) == 3
            print(f"Model comparison for {size} results in {comparison_time:.3f} seconds")
            
            # Test statistics generation performance
            start_time = time.time()
            statistics = analysis_engine.generate_statistics(results)
            stats_time = time.time() - start_time
            
            assert statistics.total_evaluations == size * 3  # 3 models
            print(f"Statistics generation for {size} results in {stats_time:.3f} seconds")
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_api_performance(self):
        """Test concurrent API call performance."""
        # Mock API client for performance testing
        with patch('ai_model_evaluation.services.api_client.APIClient') as mock_api_client:
            mock_client = AsyncMock()
            mock_api_client.return_value = mock_client
            
            # Simulate API response times
            async def mock_api_call(prompt, model_config):
                # Simulate varying response times
                await asyncio.sleep(0.1 + (hash(prompt) % 100) / 1000)  # 0.1-0.2 seconds
                
                from ai_model_evaluation.models.core import APIResponse
                return APIResponse(
                    request_id=f"test_{hash(prompt)}",
                    content=f"Response for {model_config.id}",
                    success=True,
                    execution_time=0.15
                )
            
            mock_client.call_api = mock_api_call
            
            # Test different concurrency levels
            concurrency_levels = [1, 5, 10, 20]
            request_counts = [10, 50, 100]
            
            for concurrency in concurrency_levels:
                for request_count in request_counts:
                    engine = EvaluationEngine(max_concurrent_requests=concurrency)
                    
                    # Create test data
                    prompts = [f"Test prompt {i}" for i in range(request_count)]
                    model_config = ModelConfig(
                        id='test_model',
                        provider_id='test_provider',
                        model_name='test-model',
                        display_name='Test Model'
                    )
                    
                    # Measure concurrent execution time
                    start_time = time.time()
                    
                    tasks = []
                    for prompt in prompts:
                        task = mock_client.call_api(prompt, model_config)
                        tasks.append(task)
                    
                    # Execute with concurrency limit
                    semaphore = asyncio.Semaphore(concurrency)
                    
                    async def limited_call(task):
                        async with semaphore:
                            return await task
                    
                    results = await asyncio.gather(*[limited_call(task) for task in tasks])
                    
                    execution_time = time.time() - start_time
                    
                    assert len(results) == request_count
                    print(f"Concurrency {concurrency}: {request_count} requests in {execution_time:.3f}s ({request_count/execution_time:.1f} req/sec)")
                    
                    # Verify concurrency benefits
                    if concurrency > 1 and request_count >= 20:
                        # Should be faster than sequential execution
                        sequential_time = request_count * 0.15  # Approximate sequential time
                        assert execution_time < sequential_time * 0.8, f"Concurrency not effective: {execution_time:.3f}s vs expected {sequential_time * 0.8:.3f}s"
    
    @pytest.mark.performance
    def test_memory_usage_analysis(self):
        """Test memory usage during analysis operations."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # Test memory usage with increasing dataset sizes
        sizes = [100, 500, 1000, 2000]
        
        for size in sizes:
            # Measure initial memory
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Create large dataset
            results = self.create_large_results(size)
            
            # Measure memory after data creation
            after_creation_memory = process.memory_info().rss / 1024 / 1024
            creation_increase = after_creation_memory - initial_memory
            
            # Perform analysis
            analysis_engine = AnalysisEngine()
            analysis = analysis_engine.analyze_results(results)
            comparison = analysis_engine.compare_models(results)
            statistics = analysis_engine.generate_statistics(results)
            
            # Measure memory after analysis
            after_analysis_memory = process.memory_info().rss / 1024 / 1024
            analysis_increase = after_analysis_memory - after_creation_memory
            total_increase = after_analysis_memory - initial_memory
            
            print(f"Size {size}: Data creation +{creation_increase:.1f}MB, Analysis +{analysis_increase:.1f}MB, Total +{total_increase:.1f}MB")
            
            # Memory usage should be reasonable
            # Rough estimate: each result should use less than 1KB on average
            expected_max_memory = size * 0.001 * 10  # 10KB per result as upper bound
            assert total_increase < expected_max_memory, f"Memory usage too high: {total_increase:.1f}MB for {size} results"
            
            # Clean up
            del results
            del analysis
            del comparison
            del statistics
    
    @pytest.mark.performance
    def test_report_generation_performance(self):
        """Test report generation performance."""
        from ai_model_evaluation.services.report_generator import ReportGenerator
        
        sizes = [50, 200, 500, 1000]
        report_generator = ReportGenerator()
        
        for size in sizes:
            results = self.create_large_results(size)
            
            # Test text report generation
            start_time = time.time()
            text_report = report_generator.generate_text_report(results)
            text_time = time.time() - start_time
            
            assert len(text_report) > 1000  # Should generate substantial report
            print(f"Text report for {size} results: {text_time:.3f}s")
            
            # Test JSON report generation
            start_time = time.time()
            json_report = report_generator.generate_json_report(results)
            json_time = time.time() - start_time
            
            assert 'metadata' in json_report
            print(f"JSON report for {size} results: {json_time:.3f}s")
            
            # Test CSV summary generation
            start_time = time.time()
            csv_summary = report_generator.generate_csv_summary(results)
            csv_time = time.time() - start_time
            
            assert len(csv_summary.split('\n')) >= 4  # Header + 3 models + empty line
            print(f"CSV summary for {size} results: {csv_time:.3f}s")
            
            # Performance thresholds
            max_time = max(0.5, size * 0.01)  # At least 0.5s, or 0.01s per result
            assert text_time < max_time, f"Text report generation too slow: {text_time:.3f}s for {size} results"
            assert json_time < max_time, f"JSON report generation too slow: {json_time:.3f}s for {size} results"
            assert csv_time < max_time * 0.5, f"CSV summary generation too slow: {csv_time:.3f}s for {size} results"
    
    @pytest.mark.performance
    def test_database_performance(self):
        """Test database operations performance."""
        from ai_model_evaluation.services.history_manager import HistoryManager
        from ai_model_evaluation.models.core import EvaluationTask, TaskStatus
        from datetime import datetime
        
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = Path(temp_dir) / 'performance_test.db'
            history_manager = HistoryManager(str(db_path))
            
            # Test task insertion performance
            task_counts = [10, 50, 100, 200]
            
            for count in task_counts:
                tasks = []
                for i in range(count):
                    task = EvaluationTask(
                        id=f'perf_task_{i}',
                        name=f'Performance Test Task {i}',
                        prompt_template_id='test_template',
                        selected_models=['model1', 'model2'],
                        input_file_path=f'input_{i}.csv',
                        output_directory=f'output_{i}/',
                        status=TaskStatus.COMPLETED,
                        created_at=datetime.now()
                    )
                    tasks.append(task)
                
                # Measure insertion time
                start_time = time.time()
                for task in tasks:
                    history_manager.save_task(task)
                insertion_time = time.time() - start_time
                
                print(f"Inserted {count} tasks in {insertion_time:.3f}s ({count/insertion_time:.1f} tasks/sec)")
                
                # Performance threshold
                assert insertion_time < count * 0.1, f"Task insertion too slow: {insertion_time:.3f}s for {count} tasks"
                
                # Test query performance
                start_time = time.time()
                retrieved_tasks = history_manager.list_tasks(limit=count)
                query_time = time.time() - start_time
                
                assert len(retrieved_tasks) == count
                print(f"Queried {count} tasks in {query_time:.3f}s")
                
                # Query should be fast
                assert query_time < 1.0, f"Task query too slow: {query_time:.3f}s for {count} tasks"
                
                # Test search performance
                start_time = time.time()
                search_results = history_manager.search_tasks("Performance", limit=count)
                search_time = time.time() - start_time
                
                print(f"Searched {count} tasks in {search_time:.3f}s")
                assert search_time < 2.0, f"Task search too slow: {search_time:.3f}s for {count} tasks"
    
    @pytest.mark.performance
    def test_template_processing_performance(self):
        """Test template processing performance."""
        from ai_model_evaluation.services.prompt_generator import PromptGenerator
        from ai_model_evaluation.models.core import PromptTemplate, EvaluationRow
        
        # Create complex template
        template = PromptTemplate(
            id='complex_template',
            name='Complex Template',
            template='''
System: You are an AI assistant.

Context: {original_prompt}
Additional Info A: {variable_a}
Additional Info B: {variable_b}
Expected Output: {expected_result}

Please provide a detailed response considering all the context provided above.
Make sure to address the main question while incorporating the additional information.
            '''.strip(),
            variables=['original_prompt', 'variable_a', 'variable_b', 'expected_result'],
            description='Complex template for performance testing'
        )
        
        prompt_generator = PromptGenerator()
        
        # Test with different data sizes
        sizes = [100, 500, 1000, 2000]
        
        for size in sizes:
            # Create evaluation data
            evaluation_data = []
            for i in range(size):
                row = EvaluationRow(
                    original_prompt=f'This is a complex prompt {i} with lots of text to test template processing performance. ' * 5,
                    variable_a=f'Variable A content {i} with additional context information. ' * 3,
                    variable_b=f'Variable B content {i} with more detailed information. ' * 3,
                    expected_result=f'Expected result {i} with comprehensive answer. ' * 2
                )
                evaluation_data.append(row)
            
            # Measure template processing time
            start_time = time.time()
            
            generated_prompts = []
            for data_row in evaluation_data:
                prompt = prompt_generator.generate_prompt(template, data_row)
                generated_prompts.append(prompt)
            
            processing_time = time.time() - start_time
            
            assert len(generated_prompts) == size
            print(f"Processed {size} templates in {processing_time:.3f}s ({size/processing_time:.1f} templates/sec)")
            
            # Performance threshold
            max_time = max(1.0, size * 0.005)  # At least 1s, or 0.005s per template
            assert processing_time < max_time, f"Template processing too slow: {processing_time:.3f}s for {size} templates"
            
            # Verify template content
            sample_prompt = generated_prompts[0]
            assert 'System: You are an AI assistant.' in sample_prompt
            assert 'This is a complex prompt 0' in sample_prompt
            assert 'Variable A content 0' in sample_prompt