#!/usr/bin/env python3
"""
生成意图识别评估分析报告
"""

import pandas as pd
import json
from datetime import datetime

def generate_analysis_report():
    # 读取结果文件
    df = pd.read_csv('results/意图识别评估_results.csv')
    
    # 基本统计
    total_rows = len(df)
    qwen_success = (df['qwen-plus_result'].notna()).sum()
    ernie_success = (df['ernie-4_result'].notna()).sum()
    
    # qwen-plus结果分析
    qwen_results = df['qwen-plus_result'].dropna()
    qwen_distribution = qwen_results.value_counts()
    
    # 准确率分析（与expected_result比较）
    valid_comparisons = df[(df['expected_result'].notna()) & (df['qwen-plus_result'].notna())]
    if len(valid_comparisons) > 0:
        accuracy = (valid_comparisons['expected_result'] == valid_comparisons['qwen-plus_result']).mean()
    else:
        accuracy = 0
    
    # 生成HTML报告
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>意图识别评估报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .stats {{ display: flex; gap: 20px; flex-wrap: wrap; }}
        .stat-card {{ background-color: #e9f5ff; padding: 15px; border-radius: 5px; min-width: 200px; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #0066cc; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .success {{ color: #28a745; }}
        .error {{ color: #dc3545; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>意图识别评估报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>评估概览</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_rows}</div>
                <div>总测试样本</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{qwen_success}</div>
                <div>qwen-plus成功数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{qwen_success/total_rows:.1%}</div>
                <div>qwen-plus成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{accuracy:.1%}</div>
                <div>准确率</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>模型表现</h2>
        <table>
            <tr>
                <th>模型</th>
                <th>成功请求数</th>
                <th>成功率</th>
                <th>状态</th>
            </tr>
            <tr>
                <td>qwen-plus (通义千问Plus)</td>
                <td>{qwen_success}/{total_rows}</td>
                <td>{qwen_success/total_rows:.1%}</td>
                <td class="success">正常运行</td>
            </tr>
            <tr>
                <td>ernie-4 (文心一言4.0)</td>
                <td>{ernie_success}/{total_rows}</td>
                <td>{ernie_success/total_rows:.1%}</td>
                <td class="error">API认证失败</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>意图识别结果分布 (qwen-plus)</h2>
        <table>
            <tr>
                <th>意图类别</th>
                <th>识别次数</th>
                <th>占比</th>
            </tr>
"""
    
    for intent, count in qwen_distribution.head(15).items():
        percentage = count / len(qwen_results) * 100
        html_content += f"""
            <tr>
                <td>{intent}</td>
                <td>{count}</td>
                <td>{percentage:.1f}%</td>
            </tr>
"""
    
    html_content += """
        </table>
    </div>
    
    <div class="section">
        <h2>问题分析</h2>
        <h3>ernie-4模型问题</h3>
        <p class="error">所有请求都返回HTTP 401错误，错误信息："The model does not exist or you do not have access to it."</p>
        <p>可能原因：</p>
        <ul>
            <li>API密钥无效或过期</li>
            <li>模型名称配置错误</li>
            <li>账户权限不足</li>
        </ul>
        
        <h3>qwen-plus模型表现</h3>
        <p class="success">成功处理了58%的请求，表现良好。</p>
        <p>主要识别的意图类别包括：问价格、看车型、问优惠、问地址等，符合汽车销售场景。</p>
    </div>
    
    <div class="section">
        <h2>建议</h2>
        <ol>
            <li><strong>修复ernie-4配置</strong>：检查API密钥和模型名称配置</li>
            <li><strong>提高成功率</strong>：分析qwen-plus失败的42%请求，可能需要调整并发数或重试机制</li>
            <li><strong>准确率评估</strong>：增加更多带标签的测试数据来评估模型准确性</li>
            <li><strong>性能优化</strong>：考虑调整请求超时和并发设置</li>
        </ol>
    </div>
</body>
</html>
"""
    
    # 保存HTML报告
    with open('results/analysis_report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    # 生成JSON报告
    json_report = {
        "evaluation_summary": {
            "total_samples": int(total_rows),
            "timestamp": datetime.now().isoformat(),
            "models_evaluated": ["qwen-plus", "ernie-4"]
        },
        "model_performance": {
            "qwen-plus": {
                "successful_requests": int(qwen_success),
                "success_rate": float(qwen_success / total_rows),
                "accuracy": float(accuracy),
                "status": "operational"
            },
            "ernie-4": {
                "successful_requests": int(ernie_success),
                "success_rate": float(ernie_success / total_rows),
                "status": "api_authentication_failed"
            }
        },
        "intent_distribution": {k: int(v) for k, v in qwen_distribution.to_dict().items()},
        "recommendations": [
            "Fix ernie-4 API configuration",
            "Improve qwen-plus success rate",
            "Add more labeled test data",
            "Optimize performance settings"
        ]
    }
    
    with open('results/analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(json_report, f, ensure_ascii=False, indent=2)
    
    print("✅ 分析报告已生成:")
    print("   - HTML报告: results/analysis_report.html")
    print("   - JSON报告: results/analysis_report.json")
    print("   - 原始结果: results/意图识别评估_results.csv")

if __name__ == "__main__":
    generate_analysis_report()
