"""
Prompt generation service that combines templates with evaluation data.
"""

from typing import Dict, List, Optional

from ..models.core import PromptTemplate, EvaluationRow
from ..utils.exceptions import TemplateError, ValidationError
from .template_engine import TemplateEngine


class PromptGenerator:
    """Service for generating prompts from templates and evaluation data."""
    
    def __init__(self):
        """Initialize prompt generator."""
        self.template_engine = TemplateEngine()
    
    def generate_prompt(self, template: PromptTemplate, evaluation_row: EvaluationRow) -> str:
        """
        Generate a prompt from template and evaluation data.
        
        Args:
            template: Prompt template to use
            evaluation_row: Evaluation data row
            
        Returns:
            Generated prompt string
            
        Raises:
            TemplateError: If prompt generation fails
        """
        return self.template_engine.render_template_for_evaluation(template, evaluation_row)
    
    def generate_prompts_batch(self, template: PromptTemplate, evaluation_rows: List[EvaluationRow]) -> List[str]:
        """
        Generate prompts for multiple evaluation rows.
        
        Args:
            template: Prompt template to use
            evaluation_rows: List of evaluation data rows
            
        Returns:
            List of generated prompt strings
            
        Raises:
            TemplateError: If any prompt generation fails
        """
        prompts = []
        
        for i, row in enumerate(evaluation_rows):
            try:
                prompt = self.generate_prompt(template, row)
                prompts.append(prompt)
            except TemplateError as e:
                raise TemplateError(f"Failed to generate prompt for row {i+1}: {e}")
        
        return prompts
    
    def validate_template_compatibility(self, template: PromptTemplate, evaluation_rows: List[EvaluationRow]) -> List[str]:
        """
        Validate that a template is compatible with evaluation data.
        
        Args:
            template: Template to validate
            evaluation_rows: Evaluation data to check against
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # First validate the template itself
        template_errors = self.template_engine.validate_template(template)
        errors.extend(template_errors)
        
        if not evaluation_rows:
            errors.append("No evaluation data provided")
            return errors
        
        # Check if template variables match evaluation data fields
        available_fields = set(evaluation_rows[0].to_dict().keys())
        required_variables = set(template.variables)
        
        missing_fields = required_variables - available_fields
        if missing_fields:
            errors.append(f"Template requires fields not available in evaluation data: {', '.join(missing_fields)}")
        
        # Check for empty or None values in required fields
        for i, row in enumerate(evaluation_rows[:10]):  # Check first 10 rows
            row_data = row.to_dict()
            for var_name in template.variables:
                if var_name in row_data:
                    value = row_data[var_name]
                    if value is None or (isinstance(value, str) and not value.strip()):
                        errors.append(f"Row {i+1} has empty value for required variable '{var_name}'")
                        break  # Only report first empty field per row
        
        return errors
    
    def preview_prompts(self, template: PromptTemplate, evaluation_rows: List[EvaluationRow], max_previews: int = 3) -> List[Dict[str, str]]:
        """
        Generate preview prompts for the first few evaluation rows.
        
        Args:
            template: Template to use
            evaluation_rows: Evaluation data
            max_previews: Maximum number of previews to generate
            
        Returns:
            List of dictionaries with row data and generated prompts
        """
        previews = []
        
        for i, row in enumerate(evaluation_rows[:max_previews]):
            try:
                prompt = self.generate_prompt(template, row)
                preview = {
                    'row_index': i + 1,
                    'generated_prompt': prompt
                }
                # Add all data from the row
                preview.update(row.to_dict())
                # Add expected_result if available
                if row.expected_result:
                    preview['expected_result'] = row.expected_result

                previews.append(preview)
            except TemplateError as e:
                preview = {
                    'row_index': i + 1,
                    'generated_prompt': f"ERROR: {e}",
                    'error': str(e)
                }
                # Add all data from the row
                preview.update(row.to_dict())
                # Add expected_result if available
                if row.expected_result:
                    preview['expected_result'] = row.expected_result

                previews.append(preview)
        
        return previews
    
    def get_template_usage_stats(self, template: PromptTemplate, evaluation_rows: List[EvaluationRow]) -> Dict[str, any]:
        """
        Get statistics about template usage with evaluation data.
        
        Args:
            template: Template to analyze
            evaluation_rows: Evaluation data
            
        Returns:
            Dictionary with usage statistics
        """
        if not evaluation_rows:
            return {
                'template_id': template.id,
                'total_rows': 0,
                'compatible_rows': 0,
                'compatibility_rate': 0.0,
                'average_prompt_length': 0,
                'variable_usage': {}
            }
        
        compatible_rows = 0
        prompt_lengths = []
        variable_usage = {var: {'empty_count': 0, 'total_length': 0} for var in template.variables}
        
        for row in evaluation_rows:
            try:
                # Try to generate prompt
                prompt = self.generate_prompt(template, row)
                compatible_rows += 1
                prompt_lengths.append(len(prompt))
                
                # Analyze variable usage
                row_data = row.to_dict()
                for var_name in template.variables:
                    if var_name in row_data:
                        value = row_data[var_name]
                        if not value or (isinstance(value, str) and not value.strip()):
                            variable_usage[var_name]['empty_count'] += 1
                        else:
                            variable_usage[var_name]['total_length'] += len(str(value))
                
            except TemplateError:
                continue
        
        # Calculate averages
        avg_prompt_length = sum(prompt_lengths) / len(prompt_lengths) if prompt_lengths else 0
        compatibility_rate = compatible_rows / len(evaluation_rows) if evaluation_rows else 0
        
        # Calculate average variable lengths
        for var_name in variable_usage:
            non_empty_count = compatible_rows - variable_usage[var_name]['empty_count']
            if non_empty_count > 0:
                variable_usage[var_name]['average_length'] = variable_usage[var_name]['total_length'] / non_empty_count
            else:
                variable_usage[var_name]['average_length'] = 0
        
        return {
            'template_id': template.id,
            'total_rows': len(evaluation_rows),
            'compatible_rows': compatible_rows,
            'compatibility_rate': compatibility_rate,
            'average_prompt_length': avg_prompt_length,
            'variable_usage': variable_usage
        }
    
    def suggest_template_improvements(self, template: PromptTemplate, evaluation_rows: List[EvaluationRow]) -> List[str]:
        """
        Suggest improvements for a template based on evaluation data.
        
        Args:
            template: Template to analyze
            evaluation_rows: Evaluation data
            
        Returns:
            List of improvement suggestions
        """
        suggestions = []
        
        # Get basic template suggestions
        basic_suggestions = self.template_engine.suggest_improvements(template)
        suggestions.extend(basic_suggestions)
        
        if not evaluation_rows:
            suggestions.append("No evaluation data available for analysis")
            return suggestions
        
        # Analyze compatibility
        stats = self.get_template_usage_stats(template, evaluation_rows)
        
        if stats['compatibility_rate'] < 0.9:
            suggestions.append(f"Template is only compatible with {stats['compatibility_rate']:.1%} of evaluation data")
        
        # Analyze variable usage
        for var_name, usage in stats['variable_usage'].items():
            empty_rate = usage['empty_count'] / stats['total_rows']
            if empty_rate > 0.1:
                suggestions.append(f"Variable '{var_name}' is empty in {empty_rate:.1%} of rows")
        
        # Check prompt length consistency
        if evaluation_rows:
            prompt_lengths = []
            for row in evaluation_rows[:20]:  # Sample first 20 rows
                try:
                    prompt = self.generate_prompt(template, row)
                    prompt_lengths.append(len(prompt))
                except TemplateError:
                    continue
            
            if prompt_lengths:
                min_length = min(prompt_lengths)
                max_length = max(prompt_lengths)
                if max_length > min_length * 2:
                    suggestions.append("Generated prompts vary significantly in length. Consider more consistent variable content.")
        
        return suggestions
    
    def create_optimized_template(self, template_id: str, name: str, evaluation_rows: List[EvaluationRow], 
                                base_template: Optional[str] = None) -> PromptTemplate:
        """
        Create an optimized template based on evaluation data.
        
        Args:
            template_id: ID for new template
            name: Name for new template
            evaluation_rows: Evaluation data to optimize for
            base_template: Optional base template text
            
        Returns:
            Optimized template
            
        Raises:
            ValidationError: If optimization fails
        """
        if not evaluation_rows:
            raise ValidationError("No evaluation data provided for optimization")
        
        # Use base template or create default
        if base_template is None:
            base_template = "{original_prompt}\n\nOption A: {variable_a}\nOption B: {variable_b}"
        
        # Create template
        try:
            template = self.template_engine.create_template_from_text(
                template_id=template_id,
                name=name,
                template_text=base_template,
                description=f"Optimized template for {len(evaluation_rows)} evaluation rows"
            )
        except TemplateError as e:
            raise ValidationError(f"Failed to create optimized template: {e}")
        
        # Validate compatibility
        errors = self.validate_template_compatibility(template, evaluation_rows)
        if errors:
            raise ValidationError(f"Optimized template is not compatible with evaluation data: {'; '.join(errors)}")
        
        return template