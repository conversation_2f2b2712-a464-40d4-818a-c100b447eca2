"""
Tests for report generator.
"""

import json
import tempfile
import os
from unittest.mock import patch, MagicMock

import pytest

from ai_model_evaluation.services.report_generator import ReportGenerator
from ai_model_evaluation.models.core import ResultRow


class TestReportGenerator:
    """Test ReportGenerator class."""
    
    def create_test_results(self):
        """Create test result rows."""
        return [
            ResultRow(
                original_prompt="What is the capital of France?",
                variable_a="France",
                variable_b="capital",
                expected_result="Paris",
                model_results={"gpt-4": "Paris", "claude": "Paris"},
                execution_time={"gpt-4": 1.2, "claude": 0.8},
                error_info={"gpt-4": None, "claude": None}
            ),
            ResultRow(
                original_prompt="What is 2 + 2?",
                variable_a="2",
                variable_b="2",
                expected_result="4",
                model_results={"gpt-4": "4", "claude": ""},
                execution_time={"gpt-4": 0.5, "claude": 0.3},
                error_info={"gpt-4": None, "claude": "API timeout"}
            ),
            ResultRow(
                original_prompt="Translate 'hello' to Spanish",
                variable_a="hello",
                variable_b="Spanish",
                expected_result="hola",
                model_results={"gpt-4": "hola", "claude": "hola"},
                execution_time={"gpt-4": 0.9, "claude": 1.1},
                error_info={"gpt-4": None, "claude": None}
            )
        ]
    
    def test_generate_text_report_empty(self):
        """Test generating text report with empty results."""
        generator = ReportGenerator()
        
        report = generator.generate_text_report([])
        
        assert "No evaluation results to analyze" in report
        assert len(report.strip()) > 0
    
    def test_generate_text_report_with_data(self):
        """Test generating text report with data."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        report = generator.generate_text_report(results)
        
        # Check that report contains expected sections
        assert "AI Model Evaluation Report" in report
        assert "EXECUTIVE SUMMARY" in report
        assert "DETAILED ANALYSIS" in report
        assert "MODEL COMPARISON" in report
        assert "DETAILED STATISTICS" in report
        assert "RECOMMENDATIONS" in report
        
        # Check that model names appear
        assert "gpt-4" in report
        assert "claude" in report
        
        # Check that metrics appear
        assert "Success Rate" in report
        assert "Execution Time" in report
    
    def test_generate_text_report_save_to_file(self):
        """Test saving text report to file."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            output_path = f.name
        
        try:
            report = generator.generate_text_report(results, output_path)
            
            # Check that file was created and contains the report
            assert os.path.exists(output_path)
            with open(output_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
            
            assert file_content == report
            assert "AI Model Evaluation Report" in file_content
        finally:
            os.unlink(output_path)
    
    def test_generate_json_report_empty(self):
        """Test generating JSON report with empty results."""
        generator = ReportGenerator()
        
        report_data = generator.generate_json_report([])
        
        assert "metadata" in report_data
        assert report_data["metadata"]["total_results"] == 0
        assert "message" in report_data
        assert "No evaluation results to analyze" in report_data["message"]
    
    def test_generate_json_report_with_data(self):
        """Test generating JSON report with data."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        report_data = generator.generate_json_report(results)
        
        # Check metadata
        assert "metadata" in report_data
        assert report_data["metadata"]["total_results"] == 3
        assert "generated_at" in report_data["metadata"]
        
        # Check executive summary
        assert "executive_summary" in report_data
        summary = report_data["executive_summary"]
        assert summary["total_rows"] == 3
        assert summary["model_count"] == 2
        assert "overall_success_rate" in summary
        assert "best_performing_model" in summary
        
        # Check model analysis
        assert "model_analysis" in report_data
        analysis = report_data["model_analysis"]
        assert "success_rates" in analysis
        assert "average_execution_times" in analysis
        assert "error_counts" in analysis
        assert "gpt-4" in analysis["success_rates"]
        assert "claude" in analysis["success_rates"]
        
        # Check model comparison
        assert "model_comparison" in report_data
        comparison = report_data["model_comparison"]
        assert "models" in comparison
        assert "metrics" in comparison
        assert "summary" in comparison
        
        # Check detailed statistics
        assert "detailed_statistics" in report_data
        stats = report_data["detailed_statistics"]
        assert "total_evaluations" in stats
        assert "successful_evaluations" in stats
        assert "failed_evaluations" in stats
        
        # Check problematic cases
        assert "problematic_cases" in report_data
        
        # Check recommendations
        assert "recommendations" in report_data
        assert isinstance(report_data["recommendations"], list)
    
    def test_generate_json_report_save_to_file(self):
        """Test saving JSON report to file."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            output_path = f.name
        
        try:
            report_data = generator.generate_json_report(results, output_path)
            
            # Check that file was created and contains valid JSON
            assert os.path.exists(output_path)
            with open(output_path, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            assert file_data == report_data
            assert "metadata" in file_data
        finally:
            os.unlink(output_path)
    
    def test_generate_csv_summary_empty(self):
        """Test generating CSV summary with empty results."""
        generator = ReportGenerator()
        
        csv_content = generator.generate_csv_summary([])
        
        lines = csv_content.strip().split('\n')
        assert len(lines) == 1  # Only header
        assert "model_id,success_rate,avg_execution_time" in lines[0]
    
    def test_generate_csv_summary_with_data(self):
        """Test generating CSV summary with data."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        csv_content = generator.generate_csv_summary(results)
        
        lines = csv_content.strip().split('\n')
        assert len(lines) == 3  # Header + 2 models
        
        # Check header
        header = lines[0]
        assert "model_id" in header
        assert "success_rate" in header
        assert "avg_execution_time" in header
        assert "total_requests" in header
        
        # Check data rows
        assert "claude" in csv_content
        assert "gpt-4" in csv_content
        
        # Check that numeric values are present
        for line in lines[1:]:
            parts = line.split(',')
            assert len(parts) >= 6  # model_id, success_rate, avg_time, total, successful, failed
            # Check that success rate is a valid number
            success_rate = float(parts[1])
            assert 0.0 <= success_rate <= 1.0
    
    def test_generate_csv_summary_save_to_file(self):
        """Test saving CSV summary to file."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            output_path = f.name
        
        try:
            csv_content = generator.generate_csv_summary(results, output_path)
            
            # Check that file was created and contains the CSV
            assert os.path.exists(output_path)
            with open(output_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
            
            assert file_content == csv_content
            assert "model_id" in file_content
        finally:
            os.unlink(output_path)
    
    def test_generate_html_report_empty(self):
        """Test generating HTML report with empty results."""
        generator = ReportGenerator()
        
        html_content = generator.generate_html_report([])
        
        assert "<!DOCTYPE html>" in html_content
        assert "<title>AI Model Evaluation Report</title>" in html_content
        assert "No models evaluated" in html_content
        assert "</html>" in html_content
    
    def test_generate_html_report_with_data(self):
        """Test generating HTML report with data."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        html_content = generator.generate_html_report(results)
        
        # Check HTML structure
        assert "<!DOCTYPE html>" in html_content
        assert "<title>AI Model Evaluation Report</title>" in html_content
        assert "</html>" in html_content
        
        # Check CSS is included
        assert "<style>" in html_content
        assert "font-family:" in html_content
        
        # Check content sections
        assert "Executive Summary" in html_content
        assert "Model Comparison" in html_content
        assert "Detailed Statistics" in html_content
        
        # Check that model names appear
        assert "gpt-4" in html_content
        assert "claude" in html_content
        
        # Check HTML table structure
        assert "<table>" in html_content
        assert "<th>" in html_content
        assert "<td>" in html_content
    
    def test_generate_html_report_save_to_file(self):
        """Test saving HTML report to file."""
        generator = ReportGenerator()
        results = self.create_test_results()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            output_path = f.name
        
        try:
            html_content = generator.generate_html_report(results, output_path)
            
            # Check that file was created and contains the HTML
            assert os.path.exists(output_path)
            with open(output_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
            
            assert file_content == html_content
            assert "<!DOCTYPE html>" in file_content
        finally:
            os.unlink(output_path)
    
    def test_generate_header(self):
        """Test generating report header."""
        generator = ReportGenerator()
        
        header = generator._generate_header()
        
        assert "AI Model Evaluation Report" in header
        assert "Generated:" in header
        assert "=" in header  # Check for separator
    
    def test_generate_executive_summary(self):
        """Test generating executive summary."""
        generator = ReportGenerator()
        
        # Mock analysis and comparison results
        from ai_model_evaluation.models.core import AnalysisResult, ComparisonReport
        
        analysis = AnalysisResult(
            total_rows=3,
            model_count=2,
            success_rates={"gpt-4": 1.0, "claude": 0.67},
            average_execution_times={"gpt-4": 0.87, "claude": 0.73},
            error_counts={"gpt-4": 0, "claude": 1}
        )
        
        comparison = ComparisonReport(
            models=["gpt-4", "claude"],
            metrics={
                "gpt-4": {"total_requests": 3.0},
                "claude": {"total_requests": 3.0}
            },
            summary="Test summary"
        )
        
        summary = generator._generate_executive_summary(analysis, comparison)
        
        assert "EXECUTIVE SUMMARY" in summary
        assert "Total evaluation rows: 3" in summary
        assert "Models evaluated: 2" in summary
        assert "Best performing model: gpt-4" in summary
        assert "Overall success rate:" in summary
    
    def test_generate_recommendations(self):
        """Test generating recommendations."""
        generator = ReportGenerator()
        
        # Mock analysis and comparison results
        from ai_model_evaluation.models.core import AnalysisResult, ComparisonReport
        
        analysis = AnalysisResult(
            total_rows=3,
            model_count=2,
            success_rates={"gpt-4": 1.0, "claude": 0.5},  # claude has low success rate
            average_execution_times={"gpt-4": 0.87, "claude": 2.5},  # claude is slow
            error_counts={"gpt-4": 0, "claude": 2}
        )
        
        comparison = ComparisonReport(
            models=["gpt-4", "claude"],
            metrics={},
            summary="Test summary"
        )
        
        problematic_rows = [{"row_index": 1, "failure_rate": 0.5}]
        
        recommendations = generator._generate_recommendations(analysis, comparison, problematic_rows)
        
        assert "RECOMMENDATIONS" in recommendations
        assert "Consider using gpt-4 for production" in recommendations
        assert "Investigate issues with claude" in recommendations  # Low success rate
        assert "Review 1 problematic input cases" in recommendations
        assert "claude is significantly slower" in recommendations
    
    def test_find_best_model_summary(self):
        """Test finding best model for summary."""
        generator = ReportGenerator()
        
        from ai_model_evaluation.models.core import AnalysisResult
        
        analysis = AnalysisResult(
            total_rows=3,
            model_count=2,
            success_rates={"gpt-4": 1.0, "claude": 0.67},
            average_execution_times={"gpt-4": 0.87, "claude": 0.73},
            error_counts={"gpt-4": 0, "claude": 1}
        )
        
        best_model = generator._find_best_model_summary(analysis)
        
        assert best_model["model_id"] == "gpt-4"
        assert best_model["success_rate"] == 1.0
    
    def test_find_best_model_summary_empty(self):
        """Test finding best model with empty analysis."""
        generator = ReportGenerator()
        
        from ai_model_evaluation.models.core import AnalysisResult
        
        analysis = AnalysisResult(
            total_rows=0,
            model_count=0,
            success_rates={},
            average_execution_times={},
            error_counts={}
        )
        
        best_model = generator._find_best_model_summary(analysis)
        
        assert best_model["model_id"] is None
        assert best_model["success_rate"] == 0.0
    
    def test_generate_recommendations_data(self):
        """Test generating recommendations as data."""
        generator = ReportGenerator()
        
        from ai_model_evaluation.models.core import AnalysisResult, ComparisonReport
        
        analysis = AnalysisResult(
            total_rows=3,
            model_count=2,
            success_rates={"gpt-4": 1.0, "claude": 0.5},
            average_execution_times={"gpt-4": 0.87, "claude": 0.73},
            error_counts={"gpt-4": 0, "claude": 2}
        )
        
        comparison = ComparisonReport(models=[], metrics={}, summary="")
        problematic_rows = [{"row_index": 1}]
        
        recommendations = generator._generate_recommendations_data(analysis, comparison, problematic_rows)
        
        assert isinstance(recommendations, list)
        assert len(recommendations) >= 2
        assert any("gpt-4 for production" in rec for rec in recommendations)
        assert any("Investigate issues with claude" in rec for rec in recommendations)
    
    def test_generate_recommendations_data_empty(self):
        """Test generating recommendations with empty analysis."""
        generator = ReportGenerator()
        
        from ai_model_evaluation.models.core import AnalysisResult, ComparisonReport
        
        analysis = AnalysisResult(
            total_rows=0,
            model_count=0,
            success_rates={},
            average_execution_times={},
            error_counts={}
        )
        
        comparison = ComparisonReport(models=[], metrics={}, summary="")
        
        recommendations = generator._generate_recommendations_data(analysis, comparison, [])
        
        assert isinstance(recommendations, list)
        assert len(recommendations) == 1
        assert "No data available for recommendations" in recommendations[0]