"""
Configuration manager for the AI Model Evaluation System.
"""

from pathlib import Path
from typing import List, Optional, Union

from ..models.config import ConfigData, ConfigValidationResult, ProviderConnectionStatus
from ..models.core import Provider, ModelConfig, PromptTemplate
from ..utils.exceptions import ConfigurationError
from .loader import ConfigLoader


class ConfigManager:
    """Manages configuration data and operations."""
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file
        """
        self.loader = ConfigLoader(config_path)
        self._config_data: Optional[ConfigData] = None
    
    def load_config(self, validate: bool = True) -> ConfigData:
        """
        Load configuration from file.
        
        Args:
            validate: Whether to validate configuration
            
        Returns:
            Loaded configuration data
        """
        self._config_data = self.loader.load(validate=validate)
        return self._config_data
    
    def save_config(self, backup: bool = True) -> None:
        """
        Save current configuration to file.
        
        Args:
            backup: Whether to create backup of existing file
        """
        if self._config_data is None:
            raise ConfigurationError("No configuration data to save")
        
        self.loader.save(self._config_data, backup=backup)
    
    def validate_config(self) -> ConfigValidationResult:
        """
        Validate current configuration.
        
        Returns:
            Validation result
        """
        return self.loader.validate()
    
    @property
    def config_data(self) -> ConfigData:
        """Get current configuration data."""
        if self._config_data is None:
            raise ConfigurationError("Configuration not loaded. Call load_config() first.")
        return self._config_data
    
    # Provider management
    def add_provider(self, name: str, base_url: str, api_key: str, provider_id: Optional[str] = None) -> Provider:
        """
        Add a new provider.
        
        Args:
            name: Provider name
            base_url: Provider API base URL
            api_key: Provider API key
            provider_id: Optional custom provider ID
            
        Returns:
            Created provider
        """
        if provider_id is None:
            # Generate ID from name
            provider_id = name.lower().replace(' ', '_').replace('-', '_')
        
        provider = Provider(
            id=provider_id,
            name=name,
            base_url=base_url,
            api_key=api_key
        )
        
        self.config_data.add_provider(provider)
        return provider
    
    def get_provider(self, provider_id: str) -> Optional[Provider]:
        """Get provider by ID."""
        return self.config_data.get_provider(provider_id)
    
    def list_providers(self) -> List[Provider]:
        """List all providers."""
        return self.config_data.providers.copy()
    
    def remove_provider(self, provider_id: str) -> bool:
        """
        Remove provider and all its models.
        
        Args:
            provider_id: Provider ID to remove
            
        Returns:
            True if provider was removed, False if not found
        """
        return self.config_data.remove_provider(provider_id)
    
    # Model management
    def add_model(self, provider_id: str, model_config: ModelConfig) -> ModelConfig:
        """
        Add a new model.
        
        Args:
            provider_id: Provider ID for the model
            model_config: Model configuration
            
        Returns:
            Added model configuration
        """
        # Ensure provider_id matches
        model_config.provider_id = provider_id
        self.config_data.add_model(model_config)
        return model_config
    
    def get_model(self, model_id: str) -> Optional[ModelConfig]:
        """Get model by ID."""
        return self.config_data.get_model(model_id)
    
    def list_models(self, provider_id: Optional[str] = None) -> List[ModelConfig]:
        """
        List models, optionally filtered by provider.
        
        Args:
            provider_id: Optional provider ID to filter by
            
        Returns:
            List of models
        """
        if provider_id:
            return self.config_data.get_models_by_provider(provider_id)
        return self.config_data.models.copy()
    
    def remove_model(self, model_id: str) -> bool:
        """
        Remove model.
        
        Args:
            model_id: Model ID to remove
            
        Returns:
            True if model was removed, False if not found
        """
        return self.config_data.remove_model(model_id)
    
    # Prompt template management
    def add_prompt_template(self, template: PromptTemplate) -> PromptTemplate:
        """
        Add a new prompt template.
        
        Args:
            template: Prompt template to add
            
        Returns:
            Added template
        """
        self.config_data.add_prompt_template(template)
        return template
    
    def get_prompt_template(self, template_id: str) -> Optional[PromptTemplate]:
        """Get prompt template by ID."""
        return self.config_data.get_prompt_template(template_id)
    
    def list_prompt_templates(self) -> List[PromptTemplate]:
        """List all prompt templates."""
        return self.config_data.prompt_templates.copy()
    
    def remove_prompt_template(self, template_id: str) -> bool:
        """
        Remove prompt template.
        
        Args:
            template_id: Template ID to remove
            
        Returns:
            True if template was removed, False if not found
        """
        return self.config_data.remove_prompt_template(template_id)
    
    # Utility methods
    def get_models_for_evaluation(self, model_ids: List[str]) -> List[ModelConfig]:
        """
        Get models for evaluation by their IDs.
        
        Args:
            model_ids: List of model IDs
            
        Returns:
            List of model configurations
            
        Raises:
            ConfigurationError: If any model ID is not found
        """
        models = []
        missing_models = []
        
        for model_id in model_ids:
            model = self.get_model(model_id)
            if model is None:
                missing_models.append(model_id)
            else:
                models.append(model)
        
        if missing_models:
            raise ConfigurationError(f"Models not found: {', '.join(missing_models)}")
        
        return models
    
    def get_provider_for_model(self, model_id: str) -> Optional[Provider]:
        """
        Get provider for a specific model.
        
        Args:
            model_id: Model ID
            
        Returns:
            Provider for the model, or None if model not found
        """
        model = self.get_model(model_id)
        if model is None:
            return None
        
        return self.get_provider(model.provider_id)
    
    def create_default_config(self) -> ConfigData:
        """
        Create a default configuration with example data.
        
        Returns:
            Default configuration
        """
        config_data = ConfigData()
        
        # Add example prompt template
        default_template = PromptTemplate(
            id="default",
            name="默认模板",
            template="{original_prompt}\n\n变量A: {variable_a}\n变量B: {variable_b}",
            variables=["original_prompt", "variable_a", "variable_b"],
            description="默认的提示词模板，包含原始提示词和两个变量"
        )
        config_data.add_prompt_template(default_template)
        
        self._config_data = config_data
        return config_data