"""
Tests for history manager.
"""

import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import patch

import pytest

from ai_model_evaluation.services.history_manager import HistoryManager
from ai_model_evaluation.models.core import EvaluationTask, TaskStatus, ResultRow
from ai_model_evaluation.utils.exceptions import HistoryError


class TestHistoryManager:
    """Test HistoryManager class."""
    
    def create_test_task(self, task_id: str = "test_task") -> EvaluationTask:
        """Create a test evaluation task."""
        return EvaluationTask(
            id=task_id,
            name="Test Task",
            prompt_template_id="test_template",
            selected_models=["model1", "model2"],
            input_file_path="test_input.csv",
            output_directory="test_output/",
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
    
    def create_test_results(self) -> list:
        """Create test result rows."""
        return [
            ResultRow(
                original_prompt="Test prompt 1",
                variable_a="A1",
                variable_b="B1",
                expected_result="Expected 1",
                model_results={"model1": "Response 1", "model2": "Response 2"},
                execution_time={"model1": 1.0, "model2": 2.0},
                error_info={"model1": None, "model2": None}
            ),
            ResultRow(
                original_prompt="Test prompt 2",
                variable_a="A2",
                variable_b="B2",
                expected_result="Expected 2",
                model_results={"model1": "Response 3", "model2": ""},
                execution_time={"model1": 1.5, "model2": 0.5},
                error_info={"model1": None, "model2": "API Error"}
            )
        ]
    
    def test_init_database(self):
        """Test database initialization."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            
            # Check that database file was created
            assert os.path.exists(db_path)
            
            # Check that tables were created
            import sqlite3
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # Check tasks table
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tasks'")
                assert cursor.fetchone() is not None
                
                # Check task_results table
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='task_results'")
                assert cursor.fetchone() is not None
                
                # Check task_logs table
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='task_logs'")
                assert cursor.fetchone() is not None
        
        finally:
            os.unlink(db_path)
    
    def test_save_and_get_task(self):
        """Test saving and retrieving a task."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            task = self.create_test_task()
            
            # Save task
            manager.save_task(task)
            
            # Retrieve task
            retrieved_task = manager.get_task(task.id)
            
            assert retrieved_task is not None
            assert retrieved_task.id == task.id
            assert retrieved_task.name == task.name
            assert retrieved_task.prompt_template_id == task.prompt_template_id
            assert retrieved_task.selected_models == task.selected_models
            assert retrieved_task.input_file_path == task.input_file_path
            assert retrieved_task.output_directory == task.output_directory
            assert retrieved_task.status == task.status
            assert retrieved_task.created_at == task.created_at
        
        finally:
            os.unlink(db_path)
    
    def test_get_nonexistent_task(self):
        """Test retrieving a nonexistent task."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            
            task = manager.get_task("nonexistent_task")
            assert task is None
        
        finally:
            os.unlink(db_path)
    
    def test_update_task(self):
        """Test updating an existing task."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            task = self.create_test_task()
            
            # Save initial task
            manager.save_task(task)
            
            # Update task
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.error_message = "Test error"
            
            manager.save_task(task)
            
            # Retrieve updated task
            retrieved_task = manager.get_task(task.id)
            
            assert retrieved_task.status == TaskStatus.COMPLETED
            assert retrieved_task.completed_at == task.completed_at
            assert retrieved_task.error_message == "Test error"
        
        finally:
            os.unlink(db_path)
    
    def test_list_tasks(self):
        """Test listing tasks."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            
            # Create and save multiple tasks
            task1 = self.create_test_task("task1")
            task2 = self.create_test_task("task2")
            task2.status = TaskStatus.COMPLETED
            task3 = self.create_test_task("task3")
            task3.status = TaskStatus.FAILED
            
            manager.save_task(task1)
            manager.save_task(task2)
            manager.save_task(task3)
            
            # List all tasks
            all_tasks = manager.list_tasks()
            assert len(all_tasks) == 3
            
            # List tasks by status
            pending_tasks = manager.list_tasks(status=TaskStatus.PENDING)
            assert len(pending_tasks) == 1
            assert pending_tasks[0].id == "task1"
            
            completed_tasks = manager.list_tasks(status=TaskStatus.COMPLETED)
            assert len(completed_tasks) == 1
            assert completed_tasks[0].id == "task2"
            
            # List with limit
            limited_tasks = manager.list_tasks(limit=2)
            assert len(limited_tasks) == 2
        
        finally:
            os.unlink(db_path)
    
    def test_delete_task(self):
        """Test deleting a task."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            task = self.create_test_task()
            
            # Save task
            manager.save_task(task)
            
            # Verify task exists
            assert manager.get_task(task.id) is not None
            
            # Delete task
            deleted = manager.delete_task(task.id)
            assert deleted is True
            
            # Verify task is gone
            assert manager.get_task(task.id) is None
            
            # Try to delete nonexistent task
            deleted = manager.delete_task("nonexistent")
            assert deleted is False
        
        finally:
            os.unlink(db_path)
    
    def test_get_task_statistics(self):
        """Test getting task statistics."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            
            # Create tasks with different statuses
            task1 = self.create_test_task("task1")
            task2 = self.create_test_task("task2")
            task2.status = TaskStatus.COMPLETED
            task3 = self.create_test_task("task3")
            task3.status = TaskStatus.COMPLETED
            task4 = self.create_test_task("task4")
            task4.status = TaskStatus.FAILED
            
            manager.save_task(task1)
            manager.save_task(task2)
            manager.save_task(task3)
            manager.save_task(task4)
            
            # Get statistics
            stats = manager.get_task_statistics()
            
            assert stats['pending'] == 1
            assert stats['completed'] == 2
            assert stats['failed'] == 1
            assert stats['running'] == 0  # Should be 0 even if not present
            assert stats['cancelled'] == 0
            assert stats['total'] == 4
        
        finally:
            os.unlink(db_path)
    
    def test_save_and_get_task_results(self):
        """Test saving and retrieving task results."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            task = self.create_test_task()
            results = self.create_test_results()
            
            # Save task and results
            manager.save_task(task)
            manager.save_task_results(task.id, results)
            
            # Retrieve results
            retrieved_results = manager.get_task_results(task.id)
            
            assert len(retrieved_results) == 2
            
            # Check first result
            result1 = retrieved_results[0]
            assert result1['row_index'] == 0
            assert result1['original_prompt'] == "Test prompt 1"
            assert result1['variable_a'] == "A1"
            assert result1['variable_b'] == "B1"
            assert result1['expected_result'] == "Expected 1"
            assert result1['model_results']['model1'] == "Response 1"
            assert result1['model_results']['model2'] == "Response 2"
            assert result1['execution_time']['model1'] == 1.0
            assert result1['execution_time']['model2'] == 2.0
            assert result1['error_info']['model1'] is None
            assert result1['error_info']['model2'] is None
            
            # Check second result
            result2 = retrieved_results[1]
            assert result2['row_index'] == 1
            assert result2['original_prompt'] == "Test prompt 2"
            assert result2['error_info']['model2'] == "API Error"
        
        finally:
            os.unlink(db_path)
    
    def test_log_task_event(self):
        """Test logging task events."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            task = self.create_test_task()
            
            # Save task
            manager.save_task(task)
            
            # Log events
            manager.log_task_event(task.id, "INFO", "Task started")
            manager.log_task_event(task.id, "WARNING", "API rate limit", {"retry_count": 3})
            manager.log_task_event(task.id, "ERROR", "Task failed", {"error_code": 500})
            
            # Retrieve all logs
            logs = manager.get_task_logs(task.id)
            assert len(logs) == 3
            
            # Check log entries
            assert logs[0]['level'] == "INFO"
            assert logs[0]['message'] == "Task started"
            assert logs[0]['details'] is None
            
            assert logs[1]['level'] == "WARNING"
            assert logs[1]['message'] == "API rate limit"
            assert logs[1]['details']['retry_count'] == 3
            
            assert logs[2]['level'] == "ERROR"
            assert logs[2]['message'] == "Task failed"
            assert logs[2]['details']['error_code'] == 500
            
            # Retrieve logs by level
            error_logs = manager.get_task_logs(task.id, level="ERROR")
            assert len(error_logs) == 1
            assert error_logs[0]['level'] == "ERROR"
        
        finally:
            os.unlink(db_path)
    
    def test_search_tasks(self):
        """Test searching tasks."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            
            # Create tasks with different names
            task1 = self.create_test_task("task1")
            task1.name = "Model Evaluation Test"
            
            task2 = self.create_test_task("task2")
            task2.name = "Performance Benchmark"
            
            task3 = self.create_test_task("task3")
            task3.name = "Quality Assessment"
            task3.prompt_template_id = "evaluation_template"
            
            manager.save_task(task1)
            manager.save_task(task2)
            manager.save_task(task3)
            
            # Search by name
            results = manager.search_tasks("Evaluation")
            assert len(results) == 1
            assert results[0].name == "Model Evaluation Test"
            
            # Search by template ID
            results = manager.search_tasks("evaluation_template")
            assert len(results) == 1
            assert results[0].name == "Quality Assessment"
            
            # Search with no matches
            results = manager.search_tasks("nonexistent")
            assert len(results) == 0
        
        finally:
            os.unlink(db_path)
    
    def test_cleanup_old_tasks(self):
        """Test cleaning up old tasks."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            
            # Create old completed task
            old_task = self.create_test_task("old_task")
            old_task.status = TaskStatus.COMPLETED
            old_task.created_at = datetime.now() - timedelta(days=35)
            
            # Create recent completed task
            recent_task = self.create_test_task("recent_task")
            recent_task.status = TaskStatus.COMPLETED
            recent_task.created_at = datetime.now() - timedelta(days=10)
            
            # Create old running task (should not be deleted)
            old_running_task = self.create_test_task("old_running_task")
            old_running_task.status = TaskStatus.RUNNING
            old_running_task.created_at = datetime.now() - timedelta(days=35)
            
            manager.save_task(old_task)
            manager.save_task(recent_task)
            manager.save_task(old_running_task)
            
            # Cleanup tasks older than 30 days
            deleted_count = manager.cleanup_old_tasks(days=30)
            
            assert deleted_count == 1
            
            # Verify correct tasks remain
            assert manager.get_task("old_task") is None
            assert manager.get_task("recent_task") is not None
            assert manager.get_task("old_running_task") is not None
        
        finally:
            os.unlink(db_path)
    
    def test_export_task_data(self):
        """Test exporting task data."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            export_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            task = self.create_test_task()
            results = self.create_test_results()
            
            # Save task, results, and logs
            manager.save_task(task)
            manager.save_task_results(task.id, results)
            manager.log_task_event(task.id, "INFO", "Task completed")
            
            # Export task data
            manager.export_task_data(task.id, export_path)
            
            # Verify export file
            assert os.path.exists(export_path)
            
            import json
            with open(export_path, 'r', encoding='utf-8') as f:
                export_data = json.load(f)
            
            assert 'task' in export_data
            assert 'results' in export_data
            assert 'logs' in export_data
            assert 'exported_at' in export_data
            
            # Check task data
            task_data = export_data['task']
            assert task_data['id'] == task.id
            assert task_data['name'] == task.name
            assert task_data['status'] == task.status.value
            
            # Check results data
            assert len(export_data['results']) == 2
            
            # Check logs data
            assert len(export_data['logs']) == 1
            assert export_data['logs'][0]['message'] == "Task completed"
        
        finally:
            os.unlink(db_path)
            os.unlink(export_path)
    
    def test_export_nonexistent_task(self):
        """Test exporting data for nonexistent task."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            manager = HistoryManager(db_path)
            
            with pytest.raises(HistoryError, match="Task nonexistent not found"):
                manager.export_task_data("nonexistent", "export.json")
        
        finally:
            os.unlink(db_path)
    
    def test_database_error_handling(self):
        """Test error handling for database operations."""
        # Test with invalid database path
        with pytest.raises(HistoryError):
            manager = HistoryManager("/invalid/path/database.db")
            task = self.create_test_task()
            manager.save_task(task)
    
    def test_default_database_path(self):
        """Test using default database path."""
        with patch('pathlib.Path.home') as mock_home:
            mock_home.return_value = Path("/tmp")
            
            manager = HistoryManager()
            
            expected_path = Path("/tmp/.ai_eval_history.db")
            assert manager.db_path == expected_path