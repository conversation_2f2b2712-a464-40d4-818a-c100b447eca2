# AI模型评估系统配置

providers:
  # - id: "openai"
  #   name: "OpenAI"
  #   base_url: "https://api.openai.com/v1/"
  #   api_key: "${OPENAI_API_KEY}"
  # - id: "zhipu"
  #   name: "智谱AI"
  #   base_url: "https://open.bigmodel.cn/api/paas/v4/"
  #   api_key: "${ZHIPU_API_KEY}"
  - id: "tongyi"
    name: "通义千问"
    base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1/"
    api_key: "sk-118e3693f3e84b029e85b9c0d4412a84"
  - id: "hunyuan"
    name: "混元AI"
    base_url: "https://api.hunyuan.cloud.tencent.com"
    api_key: "sk-FMpOdgAhRkCwfaRFVQgWAlTYFhrEaZLUNsYZXOPgk6ouffCk"
  - id: "baidu"
    name: "百度AI"
    base_url: "https://qianfan.baidubce.com/v2/"
    api_key: "bce-v3/ALTAK-tFI7EaDNOz96ifvHt1Vn0/9c0f28d9b7c343cee89f2eaf3f1a5f52dc0dc405"
  - id: "huoshan"
    name: "火山AI"
    base_url: "https://ark.cn-beijing.volces.com/api/v3/"
    api_key: "a44bd908-ca9f-4c94-a208-7e9717a8b626"

models:
  # - id: "glm-4"
  #   provider_id: "zhipu"
  #   model_name: "glm-4"
  #   display_name: "GLM-4"
  #   temperature: 0.7
  #   thinking_enabled: true
  #   max_tokens: 2000
  - id: "qwen-plus"
    provider_id: "tongyi"
    model_name: "qwen-plus"
    display_name: "通义千问Plus"
    temperature: 0.7
    thinking_enabled: true
    max_tokens: 2000
  - id: "ernie-4"
    provider_id: "baidu"
    model_name: "ERNIE-4.0"
    display_name: "文心一言4.0"
    temperature: 0.7
    thinking_enabled: true
    max_tokens: 2000
  - id: "deepseek-v3"
    provider_id: "baidu"
    model_name: "deepseek-v3"
    display_name: "DeepSeek V3"
    temperature: 0.7
    thinking_enabled: true
    max_tokens: 2000
  - id: "doubao-1.6-seed"
    provider_id: "huoshan"
    model_name: "doubao-1.6-seed"
    display_name: "豆包1.6-seed"
    temperature: 0.7
    thinking_enabled: true
    max_tokens: 2000
  - id: "hunyuan-pro"
    provider_id: "hunyuan"
    model_name: "hunyuan-pro"
    display_name: "混元Pro"
    temperature: 0.7
    thinking_enabled: true
    max_tokens: 2000

prompt_templates:
  - id: "default"
    name: "默认模板"
    template: "{original_prompt}\n\n变量A: {variable_a}\n变量B: {variable_b}"
    variables: ["original_prompt", "variable_a", "variable_b"]
    description: "默认的提示词模板，包含原始提示词和两个变量"
  - id: "comparison"
    name: "对比分析模板"
    template: "请对比分析以下两个选项：\n\n问题：{original_prompt}\n\n选项A：{variable_a}\n选项B：{variable_b}\n\n请从多个维度进行详细分析，并给出建议。"
    variables: ["original_prompt", "variable_a", "variable_b"]
    description: "用于对比分析的详细模板"
  - id: "simple"
    name: "简单模板"
    template: "{original_prompt} - A: {variable_a}, B: {variable_b}"
    variables: ["original_prompt", "variable_a", "variable_b"]
    description: "简化的提示词模板"
  - id: "yitu"
    name: "意图识别模板"
    template: "角色：你是一个汽车行业4S店直播领域的专家，善于根据历史消息分析客户发送的弹幕文本，理解背后的意图，识别潜在购车用户。

你的任务是识别用户弹幕消息是否包含以下意图，只能选择一个最符合的意图：

<意图列表>
意图名称：问优惠
意图定义：用户询问购车优惠、补贴、促销活动或折扣信息
相似问法：
反例：

意图名称：要买车
意图定义：用户明确表达购车意向，咨询购车流程、提车时间或希望获得车型推荐
相似问法：代下步、刚才在懂车帝看了
反例：

意图名称：问地址
意图定义：用户询问4S店的具体地理位置、门店地址、导航路线、说明自己所在城市或区域等
相似问法：[具体城市]、
反例：

意图名称：金融政策
意图定义：用户咨询贷款政策，包括全款、分期、贷款等
相似问法：
反例：

意图名称：问颜色
意图定义：用户询问可选车身颜色、内饰颜色或个性化配色方案
相似问法：
反例：

意图名称：看车型
意图定义：用户询问具体车型的型号、版本或希望了解不同车型的特点
相似问法：介绍下、
反例：

意图名称：问价格
意图定义：用户询问价格、裸车价、落地价、分期价格等
相似问法：高配的加4000、[车系]也要快[数字]了、[城市]便宜啊
反例：

意图名称：想卖车
意图定义：用户表达卖车意向，咨询收车、车辆回收、评估、检测、收购价格或卖车流程
相似问法：收车吗、能上门检测吗、
反例：

意图名称：问置换
意图定义：用户询问旧车置换新车的政策、本品置换、非本品置换、流程、补贴或估值计算方式
相似问法：没有置换、无置换、[xxx]的旧车
反例：

意图名称：问配置
意图定义：用户询问具体车型的参数、配置、座椅、功能亮点或不同配置版本差异
相似问法：安全性怎样、续航多少、原车音响、
反例：

意图名称：问试乘试驾
意图定义：用户询问试驾相关问题，包括是否可以上门试驾
相似问法：可以来接吗、
反例：

意图名称：问库存车
意图定义：用户询问车辆是否库存车
相似问法：
反例：

意图名称：问政策
意图定义：用户咨询购车相关政策，如本地牌照政策、补贴政策、保养政策、限行政策等
相似问法：免费保养吗、包牌，包税，包保险、有售后服务吗、
反例：

意图名称：问联系方式
意图定义：用户询问4S店的电话、微信、在线客服等联系方式以便进一步沟通
相似问法：
反例：

意图名称：撩妹
意图定义：用户发送与汽车销售无关的调侃、搭讪等内容，无明确购车咨询意图
相似问法：
反例：

</意图列表>

识别规则：
1. 仔细分析弹幕文本的核心意图
2. 优先匹配最直接相关的意图
3. 如果一条弹幕包含多个意图，选择最主要的一个
4. 未命中任何意图时返回：None
5. 只返回意图名称，不要解释，不要修改意图枚举值的名称

<input>
客户多轮对话格式：
<context>
{history}
</context>
<current>
{live_comment}
</current>
</input>

输出格式：直接返回意图名称，如：问优惠
"
    variables: ["history","live_comment"]
    description: "用于识别意图的模板"