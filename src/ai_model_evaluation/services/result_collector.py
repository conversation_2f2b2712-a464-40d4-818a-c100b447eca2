"""
Result collection and processing service.
"""

import time
from typing import Dict, List, Optional, Tuple

from ..models.core import ResultRow, APIResponse, ModelConfig, EvaluationRow
from ..utils.helpers import format_duration


class ResultCollector:
    """Collects and processes evaluation results."""
    
    def __init__(self):
        """Initialize result collector."""
        self.results: List[ResultRow] = []
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
    
    def start_collection(self) -> None:
        """Start result collection timing."""
        self.start_time = time.time()
        self.results.clear()
    
    def end_collection(self) -> None:
        """End result collection timing."""
        self.end_time = time.time()
    
    def add_result(self, evaluation_row: EvaluationRow, model_id: str, 
                  api_response: APIResponse) -> None:
        """
        Add a single result.
        
        Args:
            evaluation_row: Original evaluation data
            model_id: Model ID that generated the response
            api_response: API response from the model
        """
        # Find existing result row or create new one
        result_row = self._find_or_create_result_row(evaluation_row)
        
        # Add model result
        result_row.add_result(
            model_id=model_id,
            result=api_response.content,
            execution_time=api_response.execution_time,
            error=api_response.error_message if not api_response.success else None
        )
    
    def add_batch_results(self, evaluation_rows: List[EvaluationRow], 
                         model_id: str, api_responses: List[APIResponse]) -> None:
        """
        Add batch results for a single model.
        
        Args:
            evaluation_rows: List of evaluation data rows
            model_id: Model ID that generated the responses
            api_responses: List of API responses from the model
        """
        if len(evaluation_rows) != len(api_responses):
            raise ValueError("Number of evaluation rows must match number of API responses")
        
        for evaluation_row, api_response in zip(evaluation_rows, api_responses):
            self.add_result(evaluation_row, model_id, api_response)
    
    def add_error_for_model(self, evaluation_rows: List[EvaluationRow], 
                           model_id: str, error_message: str) -> None:
        """
        Add error results for all evaluation rows for a specific model.
        
        Args:
            evaluation_rows: List of evaluation data rows
            model_id: Model ID that failed
            error_message: Error message
        """
        for evaluation_row in evaluation_rows:
            result_row = self._find_or_create_result_row(evaluation_row)
            result_row.add_result(
                model_id=model_id,
                result="",
                execution_time=0.0,
                error=error_message
            )
    
    def get_results(self) -> List[ResultRow]:
        """Get all collected results."""
        return self.results.copy()
    
    def get_results_summary(self) -> Dict[str, any]:
        """
        Get summary of collected results.
        
        Returns:
            Dictionary with result statistics
        """
        if not self.results:
            return {
                'total_rows': 0,
                'total_models': 0,
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'success_rate': 0.0,
                'total_execution_time': 0.0,
                'average_execution_time': 0.0,
                'collection_duration': 0.0
            }
        
        # Get all model IDs
        all_model_ids = set()
        for result in self.results:
            all_model_ids.update(result.model_results.keys())
        
        # Calculate statistics
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        total_execution_time = 0.0
        
        for result in self.results:
            for model_id in all_model_ids:
                if model_id in result.model_results:
                    total_requests += 1
                    
                    if result.error_info.get(model_id) is None:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                    
                    total_execution_time += result.execution_time.get(model_id, 0.0)
        
        success_rate = successful_requests / total_requests if total_requests > 0 else 0.0
        average_execution_time = total_execution_time / total_requests if total_requests > 0 else 0.0
        
        collection_duration = 0.0
        if self.start_time and self.end_time:
            collection_duration = self.end_time - self.start_time
        
        return {
            'total_rows': len(self.results),
            'total_models': len(all_model_ids),
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'success_rate': success_rate,
            'total_execution_time': total_execution_time,
            'average_execution_time': average_execution_time,
            'collection_duration': collection_duration
        }
    
    def get_model_statistics(self) -> Dict[str, Dict[str, any]]:
        """
        Get statistics for each model.
        
        Returns:
            Dictionary mapping model IDs to their statistics
        """
        if not self.results:
            return {}
        
        # Get all model IDs
        all_model_ids = set()
        for result in self.results:
            all_model_ids.update(result.model_results.keys())
        
        model_stats = {}
        
        for model_id in all_model_ids:
            total_requests = 0
            successful_requests = 0
            failed_requests = 0
            execution_times = []
            response_lengths = []
            errors = []
            
            for result in self.results:
                if model_id in result.model_results:
                    total_requests += 1
                    
                    error = result.error_info.get(model_id)
                    if error is None:
                        successful_requests += 1
                        response_content = result.model_results[model_id]
                        response_lengths.append(len(response_content))
                    else:
                        failed_requests += 1
                        errors.append(error)
                    
                    exec_time = result.execution_time.get(model_id, 0.0)
                    if exec_time > 0:
                        execution_times.append(exec_time)
            
            success_rate = successful_requests / total_requests if total_requests > 0 else 0.0
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0.0
            avg_response_length = sum(response_lengths) / len(response_lengths) if response_lengths else 0.0
            
            model_stats[model_id] = {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': success_rate,
                'average_execution_time': avg_execution_time,
                'average_response_length': avg_response_length,
                'min_execution_time': min(execution_times) if execution_times else 0.0,
                'max_execution_time': max(execution_times) if execution_times else 0.0,
                'unique_errors': list(set(errors)),
                'error_count': len(errors)
            }
        
        return model_stats
    
    def get_failed_requests(self) -> List[Dict[str, any]]:
        """
        Get details of all failed requests.
        
        Returns:
            List of failed request details
        """
        failed_requests = []
        
        for i, result in enumerate(self.results):
            for model_id, error in result.error_info.items():
                if error is not None:
                    failed_requests.append({
                        'row_index': i + 1,
                        'model_id': model_id,
                        'original_prompt': result.original_prompt,
                        'variable_a': result.variable_a,
                        'variable_b': result.variable_b,
                        'error_message': error,
                        'execution_time': result.execution_time.get(model_id, 0.0)
                    })
        
        return failed_requests
    
    def get_performance_metrics(self) -> Dict[str, any]:
        """
        Get performance metrics for the evaluation.
        
        Returns:
            Dictionary with performance metrics
        """
        model_stats = self.get_model_statistics()
        summary = self.get_results_summary()
        
        if not model_stats:
            return {
                'fastest_model': None,
                'slowest_model': None,
                'most_reliable_model': None,
                'least_reliable_model': None,
                'average_response_time': 0.0,
                'total_evaluation_time': 0.0
            }
        
        # Find fastest and slowest models
        fastest_model = min(model_stats.items(), key=lambda x: x[1]['average_execution_time'])
        slowest_model = max(model_stats.items(), key=lambda x: x[1]['average_execution_time'])
        
        # Find most and least reliable models
        most_reliable = max(model_stats.items(), key=lambda x: x[1]['success_rate'])
        least_reliable = min(model_stats.items(), key=lambda x: x[1]['success_rate'])
        
        return {
            'fastest_model': {
                'model_id': fastest_model[0],
                'average_time': fastest_model[1]['average_execution_time']
            },
            'slowest_model': {
                'model_id': slowest_model[0],
                'average_time': slowest_model[1]['average_execution_time']
            },
            'most_reliable_model': {
                'model_id': most_reliable[0],
                'success_rate': most_reliable[1]['success_rate']
            },
            'least_reliable_model': {
                'model_id': least_reliable[0],
                'success_rate': least_reliable[1]['success_rate']
            },
            'average_response_time': summary['average_execution_time'],
            'total_evaluation_time': summary['collection_duration']
        }
    
    def export_detailed_report(self) -> Dict[str, any]:
        """
        Export a detailed report of all results.
        
        Returns:
            Comprehensive report dictionary
        """
        return {
            'summary': self.get_results_summary(),
            'model_statistics': self.get_model_statistics(),
            'performance_metrics': self.get_performance_metrics(),
            'failed_requests': self.get_failed_requests(),
            'collection_info': {
                'start_time': self.start_time,
                'end_time': self.end_time,
                'duration': self.end_time - self.start_time if self.start_time and self.end_time else 0.0,
                'duration_formatted': format_duration(self.end_time - self.start_time) if self.start_time and self.end_time else "0s"
            }
        }
    
    def _find_or_create_result_row(self, evaluation_row: EvaluationRow) -> ResultRow:
        """Find existing result row or create new one."""
        # Try to find existing result row
        for result in self.results:
            if (result.original_prompt == evaluation_row.original_prompt and
                result.variable_a == evaluation_row.variable_a and
                result.variable_b == evaluation_row.variable_b):
                return result
        
        # Create new result row
        result_row = ResultRow(
            original_prompt=evaluation_row.original_prompt,
            variable_a=evaluation_row.variable_a,
            variable_b=evaluation_row.variable_b,
            expected_result=evaluation_row.expected_result
        )
        self.results.append(result_row)
        return result_row
    
    def clear_results(self) -> None:
        """Clear all collected results."""
        self.results.clear()
        self.start_time = None
        self.end_time = None
    
    def merge_results(self, other_collector: 'ResultCollector') -> None:
        """
        Merge results from another collector.
        
        Args:
            other_collector: Another ResultCollector to merge from
        """
        for other_result in other_collector.results:
            # Find matching result row or create new one
            matching_result = None
            for result in self.results:
                if (result.original_prompt == other_result.original_prompt and
                    result.variable_a == other_result.variable_a and
                    result.variable_b == other_result.variable_b):
                    matching_result = result
                    break
            
            if matching_result is None:
                # Create new result row
                matching_result = ResultRow(
                    original_prompt=other_result.original_prompt,
                    variable_a=other_result.variable_a,
                    variable_b=other_result.variable_b,
                    expected_result=other_result.expected_result
                )
                self.results.append(matching_result)
            
            # Merge model results
            for model_id, result_content in other_result.model_results.items():
                matching_result.model_results[model_id] = result_content
                matching_result.execution_time[model_id] = other_result.execution_time.get(model_id, 0.0)
                matching_result.error_info[model_id] = other_result.error_info.get(model_id)