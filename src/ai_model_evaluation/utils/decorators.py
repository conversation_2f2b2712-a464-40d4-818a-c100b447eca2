"""
Decorator utilities for the AI Model Evaluation System.
"""

import asyncio
import functools
from typing import Any, Callable, TypeVar

from .exceptions import APIError

F = TypeVar('F', bound=Callable[..., Any])


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
    """
    Decorator to retry async functions on failure.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff_factor: Multiplier for delay after each retry
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except APIError as e:
                    last_exception = e
                    if attempt == max_retries:
                        # Update retry count in the exception
                        e.retry_count = attempt
                        raise e
                    
                    # Calculate delay with exponential backoff
                    current_delay = delay * (backoff_factor ** attempt)
                    await asyncio.sleep(current_delay)
                except Exception as e:
                    # For non-API errors, don't retry
                    raise e
            
            # This should never be reached, but just in case
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator


def log_execution_time(logger=None):
    """
    Decorator to log function execution time.
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                if logger:
                    logger.info(f"{func.__name__} executed in {execution_time:.2f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                if logger:
                    logger.error(f"{func.__name__} failed after {execution_time:.2f}s: {e}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                if logger:
                    logger.info(f"{func.__name__} executed in {execution_time:.2f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                if logger:
                    logger.error(f"{func.__name__} failed after {execution_time:.2f}s: {e}")
                raise
        
        # Return appropriate wrapper based on whether function is async
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator