# 自动化AI模型评估脚本使用说明

## 功能特点

✨ **自动化评估脚本** `auto_evaluate.py` 提供以下功能：

- 🔧 **灵活配置**：支持配置输入文件路径、模板和模型列表
- 📁 **自动文件夹管理**：根据原始文件名和时间戳自动生成结果文件夹
- 📊 **自动分析报告**：评估完成后自动生成JSON和HTML分析报告
- 🔄 **批量评估**：支持一次性运行多个评估任务
- 📝 **详细日志**：记录完整的执行过程和错误信息

## 快速开始

### 1. 单个评估

编辑 `auto_evaluate.py` 文件中的配置：

```python
# 单个评估配置
SINGLE_EVALUATION = {
    "input_file": "yitu.xlsx",     # 输入文件路径
    "template": "yitu",            # 模板名称
    "models": ["qwen-plus"],       # 模型列表
    "output_name": None            # 输出名称（None为自动生成）
}

# 运行模式
RUN_MODE = "single"
```

运行评估：
```bash
poetry run python auto_evaluate.py
```

### 2. 批量评估

配置多个评估任务：

```python
# 批量评估配置
BATCH_EVALUATIONS = [
    {
        "input_file": "yitu.xlsx",
        "template": "yitu", 
        "models": ["qwen-plus"],
        "output_name": "yitu_qwen_only"
    },
    {
        "input_file": "yitu.xlsx",
        "template": "yitu",
        "models": ["qwen-plus", "ernie-4"],
        "output_name": "yitu_multi_models"
    },
    {
        "input_file": "another_data.xlsx",
        "template": "default",
        "models": ["gpt-4", "claude-3"],
        "output_name": "comparison_test"
    }
]

# 运行模式
RUN_MODE = "batch"
```

## 配置参数说明

### 评估配置参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `input_file` | str | 输入文件路径 | `"yitu.xlsx"` |
| `template` | str | 模板名称 | `"yitu"`, `"default"` |
| `models` | List[str] | 模型列表 | `["qwen-plus", "ernie-4"]` |
| `output_name` | str/None | 输出文件夹名称 | `"my_test"` 或 `None`（自动生成） |

### 支持的模型

根据你的 `config.yaml` 配置，支持的模型包括：
- `qwen-plus` - 通义千问Plus
- `ernie-4` - 文心一言4.0
- `gpt-4` - OpenAI GPT-4
- `claude-3` - Anthropic Claude-3
- 等等...

### 支持的模板

根据你的 `config.yaml` 配置，支持的模板包括：
- `default` - 默认模板
- `yitu` - 意图识别模板
- 等等...

## 输出文件结构

每次评估会在 `evaluation_results/` 目录下创建一个文件夹，包含：

```
evaluation_results/
└── yitu_20250804_155309/           # 自动生成的文件夹名
    ├── yitu_20250804_155309_results.csv    # 评估结果CSV
    ├── evaluation_info.json               # 评估配置信息
    ├── analysis_report.json              # 分析报告（JSON格式）
    ├── analysis_report.html              # 分析报告（HTML格式）
    └── execution.log                      # 执行日志
```

### 文件说明

1. **`*_results.csv`** - 主要结果文件
   - 包含原始数据列（如 `history`, `live_comment`）
   - 包含每个模型的结果列（如 `qwen-plus_result`）
   - 包含执行时间列（如 `qwen-plus_execution_time`）

2. **`evaluation_info.json`** - 评估元信息
   ```json
   {
     "status": "success",
     "input_file": "yitu.xlsx",
     "template": "yitu",
     "models": ["qwen-plus"],
     "output_dir": "...",
     "result_files": ["..."],
     "execution_time": "2025-08-04T15:53:46.232064"
   }
   ```

3. **`analysis_report.json`** - 详细分析数据
   - 模型性能统计
   - 成功率分析
   - 结果分布统计

4. **`analysis_report.html`** - 可视化分析报告
   - 美观的HTML格式
   - 包含图表和统计信息
   - 可直接在浏览器中查看

5. **`execution.log`** - 完整执行日志
   - 命令行输出
   - 错误信息
   - 调试信息

## 高级用法

### 自定义输出文件夹名称

```python
{
    "input_file": "my_data.xlsx",
    "template": "custom",
    "models": ["model1", "model2"],
    "output_name": "experiment_v1_20250804"  # 自定义名称
}
```

### 多文件批量评估

```python
BATCH_EVALUATIONS = [
    {
        "input_file": "dataset1.xlsx",
        "template": "template1",
        "models": ["qwen-plus"],
        "output_name": "dataset1_qwen"
    },
    {
        "input_file": "dataset2.csv",
        "template": "template2", 
        "models": ["ernie-4"],
        "output_name": "dataset2_ernie"
    }
]
```

### 模型对比评估

```python
{
    "input_file": "comparison_data.xlsx",
    "template": "default",
    "models": ["qwen-plus", "ernie-4", "gpt-4"],  # 多个模型对比
    "output_name": "model_comparison"
}
```

## 错误处理

脚本包含完善的错误处理机制：

- ✅ 文件不存在检查
- ✅ 模型配置验证
- ✅ 执行过程监控
- ✅ 详细错误日志
- ✅ 失败任务记录

失败的评估也会生成相应的日志和信息文件，便于问题排查。

## 注意事项

1. **确保环境配置正确**：运行前确保 poetry 环境已激活
2. **检查模型配置**：确保 `config.yaml` 中配置了相应的模型
3. **文件路径**：输入文件路径相对于项目根目录
4. **磁盘空间**：确保有足够空间存储结果文件
5. **网络连接**：某些模型需要网络连接到API服务

## 示例场景

### 场景1：单模型快速测试
```python
SINGLE_EVALUATION = {
    "input_file": "test_data.xlsx",
    "template": "yitu",
    "models": ["qwen-plus"],
    "output_name": "quick_test"
}
```

### 场景2：多模型性能对比
```python
SINGLE_EVALUATION = {
    "input_file": "benchmark_data.xlsx", 
    "template": "default",
    "models": ["qwen-plus", "ernie-4", "gpt-4"],
    "output_name": "performance_benchmark"
}
```

### 场景3：批量数据集评估
```python
BATCH_EVALUATIONS = [
    {"input_file": f"dataset_{i}.xlsx", "template": "yitu", "models": ["qwen-plus"], "output_name": f"batch_{i}"}
    for i in range(1, 6)  # 评估5个数据集
]
```

这个脚本让AI模型评估变得简单高效，支持各种使用场景！🚀
