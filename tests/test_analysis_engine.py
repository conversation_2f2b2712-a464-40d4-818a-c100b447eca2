"""
Tests for analysis engine.
"""

import tempfile
import os
from unittest.mock import patch

import pandas as pd
import pytest

from ai_model_evaluation.services.analysis import AnalysisEngine
from ai_model_evaluation.models.core import ResultRow
from ai_model_evaluation.utils.exceptions import FileProcessingError


class TestAnalysisEngine:
    """Test AnalysisEngine class."""
    
    def create_test_results(self):
        """Create test result rows."""
        return [
            ResultRow(
                original_prompt="Prompt 1",
                variable_a="A1",
                variable_b="B1",
                expected_result="Expected 1",
                model_results={"model1": "Response 1", "model2": "Response 2"},
                execution_time={"model1": 1.0, "model2": 2.0},
                error_info={"model1": None, "model2": None}
            ),
            ResultRow(
                original_prompt="Prompt 2",
                variable_a="A2",
                variable_b="B2",
                expected_result="Expected 2",
                model_results={"model1": "Response 3", "model2": ""},
                execution_time={"model1": 1.5, "model2": 0.5},
                error_info={"model1": None, "model2": "API Error"}
            ),
            ResultRow(
                original_prompt="Prompt 3",
                variable_a="A3",
                variable_b="B3",
                expected_result="Expected 3",
                model_results={"model1": "Response 5", "model2": "Response 6"},
                execution_time={"model1": 0.8, "model2": 1.8},
                error_info={"model1": None, "model2": None}
            )
        ]
    
    def create_test_results_file(self):
        """Create a test results CSV file."""
        data = {
            'original_prompt': ['Prompt 1', 'Prompt 2'],
            'variable_a': ['A1', 'A2'],
            'variable_b': ['B1', 'B2'],
            'expected_result': ['Expected 1', 'Expected 2'],
            'model1_result': ['Response 1', 'Response 3'],
            'model1_execution_time': [1.0, 1.5],
            'model2_result': ['Response 2', ''],
            'model2_execution_time': [2.0, 0.5],
            'model2_error': [None, 'API Error']
        }
        
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            return f.name
    
    def test_analyze_results_empty(self):
        """Test analyzing empty results."""
        engine = AnalysisEngine()
        
        analysis = engine.analyze_results([])
        
        assert analysis.total_rows == 0
        assert analysis.model_count == 0
        assert analysis.success_rates == {}
        assert analysis.average_execution_times == {}
        assert analysis.error_counts == {}
    
    def test_analyze_results_with_data(self):
        """Test analyzing results with data."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        analysis = engine.analyze_results(results)
        
        assert analysis.total_rows == 3
        assert analysis.model_count == 2
        
        # Check model1 metrics (all successful)
        assert analysis.success_rates["model1"] == 1.0  # 3/3 successful
        assert analysis.average_execution_times["model1"] == 1.1  # (1.0 + 1.5 + 0.8) / 3
        assert analysis.error_counts["model1"] == 0
        
        # Check model2 metrics (2/3 successful)
        assert analysis.success_rates["model2"] == 2/3  # 2/3 successful
        assert analysis.average_execution_times["model2"] == 1.4  # (2.0 + 0.5 + 1.8) / 3
        assert analysis.error_counts["model2"] == 1
    
    def test_compare_models(self):
        """Test model comparison."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        comparison = engine.compare_models(results)
        
        assert len(comparison.models) == 2
        assert "model1" in comparison.models
        assert "model2" in comparison.models
        
        # Check metrics
        assert "model1" in comparison.metrics
        assert "model2" in comparison.metrics
        
        model1_metrics = comparison.metrics["model1"]
        assert model1_metrics["success_rate"] == 1.0
        assert model1_metrics["total_requests"] == 3.0
        assert model1_metrics["successful_requests"] == 3.0
        assert model1_metrics["failed_requests"] == 0.0
        
        model2_metrics = comparison.metrics["model2"]
        assert model2_metrics["success_rate"] == 2/3
        assert model2_metrics["total_requests"] == 3.0
        assert model2_metrics["successful_requests"] == 2.0
        assert model2_metrics["failed_requests"] == 1.0
        
        # Check summary
        assert "Compared 2 models" in comparison.summary
        assert "Best success rate: model1" in comparison.summary
    
    def test_compare_models_specific_models(self):
        """Test model comparison with specific model IDs."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        comparison = engine.compare_models(results, model_ids=["model1"])
        
        assert len(comparison.models) == 1
        assert "model1" in comparison.models
        assert "model2" not in comparison.models
    
    def test_generate_statistics(self):
        """Test statistics generation."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        stats = engine.generate_statistics(results)
        
        assert stats.total_evaluations == 6  # 3 rows * 2 models
        assert stats.successful_evaluations == 5  # model1: 3, model2: 2
        assert stats.failed_evaluations == 1  # model2: 1
        
        # Check average response lengths
        assert "model1" in stats.average_response_length
        assert "model2" in stats.average_response_length
        
        # Check response time stats
        assert "model1" in stats.response_time_stats
        assert "model2" in stats.response_time_stats
        
        model1_time_stats = stats.response_time_stats["model1"]
        assert model1_time_stats["mean"] == 1.1  # (1.0 + 1.5 + 0.8) / 3
        assert model1_time_stats["min"] == 0.8
        assert model1_time_stats["max"] == 1.5
    
    def test_generate_statistics_empty(self):
        """Test statistics generation with empty results."""
        engine = AnalysisEngine()
        
        stats = engine.generate_statistics([])
        
        assert stats.total_evaluations == 0
        assert stats.successful_evaluations == 0
        assert stats.failed_evaluations == 0
        assert stats.average_response_length == {}
        assert stats.response_time_stats == {}
    
    def test_find_best_performing_model_success_rate(self):
        """Test finding best performing model by success rate."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        best_model = engine.find_best_performing_model(results, metric='success_rate')
        
        assert best_model is not None
        assert best_model[0] == "model1"  # model1 has 100% success rate
        assert best_model[1] == 1.0
    
    def test_find_best_performing_model_response_time(self):
        """Test finding best performing model by response time."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        best_model = engine.find_best_performing_model(results, metric='response_time')
        
        assert best_model is not None
        assert best_model[0] == "model1"  # model1 has lower average response time
        assert best_model[1] == 1.1  # (1.0 + 1.5 + 0.8) / 3
    
    def test_find_best_performing_model_empty(self):
        """Test finding best performing model with empty results."""
        engine = AnalysisEngine()
        
        best_model = engine.find_best_performing_model([], metric='success_rate')
        
        assert best_model is None
    
    def test_find_best_performing_model_invalid_metric(self):
        """Test finding best performing model with invalid metric."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        with pytest.raises(ValueError, match="Unsupported metric"):
            engine.find_best_performing_model(results, metric='invalid_metric')
    
    def test_identify_problematic_rows(self):
        """Test identifying problematic rows."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        # Use threshold of 0.3 (30% failure rate)
        problematic_rows = engine.identify_problematic_rows(results, threshold=0.3)
        
        # Only row 2 should be problematic (1 out of 2 models failed = 50% failure rate)
        assert len(problematic_rows) == 1
        
        problematic_row = problematic_rows[0]
        assert problematic_row['row_index'] == 2
        assert problematic_row['original_prompt'] == "Prompt 2"
        assert problematic_row['total_models'] == 2
        assert problematic_row['failed_models'] == 1
        assert problematic_row['failure_rate'] == 0.5
        assert "model2" in problematic_row['errors']
        assert problematic_row['errors']['model2'] == "API Error"
    
    def test_identify_problematic_rows_high_threshold(self):
        """Test identifying problematic rows with high threshold."""
        engine = AnalysisEngine()
        results = self.create_test_results()
        
        # Use threshold of 0.8 (80% failure rate)
        problematic_rows = engine.identify_problematic_rows(results, threshold=0.8)
        
        # No rows should be problematic with this high threshold
        assert len(problematic_rows) == 0
    
    def test_calculate_model_agreement(self):
        """Test calculating model agreement."""
        engine = AnalysisEngine()
        
        # Create results where models sometimes agree
        results = [
            ResultRow(
                original_prompt="Prompt 1",
                variable_a="A1",
                variable_b="B1",
                expected_result="Expected 1",
                model_results={"model1": "same response", "model2": "same response"},
                execution_time={"model1": 1.0, "model2": 1.0},
                error_info={"model1": None, "model2": None}
            ),
            ResultRow(
                original_prompt="Prompt 2",
                variable_a="A2",
                variable_b="B2",
                expected_result="Expected 2",
                model_results={"model1": "different response", "model2": "another response"},
                execution_time={"model1": 1.0, "model2": 1.0},
                error_info={"model1": None, "model2": None}
            )
        ]
        
        agreement = engine.calculate_model_agreement(results)
        
        assert "model1_vs_model2" in agreement
        assert agreement["model1_vs_model2"] == 0.5  # 1 out of 2 responses match
    
    def test_calculate_model_agreement_single_model(self):
        """Test calculating model agreement with single model."""
        engine = AnalysisEngine()
        
        results = [
            ResultRow(
                original_prompt="Prompt 1",
                variable_a="A1",
                variable_b="B1",
                expected_result="Expected 1",
                model_results={"model1": "response"},
                execution_time={"model1": 1.0},
                error_info={"model1": None}
            )
        ]
        
        agreement = engine.calculate_model_agreement(results)
        
        # Should be empty since we need at least 2 models
        assert agreement == {}
    
    def test_calculate_model_agreement_empty(self):
        """Test calculating model agreement with empty results."""
        engine = AnalysisEngine()
        
        agreement = engine.calculate_model_agreement([])
        
        assert agreement == {}
    
    def test_load_results_csv(self):
        """Test loading results from CSV file."""
        engine = AnalysisEngine()
        csv_path = self.create_test_results_file()
        
        try:
            results = engine.load_results(csv_path)
            
            assert len(results) == 2
            
            # Check first result
            result1 = results[0]
            assert result1.original_prompt == "Prompt 1"
            assert result1.variable_a == "A1"
            assert result1.variable_b == "B1"
            assert result1.expected_result == "Expected 1"
            assert result1.model_results["model1"] == "Response 1"
            assert result1.model_results["model2"] == "Response 2"
            assert result1.execution_time["model1"] == 1.0
            assert result1.execution_time["model2"] == 2.0
            assert result1.error_info["model1"] is None
            assert result1.error_info["model2"] is None
            
            # Check second result
            result2 = results[1]
            assert result2.original_prompt == "Prompt 2"
            assert result2.model_results["model1"] == "Response 3"
            assert result2.model_results["model2"] == ""
            assert result2.error_info["model1"] is None
            assert result2.error_info["model2"] == "API Error"
            
        finally:
            os.unlink(csv_path)
    
    def test_load_results_nonexistent_file(self):
        """Test loading results from nonexistent file."""
        engine = AnalysisEngine()
        
        with pytest.raises(FileProcessingError, match="Results file not found"):
            engine.load_results("/nonexistent/file.csv")
    
    def test_load_results_unsupported_format(self):
        """Test loading results from unsupported file format."""
        engine = AnalysisEngine()
        
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write(b"some text content")
            txt_path = f.name
        
        try:
            with pytest.raises(FileProcessingError, match="Unsupported file format"):
                engine.load_results(txt_path)
        finally:
            os.unlink(txt_path)
    
    def test_dataframe_to_result_rows(self):
        """Test converting DataFrame to ResultRow objects."""
        engine = AnalysisEngine()
        
        data = {
            'original_prompt': ['Prompt 1', 'Prompt 2'],
            'variable_a': ['A1', 'A2'],
            'variable_b': ['B1', 'B2'],
            'expected_result': ['Expected 1', 'Expected 2'],
            'model1_result': ['Response 1', 'Response 3'],
            'model1_execution_time': [1.0, 1.5],
            'model2_result': ['Response 2', ''],
            'model2_execution_time': [2.0, 0.5],
            'model2_error': [None, 'API Error']
        }
        
        df = pd.DataFrame(data)
        results = engine._dataframe_to_result_rows(df)
        
        assert len(results) == 2
        
        # Check first result
        result1 = results[0]
        assert result1.original_prompt == "Prompt 1"
        assert result1.model_results["model1"] == "Response 1"
        assert result1.model_results["model2"] == "Response 2"
        assert result1.execution_time["model1"] == 1.0
        assert result1.execution_time["model2"] == 2.0
        assert result1.error_info["model1"] is None
        assert result1.error_info["model2"] is None
        
        # Check second result
        result2 = results[1]
        assert result2.original_prompt == "Prompt 2"
        assert result2.model_results["model1"] == "Response 3"
        assert result2.model_results["model2"] == ""
        assert result2.execution_time["model1"] == 1.5
        assert result2.execution_time["model2"] == 0.5
        assert result2.error_info["model1"] is None
        assert result2.error_info["model2"] == "API Error"