# AI模型评估系统 - 环境变量配置示例
# 复制此文件为 .env 并填入实际的API密钥

# OpenAI API密钥
OPENAI_API_KEY=sk-your-openai-api-key-here

# 智谱AI API密钥
ZHIPU_API_KEY=your-zhipu-api-key-here

# 通义千问API密钥（已在config.yaml中直接配置）
# TONGYI_API_KEY=sk-118e3693f3e84b029e85b9c0d4412a84

# 混元AI API密钥（已在config.yaml中直接配置）
# HUNYUAN_API_KEY=sk-FMpOdgAhRkCwfaRFVQgWAlTYFhrEaZLUNsYZXOPgk6ouffCk

# 百度AI API密钥（已在config.yaml中直接配置）
# BAIDU_API_KEY=bce-v3/ALTAK-tFI7EaDNOz96ifvHt1Vn0/9c0f28d9b7c343cee89f2eaf3f1a5f52dc0dc405

# 火山AI API密钥（已在config.yaml中直接配置）
# HUOSHAN_API_KEY=a44bd908-ca9f-4c94-a208-7e9717a8b626

# 其他可选配置
# LOG_LEVEL=INFO
# MAX_CONCURRENT_REQUESTS=5
# REQUEST_TIMEOUT=30