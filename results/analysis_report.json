{"evaluation_summary": {"total_samples": 345, "timestamp": "2025-08-04T15:48:46.415415", "models_evaluated": ["qwen-plus", "ernie-4"]}, "model_performance": {"qwen-plus": {"successful_requests": 197, "success_rate": 0.5710144927536231, "accuracy": 0.7386363636363636, "status": "operational"}}, "intent_distribution": {"问价格": 40, "看车型": 32, "问配置": 25, "问优惠": 24, "问地址": 24, "要买车": 11, "问政策": 9, "问置换": 7, "撩妹": 7, "问试乘试驾": 6, "金融政策": 5, "问颜色": 4, "问库存车": 2, "问联系方式": 1}, "recommendations": ["Improve qwen-plus success rate for None responses", "Add more labeled test data for accuracy evaluation", "Optimize performance settings", "Consider adding more models for comparison"]}