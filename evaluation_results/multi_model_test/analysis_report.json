{"file_info": {"result_file": "/Users/<USER>/kiro/对比/ai-model-evaluation/evaluation_results/multi_model_test/multi_model_test_results.csv", "total_rows": 345, "columns": ["history", "live_comment", "expected_result", "glm-4.5_result", "glm-4.5_execution_time", "glm-4.5_error", "qwen-plus_result", "qwen-plus_execution_time"]}, "model_performance": {"glm-4.5": {"total_requests": 345, "successful_requests": 0, "success_rate": 0.0, "average_execution_time": 0.17520567299663156, "result_distribution": {}, "classification_metrics": {"accuracy": 0.0, "macro_avg": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0}, "weighted_avg": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0}, "per_class_metrics": {}, "confusion_matrix": {}, "total_samples": 345, "valid_predictions": 0, "note": "No valid predictions found"}}, "qwen-plus": {"total_requests": 345, "successful_requests": 198, "success_rate": 0.5739130434782609, "average_execution_time": 0.5103195584338645, "result_distribution": {"问价格": 38, "看车型": 32, "问地址": 26, "问配置": 26, "问优惠": 24, "问政策": 10, "要买车": 9, "问置换": 7, "撩妹": 7, "问试乘试驾": 6, "金融政策": 5, "问颜色": 4, "问库存车": 2, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 1, "问联系方式": 1}, "classification_metrics": {"accuracy": 0.7457627118644068, "macro_avg": {"precision": 0.6609155366546671, "recall": 0.7472307410812333, "f1_score": 0.7311093269601673}, "weighted_avg": {"precision": 0.768173050405917, "recall": 0.7457627118644068, "f1_score": 0.7466697754584095}, "per_class_metrics": {"想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 5, "tp": 5, "fp": 0, "fn": 0}, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 0, "tp": 0, "fp": 1, "fn": 0}, "看车型": {"precision": 0.7333333333333333, "recall": 0.6470588235294118, "f1_score": 0.6875, "support": 34, "tp": 22, "fp": 8, "fn": 12}, "要买车": {"precision": 0.5714285714285714, "recall": 0.6666666666666666, "f1_score": 0.6153846153846153, "support": 6, "tp": 4, "fp": 3, "fn": 2}, "金融政策": {"precision": 0.2, "recall": 0.5, "f1_score": 0.28571428571428575, "support": 2, "tp": 1, "fp": 4, "fn": 1}, "问价格": {"precision": 0.8648648648648649, "recall": 0.7804878048780488, "f1_score": 0.8205128205128206, "support": 41, "tp": 32, "fp": 5, "fn": 9}, "问优惠": {"precision": 0.5652173913043478, "recall": 0.9285714285714286, "f1_score": 0.7027027027027025, "support": 14, "tp": 13, "fp": 10, "fn": 1}, "问地址": {"precision": 0.8888888888888888, "recall": 1.0, "f1_score": 0.9411764705882353, "support": 16, "tp": 16, "fp": 2, "fn": 0}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.75, "recall": 0.46153846153846156, "f1_score": 0.5714285714285714, "support": 13, "tp": 6, "fp": 2, "fn": 7}, "问置换": {"precision": 0.5, "recall": 0.5, "f1_score": 0.5, "support": 6, "tp": 3, "fp": 3, "fn": 3}, "问联系方式": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问试乘试驾": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 5, "tp": 5, "fp": 0, "fn": 0}, "问配置": {"precision": 0.84, "recall": 0.7241379310344828, "f1_score": 0.7777777777777777, "support": 29, "tp": 21, "fp": 4, "fn": 8}, "问颜色": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 2, "tp": 2, "fp": 2, "fn": 0}}, "confusion_matrix": {"想卖车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"想卖车": 0, "撩妹": 5, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 22, "要买车": 1, "金融政策": 0, "问价格": 2, "问优惠": 2, "问地址": 1, "问库存车": 1, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 3, "问颜色": 1}, "要买车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 4, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}, "金融政策": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 3, "要买车": 1, "金融政策": 1, "问价格": 32, "问优惠": 4, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 13, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 16, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问库存车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 3, "问价格": 0, "问优惠": 2, "问地址": 1, "问库存车": 0, "问政策": 6, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 3, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 0}, "问联系方式": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 5, "问配置": 0, "问颜色": 0}, "问配置": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 1, "看车型": 5, "要买车": 1, "金融政策": 0, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 21, "问颜色": 0}, "问颜色": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中客户提到的\"2.0T\"和\"12W多\"，用户在讨论具体车型的版本和价格信息。根据意图定义，\"看车型\"指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 177, "valid_predictions": 177, "note": "Calculated on 177 valid samples out of 345 total"}}}}