"""
File handling service for reading and writing evaluation data.
"""

from pathlib import Path
from typing import List, Optional, Union

import pandas as pd

from ..models.core import EvaluationRow, ResultRow, ValidationResult
from ..utils.exceptions import FileProcessingError
from ..utils.helpers import get_file_extension, is_csv_file, is_excel_file, validate_file_path


class FileHandler:
    """Handles reading and writing of evaluation data files."""
    
    REQUIRED_COLUMNS = ['original_prompt', 'variable_a', 'variable_b']
    OPTIONAL_COLUMNS = ['expected_result']
    
    def read_evaluation_data(self, file_path: Union[str, Path]) -> List[EvaluationRow]:
        """
        Read evaluation data from CSV or Excel file.
        
        Args:
            file_path: Path to the input file
            
        Returns:
            List of evaluation rows
            
        Raises:
            FileProcessingError: If file cannot be read or has invalid format
        """
        file_path = Path(file_path)
        
        if not validate_file_path(file_path):
            raise FileProcessingError(f"File not found or not readable: {file_path}")
        
        try:
            # Read file based on extension
            if is_csv_file(file_path):
                df = pd.read_csv(file_path)
            elif is_excel_file(file_path):
                df = pd.read_excel(file_path)
            else:
                raise FileProcessingError(f"Unsupported file format: {get_file_extension(file_path)}")
            
            # Validate and process data
            validation_result = self._validate_dataframe(df)
            if not validation_result.is_valid:
                error_msg = "File validation failed:\n" + "\n".join(validation_result.errors)
                raise FileProcessingError(error_msg)
            
            return self._dataframe_to_evaluation_rows(df)
            
        except pd.errors.EmptyDataError:
            raise FileProcessingError("File is empty")
        except pd.errors.ParserError as e:
            raise FileProcessingError(f"Failed to parse file: {e}")
        except Exception as e:
            raise FileProcessingError(f"Failed to read file: {e}")
    
    def validate_file_format(self, file_path: Union[str, Path]) -> ValidationResult:
        """
        Validate file format and structure without fully loading data.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            Validation result with errors and warnings
        """
        result = ValidationResult(is_valid=True, row_count=0, column_names=[])
        file_path = Path(file_path)
        
        # Check if file exists and is readable
        if not validate_file_path(file_path):
            result.errors.append(f"File not found or not readable: {file_path}")
            result.is_valid = False
            return result
        
        # Check file extension
        if not (is_csv_file(file_path) or is_excel_file(file_path)):
            result.errors.append(f"Unsupported file format: {get_file_extension(file_path)}")
            result.is_valid = False
            return result
        
        try:
            # Read just the header and a few rows for validation
            if is_csv_file(file_path):
                df = pd.read_csv(file_path, nrows=5)
            else:
                df = pd.read_excel(file_path, nrows=5)
            
            # Validate structure
            validation_result = self._validate_dataframe(df, quick_check=True)
            result.errors.extend(validation_result.errors)
            result.warnings.extend(validation_result.warnings)
            result.is_valid = validation_result.is_valid
            result.column_names = df.columns.tolist()
            
            # Get actual row count
            if is_csv_file(file_path):
                full_df = pd.read_csv(file_path)
            else:
                full_df = pd.read_excel(file_path)
            result.row_count = len(full_df)
            
        except Exception as e:
            result.errors.append(f"Failed to validate file: {e}")
            result.is_valid = False
        
        return result
    
    def write_results(self, results: List[ResultRow], output_path: Union[str, Path]) -> bool:
        """
        Write evaluation results to CSV or Excel file.
        
        Args:
            results: List of result rows to write
            output_path: Path to output file
            
        Returns:
            True if successful, False otherwise
            
        Raises:
            FileProcessingError: If writing fails
        """
        if not results:
            raise FileProcessingError("No results to write")
        
        output_path = Path(output_path)
        
        try:
            # Convert results to DataFrame
            df = self._result_rows_to_dataframe(results)
            
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write based on file extension
            if is_csv_file(output_path):
                df.to_csv(output_path, index=False, encoding='utf-8')
            elif is_excel_file(output_path):
                df.to_excel(output_path, index=False, engine='openpyxl')
            else:
                # Default to CSV if no extension or unsupported extension
                output_path = output_path.with_suffix('.csv')
                df.to_csv(output_path, index=False, encoding='utf-8')
            
            return True
            
        except Exception as e:
            raise FileProcessingError(f"Failed to write results to {output_path}: {e}")
    
    def _validate_dataframe(self, df: pd.DataFrame, quick_check: bool = False) -> ValidationResult:
        """
        Validate DataFrame structure and content.
        
        Args:
            df: DataFrame to validate
            quick_check: If True, only check structure, not content
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True, row_count=len(df), column_names=df.columns.tolist())
        
        # Check if DataFrame is empty
        if df.empty:
            result.errors.append("File contains no data")
            result.is_valid = False
            return result
        
        # Check required columns
        missing_columns = []
        for col in self.REQUIRED_COLUMNS:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            result.errors.append(f"Missing required columns: {', '.join(missing_columns)}")
            result.is_valid = False
        
        # Check for extra columns (warnings only)
        expected_columns = set(self.REQUIRED_COLUMNS + self.OPTIONAL_COLUMNS)
        extra_columns = set(df.columns) - expected_columns
        if extra_columns:
            result.warnings.append(f"Extra columns found (will be ignored): {', '.join(extra_columns)}")
        
        if quick_check:
            return result
        
        # Check for empty required fields
        for col in self.REQUIRED_COLUMNS:
            if col in df.columns:
                empty_count = df[col].isna().sum() + (df[col] == '').sum()
                if empty_count > 0:
                    result.warnings.append(f"Column '{col}' has {empty_count} empty values")
        
        # Check data types (all should be strings or convertible to strings)
        for col in self.REQUIRED_COLUMNS:
            if col in df.columns:
                try:
                    df[col].astype(str)
                except Exception:
                    result.errors.append(f"Column '{col}' contains non-text data")
                    result.is_valid = False
        
        return result
    
    def _dataframe_to_evaluation_rows(self, df: pd.DataFrame) -> List[EvaluationRow]:
        """Convert DataFrame to list of EvaluationRow objects."""
        rows = []
        
        for _, row in df.iterrows():
            evaluation_row = EvaluationRow(
                original_prompt=str(row['original_prompt']) if pd.notna(row['original_prompt']) else '',
                variable_a=str(row['variable_a']) if pd.notna(row['variable_a']) else '',
                variable_b=str(row['variable_b']) if pd.notna(row['variable_b']) else '',
                expected_result=str(row['expected_result']) if 'expected_result' in row and pd.notna(row['expected_result']) else None
            )
            rows.append(evaluation_row)
        
        return rows
    
    def _result_rows_to_dataframe(self, results: List[ResultRow]) -> pd.DataFrame:
        """Convert list of ResultRow objects to DataFrame."""
        if not results:
            return pd.DataFrame()
        
        # Get all unique model IDs from results
        all_model_ids = set()
        for result in results:
            all_model_ids.update(result.model_results.keys())
        
        model_ids = sorted(all_model_ids)
        
        # Build DataFrame data
        data = []
        for result in results:
            row_data = {
                'original_prompt': result.original_prompt,
                'variable_a': result.variable_a,
                'variable_b': result.variable_b,
                'expected_result': result.expected_result
            }
            
            # Add model results
            for model_id in model_ids:
                row_data[f'{model_id}_result'] = result.model_results.get(model_id, '')
                row_data[f'{model_id}_execution_time'] = result.execution_time.get(model_id, 0.0)
                
                # Add error info if present
                error = result.error_info.get(model_id)
                if error:
                    row_data[f'{model_id}_error'] = error
            
            data.append(row_data)
        
        return pd.DataFrame(data)
    
    def export_analysis_report(self, analysis_data: dict, output_path: Union[str, Path]) -> bool:
        """
        Export analysis report to file.
        
        Args:
            analysis_data: Analysis data to export
            output_path: Path to output file
            
        Returns:
            True if successful
            
        Raises:
            FileProcessingError: If export fails
        """
        output_path = Path(output_path)
        
        try:
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if is_csv_file(output_path):
                self._export_analysis_to_csv(analysis_data, output_path)
            elif is_excel_file(output_path):
                self._export_analysis_to_excel(analysis_data, output_path)
            else:
                # Default to CSV
                output_path = output_path.with_suffix('.csv')
                self._export_analysis_to_csv(analysis_data, output_path)
            
            return True
            
        except Exception as e:
            raise FileProcessingError(f"Failed to export analysis report: {e}")
    
    def _export_analysis_to_csv(self, analysis_data: dict, output_path: Path) -> None:
        """Export analysis data to CSV format."""
        # Create summary data
        summary_data = []
        
        if 'summary' in analysis_data:
            summary = analysis_data['summary']
            summary_data.append(['Metric', 'Value'])
            summary_data.append(['Total Rows', summary.get('total_rows', 0)])
            summary_data.append(['Model Count', summary.get('model_count', 0)])
            summary_data.append(['Success Rate', f"{summary.get('overall_success_rate', 0):.2%}"])
        
        # Add model-specific metrics
        if 'model_metrics' in analysis_data:
            summary_data.append(['', ''])  # Empty row
            summary_data.append(['Model Metrics', ''])
            
            for model_id, metrics in analysis_data['model_metrics'].items():
                summary_data.append([f'{model_id} Success Rate', f"{metrics.get('success_rate', 0):.2%}"])
                summary_data.append([f'{model_id} Avg Time', f"{metrics.get('avg_execution_time', 0):.2f}s"])
        
        # Write to CSV
        df = pd.DataFrame(summary_data)
        df.to_csv(output_path, index=False, header=False, encoding='utf-8')
    
    def _export_analysis_to_excel(self, analysis_data: dict, output_path: Path) -> None:
        """Export analysis data to Excel format with multiple sheets."""
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # Summary sheet
            if 'summary' in analysis_data:
                summary = analysis_data['summary']
                summary_df = pd.DataFrame([
                    ['Total Rows', summary.get('total_rows', 0)],
                    ['Model Count', summary.get('model_count', 0)],
                    ['Overall Success Rate', f"{summary.get('overall_success_rate', 0):.2%}"]
                ], columns=['Metric', 'Value'])
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # Model metrics sheet
            if 'model_metrics' in analysis_data:
                metrics_data = []
                for model_id, metrics in analysis_data['model_metrics'].items():
                    metrics_data.append({
                        'Model ID': model_id,
                        'Success Rate': f"{metrics.get('success_rate', 0):.2%}",
                        'Average Execution Time': f"{metrics.get('avg_execution_time', 0):.2f}s",
                        'Total Requests': metrics.get('total_requests', 0),
                        'Successful Requests': metrics.get('successful_requests', 0),
                        'Failed Requests': metrics.get('failed_requests', 0)
                    })
                
                metrics_df = pd.DataFrame(metrics_data)
                metrics_df.to_excel(writer, sheet_name='Model Metrics', index=False)
            
            # Detailed results sheet (if available)
            if 'detailed_results' in analysis_data:
                detailed_df = pd.DataFrame(analysis_data['detailed_results'])
                detailed_df.to_excel(writer, sheet_name='Detailed Results', index=False)
    
    def create_results_summary(self, results: List[ResultRow]) -> dict:
        """
        Create a summary of evaluation results.
        
        Args:
            results: List of result rows
            
        Returns:
            Dictionary with summary statistics
        """
        if not results:
            return {'total_rows': 0, 'model_count': 0, 'model_metrics': {}}
        
        # Get all model IDs
        all_model_ids = set()
        for result in results:
            all_model_ids.update(result.model_results.keys())
        
        model_ids = sorted(all_model_ids)
        
        # Calculate metrics for each model
        model_metrics = {}
        for model_id in model_ids:
            total_requests = 0
            successful_requests = 0
            failed_requests = 0
            execution_times = []
            
            for result in results:
                if model_id in result.model_results:
                    total_requests += 1
                    
                    if result.error_info.get(model_id) is None:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                    
                    exec_time = result.execution_time.get(model_id, 0)
                    if exec_time > 0:
                        execution_times.append(exec_time)
            
            success_rate = successful_requests / total_requests if total_requests > 0 else 0
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
            
            model_metrics[model_id] = {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': success_rate,
                'avg_execution_time': avg_execution_time
            }
        
        # Calculate overall metrics
        total_rows = len(results)
        total_requests = sum(m['total_requests'] for m in model_metrics.values())
        total_successful = sum(m['successful_requests'] for m in model_metrics.values())
        overall_success_rate = total_successful / total_requests if total_requests > 0 else 0
        
        return {
            'total_rows': total_rows,
            'model_count': len(model_ids),
            'overall_success_rate': overall_success_rate,
            'model_metrics': model_metrics,
            'summary': {
                'total_rows': total_rows,
                'model_count': len(model_ids),
                'overall_success_rate': overall_success_rate
            }
        }

    def get_file_info(self, file_path: Union[str, Path]) -> dict:
        """
        Get basic information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
        """
        file_path = Path(file_path)
        
        info = {
            'path': str(file_path),
            'exists': file_path.exists(),
            'size': 0,
            'extension': get_file_extension(file_path),
            'is_supported': False,
            'row_count': 0,
            'column_count': 0
        }
        
        if not file_path.exists():
            return info
        
        info['size'] = file_path.stat().st_size
        info['is_supported'] = is_csv_file(file_path) or is_excel_file(file_path)
        
        if info['is_supported']:
            try:
                validation_result = self.validate_file_format(file_path)
                info['row_count'] = validation_result.row_count
                info['column_count'] = len(validation_result.column_names)
                info['columns'] = validation_result.column_names
                info['is_valid'] = validation_result.is_valid
                info['errors'] = validation_result.errors
                info['warnings'] = validation_result.warnings
            except Exception as e:
                info['error'] = str(e)
        
        return info