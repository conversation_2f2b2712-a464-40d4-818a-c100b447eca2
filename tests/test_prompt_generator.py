"""
Tests for prompt generator service.
"""

import pytest

from ai_model_evaluation.services.prompt_generator import PromptGenerator
from ai_model_evaluation.models.core import PromptTemplate, EvaluationRow
from ai_model_evaluation.utils.exceptions import TemplateError, ValidationError


class TestPromptGenerator:
    """Test PromptGenerator class."""
    
    def create_test_template(self):
        """Create a test template."""
        return PromptTemplate(
            id="test_template",
            name="Test Template",
            template="{original_prompt}\n\nOption A: {variable_a}\nOption B: {variable_b}",
            variables=["original_prompt", "variable_a", "variable_b"],
            description="Test template for evaluation"
        )
    
    def create_test_evaluation_rows(self):
        """Create test evaluation rows."""
        return [
            EvaluationRow(
                original_prompt="Compare these options",
                variable_a="Cloud hosting",
                variable_b="On-premise hosting",
                expected_result="Cloud is better"
            ),
            EvaluationRow(
                original_prompt="Which is faster?",
                variable_a="SSD storage",
                variable_b="HDD storage",
                expected_result="SSD is faster"
            ),
            EvaluationRow(
                original_prompt="Choose the best approach",
                variable_a="Agile methodology",
                variable_b="Waterfall methodology",
                expected_result="Depends on project"
            )
        ]
    
    def test_generate_prompt(self):
        """Test generating a single prompt."""
        generator = PromptGenerator()
        template = self.create_test_template()
        evaluation_row = self.create_test_evaluation_rows()[0]
        
        prompt = generator.generate_prompt(template, evaluation_row)
        
        expected = "Compare these options\n\nOption A: Cloud hosting\nOption B: On-premise hosting"
        assert prompt == expected
    
    def test_generate_prompts_batch(self):
        """Test generating multiple prompts."""
        generator = PromptGenerator()
        template = self.create_test_template()
        evaluation_rows = self.create_test_evaluation_rows()
        
        prompts = generator.generate_prompts_batch(template, evaluation_rows)
        
        assert len(prompts) == 3
        assert "Compare these options" in prompts[0]
        assert "Cloud hosting" in prompts[0]
        assert "Which is faster?" in prompts[1]
        assert "SSD storage" in prompts[1]
        assert "Choose the best approach" in prompts[2]
        assert "Agile methodology" in prompts[2]
    
    def test_generate_prompts_batch_with_error(self):
        """Test batch generation with template error."""
        generator = PromptGenerator()
        
        # Create template with missing variable
        invalid_template = PromptTemplate(
            id="invalid_template",
            name="Invalid Template",
            template="{original_prompt}\n\nMissing: {missing_variable}",
            variables=["original_prompt", "missing_variable"],
            description="Invalid template"
        )
        
        evaluation_rows = self.create_test_evaluation_rows()
        
        with pytest.raises(TemplateError, match="Failed to generate prompt for row"):
            generator.generate_prompts_batch(invalid_template, evaluation_rows)
    
    def test_validate_template_compatibility_valid(self):
        """Test template compatibility validation with valid template."""
        generator = PromptGenerator()
        template = self.create_test_template()
        evaluation_rows = self.create_test_evaluation_rows()
        
        errors = generator.validate_template_compatibility(template, evaluation_rows)
        
        assert len(errors) == 0
    
    def test_validate_template_compatibility_missing_fields(self):
        """Test template compatibility with missing fields."""
        generator = PromptGenerator()
        
        # Create template requiring field not in evaluation data
        template = PromptTemplate(
            id="missing_field_template",
            name="Missing Field Template",
            template="{original_prompt}\n\nMissing: {missing_field}",
            variables=["original_prompt", "missing_field"],
            description="Template requiring missing field"
        )
        
        evaluation_rows = self.create_test_evaluation_rows()
        
        errors = generator.validate_template_compatibility(template, evaluation_rows)
        
        assert len(errors) > 0
        assert any("not available in evaluation data" in error for error in errors)
    
    def test_validate_template_compatibility_empty_data(self):
        """Test template compatibility with empty evaluation data."""
        generator = PromptGenerator()
        template = self.create_test_template()
        
        errors = generator.validate_template_compatibility(template, [])
        
        assert len(errors) > 0
        assert any("No evaluation data provided" in error for error in errors)
    
    def test_validate_template_compatibility_empty_values(self):
        """Test template compatibility with empty values."""
        generator = PromptGenerator()
        template = self.create_test_template()
        
        # Create evaluation row with empty value
        evaluation_rows = [
            EvaluationRow(
                original_prompt="",  # Empty original_prompt
                variable_a="Value A",
                variable_b="Value B"
            )
        ]
        
        errors = generator.validate_template_compatibility(template, evaluation_rows)
        
        assert len(errors) > 0
        assert any("empty value" in error for error in errors)
    
    def test_preview_prompts(self):
        """Test generating prompt previews."""
        generator = PromptGenerator()
        template = self.create_test_template()
        evaluation_rows = self.create_test_evaluation_rows()
        
        previews = generator.preview_prompts(template, evaluation_rows, max_previews=2)
        
        assert len(previews) == 2
        
        # Check first preview
        preview1 = previews[0]
        assert preview1['row_index'] == 1
        assert preview1['original_prompt'] == "Compare these options"
        assert preview1['variable_a'] == "Cloud hosting"
        assert preview1['variable_b'] == "On-premise hosting"
        assert "Compare these options" in preview1['generated_prompt']
        assert 'error' not in preview1
        
        # Check second preview
        preview2 = previews[1]
        assert preview2['row_index'] == 2
        assert preview2['original_prompt'] == "Which is faster?"
        assert "Which is faster?" in preview2['generated_prompt']
    
    def test_preview_prompts_with_error(self):
        """Test preview generation with template error."""
        generator = PromptGenerator()
        
        # Create invalid template
        invalid_template = PromptTemplate(
            id="invalid",
            name="Invalid",
            template="{original_prompt}\n\nMissing: {missing_field}",
            variables=["original_prompt", "missing_field"],
            description="Invalid template"
        )
        
        evaluation_rows = self.create_test_evaluation_rows()
        
        previews = generator.preview_prompts(invalid_template, evaluation_rows, max_previews=1)
        
        assert len(previews) == 1
        preview = previews[0]
        assert 'error' in preview
        assert "ERROR:" in preview['generated_prompt']
    
    def test_get_template_usage_stats(self):
        """Test getting template usage statistics."""
        generator = PromptGenerator()
        template = self.create_test_template()
        evaluation_rows = self.create_test_evaluation_rows()
        
        stats = generator.get_template_usage_stats(template, evaluation_rows)
        
        assert stats['template_id'] == "test_template"
        assert stats['total_rows'] == 3
        assert stats['compatible_rows'] == 3
        assert stats['compatibility_rate'] == 1.0
        assert stats['average_prompt_length'] > 0
        
        # Check variable usage
        assert 'original_prompt' in stats['variable_usage']
        assert 'variable_a' in stats['variable_usage']
        assert 'variable_b' in stats['variable_usage']
        
        # All variables should have 0 empty count
        for var_usage in stats['variable_usage'].values():
            assert var_usage['empty_count'] == 0
            assert var_usage['average_length'] > 0
    
    def test_get_template_usage_stats_empty_data(self):
        """Test usage statistics with empty data."""
        generator = PromptGenerator()
        template = self.create_test_template()
        
        stats = generator.get_template_usage_stats(template, [])
        
        assert stats['template_id'] == "test_template"
        assert stats['total_rows'] == 0
        assert stats['compatible_rows'] == 0
        assert stats['compatibility_rate'] == 0.0
        assert stats['average_prompt_length'] == 0
    
    def test_get_template_usage_stats_with_empty_values(self):
        """Test usage statistics with empty values."""
        generator = PromptGenerator()
        template = self.create_test_template()
        
        # Create evaluation rows with some empty values
        evaluation_rows = [
            EvaluationRow(
                original_prompt="Valid prompt",
                variable_a="",  # Empty
                variable_b="Valid B"
            ),
            EvaluationRow(
                original_prompt="Another prompt",
                variable_a="Valid A",
                variable_b="Valid B"
            )
        ]
        
        stats = generator.get_template_usage_stats(template, evaluation_rows)
        
        assert stats['total_rows'] == 2
        assert stats['variable_usage']['variable_a']['empty_count'] == 1
        assert stats['variable_usage']['variable_b']['empty_count'] == 0
    
    def test_suggest_template_improvements(self):
        """Test template improvement suggestions."""
        generator = PromptGenerator()
        template = self.create_test_template()
        evaluation_rows = self.create_test_evaluation_rows()
        
        suggestions = generator.suggest_template_improvements(template, evaluation_rows)
        
        # Should have some suggestions (at least basic ones)
        assert isinstance(suggestions, list)
        # With good compatibility, should have fewer suggestions
        compatibility_issues = [s for s in suggestions if "compatible" in s.lower()]
        assert len(compatibility_issues) == 0  # Should be fully compatible
    
    def test_suggest_template_improvements_with_issues(self):
        """Test improvement suggestions with problematic data."""
        generator = PromptGenerator()
        template = self.create_test_template()
        
        # Create evaluation rows with empty values
        evaluation_rows = [
            EvaluationRow(
                original_prompt="Valid prompt",
                variable_a="",  # Empty - will cause issues
                variable_b="Valid B"
            )
        ]
        
        suggestions = generator.suggest_template_improvements(template, evaluation_rows)
        
        assert len(suggestions) > 0
        # Should suggest issues with empty variables
        empty_var_suggestions = [s for s in suggestions if "empty" in s.lower()]
        assert len(empty_var_suggestions) > 0
    
    def test_create_optimized_template(self):
        """Test creating optimized template."""
        generator = PromptGenerator()
        evaluation_rows = self.create_test_evaluation_rows()
        
        template = generator.create_optimized_template(
            template_id="optimized_template",
            name="Optimized Template",
            evaluation_rows=evaluation_rows
        )
        
        assert template.id == "optimized_template"
        assert template.name == "Optimized Template"
        assert "original_prompt" in template.variables
        assert "variable_a" in template.variables
        assert "variable_b" in template.variables
        
        # Should be compatible with evaluation data
        errors = generator.validate_template_compatibility(template, evaluation_rows)
        assert len(errors) == 0
    
    def test_create_optimized_template_with_base(self):
        """Test creating optimized template with base template."""
        generator = PromptGenerator()
        evaluation_rows = self.create_test_evaluation_rows()
        
        base_template = "Custom template: {original_prompt} - A: {variable_a}, B: {variable_b}"
        
        template = generator.create_optimized_template(
            template_id="custom_optimized",
            name="Custom Optimized",
            evaluation_rows=evaluation_rows,
            base_template=base_template
        )
        
        assert template.template == base_template
        assert len(template.variables) == 3
    
    def test_create_optimized_template_empty_data(self):
        """Test creating optimized template with empty data."""
        generator = PromptGenerator()
        
        with pytest.raises(ValidationError, match="No evaluation data provided"):
            generator.create_optimized_template(
                template_id="empty_optimized",
                name="Empty Optimized",
                evaluation_rows=[]
            )
    
    def test_create_optimized_template_invalid_base(self):
        """Test creating optimized template with invalid base template."""
        generator = PromptGenerator()
        evaluation_rows = self.create_test_evaluation_rows()
        
        invalid_base = "Invalid template with {unclosed_brace"
        
        with pytest.raises(ValidationError, match="Failed to create optimized template"):
            generator.create_optimized_template(
                template_id="invalid_optimized",
                name="Invalid Optimized",
                evaluation_rows=evaluation_rows,
                base_template=invalid_base
            )