{"file_info": {"result_file": "/Users/<USER>/kiro/对比/ai-model-evaluation/evaluation_results/yitu_multi_models/yitu_multi_models_results.csv", "total_rows": 345, "columns": ["history", "live_comment", "expected_result", "ernie-4_result", "ernie-4_execution_time", "ernie-4_error", "qwen-plus_result", "qwen-plus_execution_time"]}, "model_performance": {"expected": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 107, "看车型": 51, "问价格": 45, "问配置": 35, "问政策": 20, "问地址": 17, "问优惠": 16, "问试乘试驾": 13, "要买车": 11, "撩妹": 10, "问置换": 8, "想卖车": 4, "问联系方式": 3, "金融政策": 2, "问颜色": 2, "问库存车": 1}}, "ernie-4": {"total_requests": 345, "successful_requests": 0, "success_rate": 0.0, "average_execution_time": 0.09751696379288378, "result_distribution": {}}, "qwen-plus": {"total_requests": 345, "successful_requests": 202, "success_rate": 0.5855072463768116, "average_execution_time": 0.4737377629763838, "result_distribution": {"问价格": 40, "看车型": 32, "问优惠": 27, "问地址": 26, "问配置": 25, "要买车": 11, "撩妹": 9, "问政策": 8, "问置换": 7, "问试乘试驾": 5, "问颜色": 5, "金融政策": 3, "问库存车": 2, "次顶配": 1, "问联系方式": 1}}}}