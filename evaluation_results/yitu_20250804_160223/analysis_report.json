{"file_info": {"result_file": "/Users/<USER>/kiro/对比/ai-model-evaluation/evaluation_results/yitu_20250804_160223/yitu_20250804_160223_results.csv", "total_rows": 345, "columns": ["history", "live_comment", "expected_result", "qwen-plus_result", "qwen-plus_execution_time"]}, "model_performance": {"qwen-plus": {"total_requests": 345, "successful_requests": 196, "success_rate": 0.5681159420289855, "average_execution_time": 0.48571209907531737, "result_distribution": {"问价格": 43, "看车型": 30, "问优惠": 25, "问配置": 25, "问地址": 22, "要买车": 10, "问政策": 8, "撩妹": 7, "问置换": 6, "问试乘试驾": 6, "金融政策": 5, "问颜色": 5, "问库存车": 2, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 1, "问联系方式": 1}, "classification_metrics": {"accuracy": 0.7457627118644068, "macro_avg": {"precision": 0.669141923436041, "recall": 0.7469287352214181, "f1_score": 0.7331007875915226}, "weighted_avg": {"precision": 0.7742494738008197, "recall": 0.7457627118644068, "f1_score": 0.7472613925046292}, "per_class_metrics": {"想卖车": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 2, "tp": 0, "fp": 0, "fn": 2}, "撩妹": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 5, "tp": 5, "fp": 0, "fn": 0}, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "support": 0, "tp": 0, "fp": 1, "fn": 0}, "看车型": {"precision": 0.7857142857142857, "recall": 0.6666666666666666, "f1_score": 0.721311475409836, "support": 33, "tp": 22, "fp": 6, "fn": 11}, "要买车": {"precision": 0.5714285714285714, "recall": 0.6666666666666666, "f1_score": 0.6153846153846153, "support": 6, "tp": 4, "fp": 3, "fn": 2}, "金融政策": {"precision": 0.2, "recall": 0.5, "f1_score": 0.28571428571428575, "support": 2, "tp": 1, "fp": 4, "fn": 1}, "问价格": {"precision": 0.8, "recall": 0.7804878048780488, "f1_score": 0.7901234567901235, "support": 41, "tp": 32, "fp": 8, "fn": 9}, "问优惠": {"precision": 0.5416666666666666, "recall": 0.9285714285714286, "f1_score": 0.6842105263157894, "support": 14, "tp": 13, "fp": 11, "fn": 1}, "问地址": {"precision": 0.9411764705882353, "recall": 1.0, "f1_score": 0.9696969696969697, "support": 16, "tp": 16, "fp": 1, "fn": 0}, "问库存车": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 1, "tp": 1, "fp": 1, "fn": 0}, "问政策": {"precision": 0.8571428571428571, "recall": 0.46153846153846156, "f1_score": 0.6, "support": 13, "tp": 6, "fp": 1, "fn": 7}, "问置换": {"precision": 0.5, "recall": 0.5, "f1_score": 0.5, "support": 6, "tp": 3, "fp": 3, "fn": 3}, "问联系方式": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 1, "tp": 1, "fp": 0, "fn": 0}, "问试乘试驾": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "support": 5, "tp": 5, "fp": 0, "fn": 0}, "问配置": {"precision": 0.84, "recall": 0.7, "f1_score": 0.7636363636363636, "support": 30, "tp": 21, "fp": 4, "fn": 9}, "问颜色": {"precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "support": 2, "tp": 2, "fp": 2, "fn": 0}}, "confusion_matrix": {"想卖车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "撩妹": {"想卖车": 0, "撩妹": 5, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "看车型": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 22, "要买车": 1, "金融政策": 0, "问价格": 2, "问优惠": 2, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 3, "问颜色": 1}, "要买车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 4, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 1, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 1}, "金融政策": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 1, "问价格": 1, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问价格": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 2, "要买车": 1, "金融政策": 1, "问价格": 32, "问优惠": 5, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问优惠": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 13, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问地址": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 16, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问库存车": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 1, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问政策": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 3, "问价格": 0, "问优惠": 2, "问地址": 1, "问库存车": 0, "问政策": 6, "问置换": 1, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问置换": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 1, "问优惠": 1, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 3, "问联系方式": 0, "问试乘试驾": 0, "问配置": 1, "问颜色": 0}, "问联系方式": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 1, "问试乘试驾": 0, "问配置": 0, "问颜色": 0}, "问试乘试驾": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 5, "问配置": 0, "问颜色": 0}, "问配置": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 1, "看车型": 4, "要买车": 1, "金融政策": 0, "问价格": 3, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 21, "问颜色": 0}, "问颜色": {"想卖车": 0, "撩妹": 0, "次顶配属于具体车型版本的描述，结合上下文中用户提到\"上次同事买的2.0T的\"以及\"12W多\"，整体是在围绕具体车型及其价格进行讨论。\"次顶配\"一词表达了对车型配置层级的关注，因此最符合的意图是用户希望了解具体车型的版本特点。\n\n输出：看车型": 0, "看车型": 0, "要买车": 0, "金融政策": 0, "问价格": 0, "问优惠": 0, "问地址": 0, "问库存车": 0, "问政策": 0, "问置换": 0, "问联系方式": 0, "问试乘试驾": 0, "问配置": 0, "问颜色": 2}}, "total_samples": 177, "valid_predictions": 177, "note": "Calculated on 177 valid samples out of 345 total"}}}}