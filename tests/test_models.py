"""
核心数据模型的测试。
"""

import pytest
from datetime import datetime

from ai_model_evaluation.models.core import (
    Provider,
    ModelConfig,
    PromptTemplate,
    EvaluationRow,
    APIRequest,
    APIResponse,
    TaskStatus,
)


class TestProvider:
    """测试Provider模型。"""
    
    def test_provider_creation(self):
        """测试基本的提供商创建。"""
        provider = Provider(
            id="test_provider",
            name="Test Provider",
            base_url="https://api.test.com/v1",
            api_key="test_key"
        )
        
        assert provider.id == "test_provider"
        assert provider.name == "Test Provider"
        assert provider.base_url == "https://api.test.com/v1/"  # 应该添加尾部斜杠
        assert provider.api_key == "test_key"
        assert provider.is_active is True
        assert isinstance(provider.created_at, datetime)
    
    def test_provider_invalid_url(self):
        """测试无效URL的提供商。"""
        with pytest.raises(ValueError, match="无效的base_url"):
            Provider(
                id="test",
                name="Test",
                base_url="invalid-url",
                api_key="key"
            )


class TestModelConfig:
    """测试ModelConfig模型。"""
    
    def test_model_config_creation(self):
        """Test basic model config creation."""
        model = ModelConfig(
            id="test_model",
            provider_id="test_provider",
            model_name="test-model",
            display_name="Test Model"
        )
        
        assert model.id == "test_model"
        assert model.provider_id == "test_provider"
        assert model.model_name == "test-model"
        assert model.display_name == "Test Model"
        assert model.temperature == 0.7
        assert model.thinking_enabled is False
        assert model.max_tokens is None
        assert model.other_params == {}
    
    def test_model_config_invalid_temperature(self):
        """Test model config with invalid temperature."""
        with pytest.raises(ValueError, match="Temperature must be between"):
            ModelConfig(
                id="test",
                provider_id="provider",
                model_name="model",
                display_name="Model",
                temperature=3.0
            )
    
    def test_model_config_invalid_max_tokens(self):
        """Test model config with invalid max_tokens."""
        with pytest.raises(ValueError, match="max_tokens must be positive"):
            ModelConfig(
                id="test",
                provider_id="provider",
                model_name="model",
                display_name="Model",
                max_tokens=-1
            )


class TestPromptTemplate:
    """Test PromptTemplate model."""
    
    def test_prompt_template_creation(self):
        """Test basic prompt template creation."""
        template = PromptTemplate(
            id="test_template",
            name="Test Template",
            template="Hello {name}, your age is {age}",
            variables=["name", "age"],
            description="Test template"
        )
        
        assert template.id == "test_template"
        assert template.name == "Test Template"
        assert template.template == "Hello {name}, your age is {age}"
        assert template.variables == ["name", "age"]
        assert template.description == "Test template"
        assert isinstance(template.created_at, datetime)
    
    def test_prompt_template_variable_mismatch(self):
        """Test prompt template with mismatched variables."""
        with pytest.raises(ValueError, match="Missing variables"):
            PromptTemplate(
                id="test",
                name="Test",
                template="Hello {name}, your age is {age}",
                variables=["name"],  # Missing 'age'
                description="Test"
            )
        
        with pytest.raises(ValueError, match="Extra variables"):
            PromptTemplate(
                id="test",
                name="Test",
                template="Hello {name}",
                variables=["name", "age"],  # Extra 'age'
                description="Test"
            )


class TestEvaluationRow:
    """Test EvaluationRow model."""
    
    def test_evaluation_row_creation(self):
        """Test basic evaluation row creation."""
        row = EvaluationRow(
            original_prompt="Test prompt",
            variable_a="Value A",
            variable_b="Value B",
            expected_result="Expected"
        )
        
        assert row.original_prompt == "Test prompt"
        assert row.variable_a == "Value A"
        assert row.variable_b == "Value B"
        assert row.expected_result == "Expected"
    
    def test_evaluation_row_to_dict(self):
        """Test conversion to dictionary."""
        row = EvaluationRow(
            original_prompt="Test prompt",
            variable_a="Value A",
            variable_b="Value B"
        )
        
        result = row.to_dict()
        expected = {
            'original_prompt': "Test prompt",
            'variable_a': "Value A",
            'variable_b': "Value B"
        }
        
        assert result == expected


class TestAPIRequest:
    """Test APIRequest model."""
    
    def test_api_request_to_openai_format(self):
        """Test conversion to OpenAI format."""
        model = ModelConfig(
            id="test_model",
            provider_id="test_provider",
            model_name="gpt-4",
            display_name="GPT-4",
            temperature=0.8,
            max_tokens=1000,
            thinking_enabled=True,
            other_params={
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.2
            }
        )
        
        request = APIRequest(
            model=model,
            prompt="Test prompt",
            request_id="test_request"
        )
        
        result = request.to_openai_format()
        
        assert result["model"] == "gpt-4"
        assert result["messages"] == [{"role": "user", "content": "Test prompt"}]
        assert result["temperature"] == 0.8
        assert result["max_tokens"] == 1000
        assert result["thinking"] is True
        assert result["top_p"] == 0.9
        assert result["frequency_penalty"] == 0.1
        assert result["presence_penalty"] == 0.2
    
    def test_api_request_validate_parameters_valid(self):
        """Test parameter validation with valid parameters."""
        model = ModelConfig(
            id="test_model",
            provider_id="test_provider",
            model_name="gpt-4",
            display_name="GPT-4",
            temperature=0.7,
            max_tokens=1000,
            other_params={
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": -0.1
            }
        )
        
        request = APIRequest(
            model=model,
            prompt="Valid prompt",
            request_id="test_request"
        )
        
        errors = request.validate_parameters()
        assert len(errors) == 0
    
    def test_api_request_validate_parameters_invalid(self):
        """Test parameter validation with invalid parameters."""
        model = ModelConfig(
            id="test_model",
            provider_id="test_provider",
            model_name="",  # Empty model name
            display_name="GPT-4",
            temperature=3.0,  # Invalid temperature
            max_tokens=-100,  # Invalid max_tokens
            other_params={
                "top_p": 1.5,  # Invalid top_p
                "frequency_penalty": 3.0,  # Invalid frequency_penalty
                "presence_penalty": -3.0  # Invalid presence_penalty
            }
        )
        
        request = APIRequest(
            model=model,
            prompt="",  # Empty prompt
            request_id="test_request"
        )
        
        errors = request.validate_parameters()
        assert len(errors) > 0
        
        error_text = " ".join(errors)
        assert "Prompt cannot be empty" in error_text
        assert "Model name cannot be empty" in error_text
        assert "Temperature must be between" in error_text
        assert "max_tokens must be positive" in error_text
        assert "top_p must be between" in error_text
        assert "frequency_penalty must be between" in error_text
        assert "presence_penalty must be between" in error_text


class TestAPIResponse:
    """Test APIResponse model."""
    
    def test_api_response_from_openai_success(self):
        """Test creating APIResponse from successful OpenAI response."""
        openai_response = {
            "choices": [
                {
                    "message": {
                        "content": "Test response content"
                    }
                }
            ]
        }
        
        response = APIResponse.from_openai_response(
            request_id="test_request",
            response_data=openai_response,
            execution_time=1.5
        )
        
        assert response.request_id == "test_request"
        assert response.content == "Test response content"
        assert response.success is True
        assert response.error_message is None
        assert response.execution_time == 1.5
    
    def test_api_response_from_openai_error(self):
        """Test creating APIResponse from invalid OpenAI response."""
        invalid_response = {"error": "Invalid request"}
        
        response = APIResponse.from_openai_response(
            request_id="test_request",
            response_data=invalid_response,
            execution_time=0.5
        )
        
        assert response.request_id == "test_request"
        assert response.content == ""
        assert response.success is False
        assert "Invalid response format" in response.error_message
        assert response.execution_time == 0.5