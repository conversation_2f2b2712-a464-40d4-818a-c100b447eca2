"""
Helper utility functions.
"""

import os
import uuid
from pathlib import Path
from typing import Union


def generate_id() -> str:
    """Generate a unique ID."""
    return str(uuid.uuid4())


def format_duration(seconds: float) -> str:
    """Format duration in seconds to human-readable string."""
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


def validate_file_path(file_path: Union[str, Path]) -> bool:
    """Validate if file path exists and is readable."""
    try:
        path = Path(file_path)
        return path.exists() and path.is_file() and os.access(path, os.R_OK)
    except (<PERSON><PERSON><PERSON><PERSON>, TypeError):
        return False


def ensure_directory(directory: Union[str, Path]) -> Path:
    """Ensure directory exists, create if it doesn't."""
    path = Path(directory)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_file_extension(file_path: Union[str, Path]) -> str:
    """Get file extension in lowercase."""
    return Path(file_path).suffix.lower()


def is_csv_file(file_path: Union[str, Path]) -> bool:
    """Check if file is a CSV file."""
    return get_file_extension(file_path) == '.csv'


def is_excel_file(file_path: Union[str, Path]) -> bool:
    """Check if file is an Excel file."""
    ext = get_file_extension(file_path)
    return ext in ['.xlsx', '.xls', '.xlsm']