#!/usr/bin/env python3
"""
生成意图识别评估分析报告
"""

import pandas as pd
import json
from datetime import datetime

def generate_analysis_report():
    # 读取结果文件 - 使用修复版的结果
    df = pd.read_csv('results_fixed/意图识别评估_修复版_results.csv')
    
    # 基本统计
    total_rows = len(df)
    qwen_success = (df['qwen-plus_result'].notna() & (df['qwen-plus_result'] != 'None')).sum()

    # qwen-plus结果分析
    qwen_results = df[(df['qwen-plus_result'].notna()) & (df['qwen-plus_result'] != 'None')]['qwen-plus_result']
    qwen_distribution = qwen_results.value_counts()

    # 准确率分析（与expected_result比较）
    valid_comparisons = df[(df['expected_result'].notna()) &
                          (df['expected_result'] != 'none') &
                          (df['qwen-plus_result'].notna()) &
                          (df['qwen-plus_result'] != 'None')]
    if len(valid_comparisons) > 0:
        accuracy = (valid_comparisons['expected_result'] == valid_comparisons['qwen-plus_result']).mean()
    else:
        accuracy = 0
    
    # 生成HTML报告
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>意图识别评估报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .stats {{ display: flex; gap: 20px; flex-wrap: wrap; }}
        .stat-card {{ background-color: #e9f5ff; padding: 15px; border-radius: 5px; min-width: 200px; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #0066cc; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .success {{ color: #28a745; }}
        .error {{ color: #dc3545; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>意图识别评估报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>评估概览</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_rows}</div>
                <div>总测试样本</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{qwen_success}</div>
                <div>qwen-plus成功数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{qwen_success/total_rows:.1%}</div>
                <div>qwen-plus成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{accuracy:.1%}</div>
                <div>准确率</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>模型表现</h2>
        <table>
            <tr>
                <th>模型</th>
                <th>成功请求数</th>
                <th>成功率</th>
                <th>状态</th>
            </tr>
            <tr>
                <td>qwen-plus (通义千问Plus)</td>
                <td>{qwen_success}/{total_rows}</td>
                <td>{qwen_success/total_rows:.1%}</td>
                <td class="success">正常运行</td>
            </tr>
            <tr>
                <td>ernie-4 (文心一言4.0)</td>
                <td>0/{total_rows}</td>
                <td>0.0%</td>
                <td class="error">未测试</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>意图识别结果分布 (qwen-plus)</h2>
        <table>
            <tr>
                <th>意图类别</th>
                <th>识别次数</th>
                <th>占比</th>
            </tr>
"""
    
    for intent, count in qwen_distribution.head(15).items():
        percentage = count / len(qwen_results) * 100
        html_content += f"""
            <tr>
                <td>{intent}</td>
                <td>{count}</td>
                <td>{percentage:.1f}%</td>
            </tr>
"""
    
    html_content += """
        </table>
    </div>
    
    <div class="section">
        <h2>问题分析</h2>
        <h3>数据质量分析</h3>
        <p>本次评估使用了修复后的系统，正确识别了模板变量 <code>history</code> 和 <code>live_comment</code>。</p>
        
        <h3>qwen-plus模型表现</h3>
        <p class="success">成功处理了{qwen_success/total_rows:.1%}的请求，表现良好。</p>
        <p>主要识别的意图类别包括：看车型、撩妹、问价格、问优惠等，符合汽车销售场景。</p>
    </div>
    
    <div class="section">
        <h2>建议</h2>
        <ol>
            <li><strong>处理None响应</strong>：分析qwen-plus返回None的情况，可能需要调整提示词或模型参数</li>
            <li><strong>准确率评估</strong>：增加更多带标签的测试数据来评估模型准确性</li>
            <li><strong>多模型对比</strong>：添加更多模型进行对比评估</li>
            <li><strong>性能优化</strong>：考虑调整请求超时和并发设置</li>
        </ol>
    </div>
</body>
</html>
"""
    
    # 保存HTML报告
    with open('results/analysis_report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    # 生成JSON报告
    json_report = {
        "evaluation_summary": {
            "total_samples": int(total_rows),
            "timestamp": datetime.now().isoformat(),
            "models_evaluated": ["qwen-plus", "ernie-4"]
        },
        "model_performance": {
            "qwen-plus": {
                "successful_requests": int(qwen_success),
                "success_rate": float(qwen_success / total_rows),
                "accuracy": float(accuracy),
                "status": "operational"
            }
        },
        "intent_distribution": {k: int(v) for k, v in qwen_distribution.to_dict().items()},
        "recommendations": [
            "Improve qwen-plus success rate for None responses",
            "Add more labeled test data for accuracy evaluation",
            "Optimize performance settings",
            "Consider adding more models for comparison"
        ]
    }
    
    with open('results/analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(json_report, f, ensure_ascii=False, indent=2)
    
    print("✅ 分析报告已生成:")
    print("   - HTML报告: results/analysis_report.html")
    print("   - JSON报告: results/analysis_report.json")
    print("   - 修复版结果: results_fixed/意图识别评估_修复版_results.csv")

if __name__ == "__main__":
    generate_analysis_report()
