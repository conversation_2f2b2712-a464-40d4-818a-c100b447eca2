history,live_comment,expected_result,glm-4.5_result,glm-4.5_execution_time,glm-4.5_error,qwen-plus_result,qwen-plus_execution_time
无历史消息,[尬笑],none,,0.9532759189605713,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6635] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4545879364013672
无历史消息,在安庆出差都给你点赞[灵机一动],none,,0.682988166809082,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6707] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3773338794708252
无历史消息,智跑介绍下,看车型,,0.6982038021087646,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.5027768611907959
"时间：2025-06-19 19:04:25
内容：在安庆出差都给你点赞[灵机一动]",主播要出个镜,撩妹,,0.6926531791687012,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6770] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.49808216094970703
"时间：2025-06-19 19:04:25
内容：在安庆出差都给你点赞[灵机一动]

时间：2025-06-19 19:07:08
内容：主播要出个镜",出镜粉丝就蹭蹭往上涨,撩妹,,0.6795797348022461,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6891] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.42600274085998535
无历史消息,狮铂1.5尊贵现金优惠多少？,问优惠,,0.1741042137145996,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6690] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.4781827926635742
"时间：2025-06-19 19:08:35
内容：狮铂1.5尊贵现金优惠多少？",没有,none,,0.17385601997375488,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6729] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.42937517166137695
"时间：2025-06-19 19:08:35
内容：狮铂1.5尊贵现金优惠多少？

时间：2025-06-19 19:09:12
内容：没有",宣城,问地址,,0.16742992401123047,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6802] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.5021848678588867
"时间：2025-06-19 19:08:35
内容：狮铂1.5尊贵现金优惠多少？

时间：2025-06-19 19:09:12
内容：没有

时间：2025-06-19 19:10:05
内容：宣城",没空啊,none,,0.20334172248840332,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6881] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.39476990699768066
无历史消息,上次同事买的2.0T的,看车型,,0.15311479568481445,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6667] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.39026403427124023
"时间：2025-06-19 19:13:23
内容：上次同事买的2.0T的",12W多,问价格,,0.15456891059875488,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6703] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4063107967376709
"时间：2025-06-19 19:13:23
内容：上次同事买的2.0T的

时间：2025-06-19 19:14:19
内容：12W多",次顶配,问配置,,0.156707763671875,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6782] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}","次顶配属于具体车型版本的描述，结合上下文中客户提到的""2.0T""和""12W多""，用户在讨论具体车型的版本和价格信息。根据意图定义，""看车型""指用户询问具体车型的型号、版本或希望了解不同车型的特点，因此最符合当前弹幕意图。

输出：看车型",3.499044179916382
无历史消息,1.5 12.5也不好买,问价格,,0.15921926498413086,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6653] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.40655517578125
"时间：2025-06-19 19:05:15
内容：智跑介绍下",适合女性开的车有推荐吗,看车型,,0.1497199535369873,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6744] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.36754512786865234
"时间：2025-06-19 19:13:23
内容：上次同事买的2.0T的

时间：2025-06-19 19:14:19
内容：12W多

时间：2025-06-19 19:14:43
内容：次顶配",2.0T的可以，有劲,问配置,,0.14325785636901855,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6883] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3408620357513428
"时间：2025-06-19 19:16:07
内容：1.5 12.5也不好买",无置换,问置换,,0.15314006805419922,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6698] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问置换,0.4402179718017578
"时间：2025-06-19 19:16:07
内容：1.5 12.5也不好买

时间：2025-06-19 19:16:28
内容：无置换",塞图斯也要快10了。没性价比,问价格,,0.14344000816345215,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6833] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.41013669967651367
无历史消息,赛图斯那台带全景天窗吗,问配置,,0.14365410804748535,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6687] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.3770132064819336
"时间：2025-06-19 19:16:07
内容：1.5 12.5也不好买

时间：2025-06-19 19:16:28
内容：无置换

时间：2025-06-19 19:17:06
内容：塞图斯也要快10了。没性价比",无置换现在报价9万大,问价格,,0.17695093154907227,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6949] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.49477314949035645
"时间：2025-06-19 19:16:07
内容：1.5 12.5也不好买

时间：2025-06-19 19:16:28
内容：无置换

时间：2025-06-19 19:17:06
内容：塞图斯也要快10了。没性价比

时间：2025-06-19 19:17:48
内容：无置换现在报价9万大",豪华也可以,看车型,,0.1466379165649414,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7040] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.8609387874603271
"时间：2025-06-19 19:16:07
内容：1.5 12.5也不好买

时间：2025-06-19 19:16:28
内容：无置换

时间：2025-06-19 19:17:06
内容：塞图斯也要快10了。没性价比

时间：2025-06-19 19:17:48
内容：无置换现在报价9万大

时间：2025-06-19 19:18:21
内容：豪华也可以",塞图斯,看车型,,0.15092802047729492,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7119] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.5307950973510742
"时间：2025-06-19 19:16:28
内容：无置换

时间：2025-06-19 19:17:06
内容：塞图斯也要快10了。没性价比

时间：2025-06-19 19:17:48
内容：无置换现在报价9万大

时间：2025-06-19 19:18:21
内容：豪华也可以

时间：2025-06-19 19:18:39
内容：塞图斯",落地,问价格,,0.15758275985717773,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7099] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.9125509262084961
"时间：2025-06-19 19:13:23
内容：上次同事买的2.0T的

时间：2025-06-19 19:14:19
内容：12W多

时间：2025-06-19 19:14:43
内容：次顶配

时间：2025-06-19 19:16:17
内容：2.0T的可以，有劲",K3现在什么价？次顶配的,问价格,,0.1562199592590332,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7006] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4141731262207031
"时间：2025-06-19 19:13:23
内容：上次同事买的2.0T的

时间：2025-06-19 19:14:19
内容：12W多

时间：2025-06-19 19:14:43
内容：次顶配

时间：2025-06-19 19:16:17
内容：2.0T的可以，有劲

时间：2025-06-19 19:20:49
内容：K3现在什么价？次顶配的",无旧车置换,问置换,,0.1521749496459961,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7097] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4989969730377197
无历史消息,K3旁边是什么Suⅴ？,看车型,,0.14864087104797363,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6667] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.5174801349639893
"时间：2025-06-19 19:24:23
内容：K3旁边是什么Suⅴ？",介绍下,看车型,,0.14369988441467285,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6712] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3776249885559082
无历史消息,EV5有现车吗？,看车型,,0.14595890045166016,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6654] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问库存车,0.3071310520172119
"时间：2025-06-19 19:24:23
内容：K3旁边是什么Suⅴ？

时间：2025-06-19 19:24:52
内容：介绍下",介绍下车长，宽,问配置,,0.1454768180847168,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6815] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.4739718437194824
"时间：2025-06-19 19:24:23
内容：K3旁边是什么Suⅴ？

时间：2025-06-19 19:24:52
内容：介绍下

时间：2025-06-19 19:26:25
内容：介绍下车长，宽",安全性怎样？,问配置,,0.14972782135009766,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6912] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.3897373676300049
"时间：2025-06-19 19:24:23
内容：K3旁边是什么Suⅴ？

时间：2025-06-19 19:24:52
内容：介绍下

时间：2025-06-19 19:26:25
内容：介绍下车长，宽

时间：2025-06-19 19:27:38
内容：安全性怎样？",有没有不带天窗的？,问配置,,0.15614891052246094,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7027] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.5365731716156006
"时间：2025-06-19 19:24:23
内容：K3旁边是什么Suⅴ？

时间：2025-06-19 19:24:52
内容：介绍下

时间：2025-06-19 19:26:25
内容：介绍下车长，宽

时间：2025-06-19 19:27:38
内容：安全性怎样？

时间：2025-06-19 19:29:17
内容：有没有不带天窗的？",才进来，报下价，全款,问价格,,0.15785884857177734,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7148] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.5187578201293945
"时间：2025-06-19 19:24:52
内容：介绍下

时间：2025-06-19 19:26:25
内容：介绍下车长，宽

时间：2025-06-19 19:27:38
内容：安全性怎样？

时间：2025-06-19 19:29:17
内容：有没有不带天窗的？

时间：2025-06-19 19:30:03
内容：才进来，报下价，全款",代下步,看车型,,0.1574850082397461,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7120] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.33159875869750977
"时间：2025-06-19 19:26:25
内容：介绍下车长，宽

时间：2025-06-19 19:27:38
内容：安全性怎样？

时间：2025-06-19 19:29:17
内容：有没有不带天窗的？

时间：2025-06-19 19:30:03
内容：才进来，报下价，全款

时间：2025-06-19 19:30:26
内容：代下步",全款,问价格,,0.15247106552124023,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7114] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.39818596839904785
"时间：2025-06-19 19:27:38
内容：安全性怎样？

时间：2025-06-19 19:29:17
内容：有没有不带天窗的？

时间：2025-06-19 19:30:03
内容：才进来，报下价，全款

时间：2025-06-19 19:30:26
内容：代下步

时间：2025-06-19 19:30:35
内容：全款",无,none,,0.14603304862976074,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7078] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.35537004470825195
"时间：2025-06-19 19:29:17
内容：有没有不带天窗的？

时间：2025-06-19 19:30:03
内容：才进来，报下价，全款

时间：2025-06-19 19:30:26
内容：代下步

时间：2025-06-19 19:30:35
内容：全款

时间：2025-06-19 19:30:54
内容：无",9个多包不包商业险？,金融政策,,0.15030598640441895,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7097] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.5008120536804199
"时间：2025-06-19 19:30:03
内容：才进来，报下价，全款

时间：2025-06-19 19:30:26
内容：代下步

时间：2025-06-19 19:30:35
内容：全款

时间：2025-06-19 19:30:54
内容：无

时间：2025-06-19 19:31:53
内容：9个多包不包商业险？",发动机正时是钢带还是钢链？,问配置,,0.15020990371704102,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7121] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,2.8388187885284424
"时间：2025-06-19 19:30:26
内容：代下步

时间：2025-06-19 19:30:35
内容：全款

时间：2025-06-19 19:30:54
内容：无

时间：2025-06-19 19:31:53
内容：9个多包不包商业险？

时间：2025-06-19 19:33:31
内容：发动机正时是钢带还是钢链？",5次是免费保养，还是？,问政策,,0.14881110191345215,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7122] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.6907000541687012
"时间：2025-06-19 19:30:35
内容：全款

时间：2025-06-19 19:30:54
内容：无

时间：2025-06-19 19:31:53
内容：9个多包不包商业险？

时间：2025-06-19 19:33:31
内容：发动机正时是钢带还是钢链？

时间：2025-06-19 19:34:57
内容：5次是免费保养，还是？",荆州的,问地址,,0.15584683418273926,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7122] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.6259157657623291
"时间：2025-06-19 19:30:54
内容：无

时间：2025-06-19 19:31:53
内容：9个多包不包商业险？

时间：2025-06-19 19:33:31
内容：发动机正时是钢带还是钢链？

时间：2025-06-19 19:34:57
内容：5次是免费保养，还是？

时间：2025-06-19 19:35:38
内容：荆州的",湖北荆州,问地址,,0.16079378128051758,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7134] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.42276501655578613
"时间：2025-06-19 19:05:15
内容：智跑介绍下

时间：2025-06-19 19:16:16
内容：适合女性开的车有推荐吗",你们位置在哪,问地址,,0.15802502632141113,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6841] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.3919029235839844
"时间：2025-06-19 19:31:53
内容：9个多包不包商业险？

时间：2025-06-19 19:33:31
内容：发动机正时是钢带还是钢链？

时间：2025-06-19 19:34:57
内容：5次是免费保养，还是？

时间：2025-06-19 19:35:38
内容：荆州的

时间：2025-06-19 19:36:03
内容：湖北荆州",可不可以5次全国免费保养，如可以，我搞一台[呲牙],要买车,,0.1602768898010254,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7263] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.42002201080322266
"时间：2025-06-19 19:33:31
内容：发动机正时是钢带还是钢链？

时间：2025-06-19 19:34:57
内容：5次是免费保养，还是？

时间：2025-06-19 19:35:38
内容：荆州的

时间：2025-06-19 19:36:03
内容：湖北荆州

时间：2025-06-19 19:37:15
内容：可不可以5次全国免费保养，如可以，我搞一台[呲牙]",好,none,,0.1580660343170166,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7214] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.42838406562805176
无历史消息,扶手箱下面小座椅干什么用的,问配置,,0.15029215812683105,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6699] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.3777790069580078
无历史消息,这是纯电吗,问配置,,0.14734101295471191,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.32293176651000977
"时间：2025-06-19 19:40:57
内容：这是纯电吗",视频车是纯电吗,问配置,,0.15717387199401855,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6720] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.376878023147583
"时间：2025-06-19 19:40:49
内容：扶手箱下面小座椅干什么用的",感觉有点丑[捂脸],none,,0.14949679374694824,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6770] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.35401225090026855
无历史消息,看看隔壁的那个车,看车型,,0.1550910472869873,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6669] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.45498204231262207
"时间：2025-06-19 19:40:49
内容：扶手箱下面小座椅干什么用的

时间：2025-06-19 19:41:55
内容：感觉有点丑[捂脸]",小座椅丑,问配置,,0.15914392471313477,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6855] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.33803319931030273
无历史消息,价格,问价格,,0.1552598476409912,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6633] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,1.6734910011291504
"时间：2025-06-19 19:42:14
内容：看看隔壁的那个车",狮铂拓界1.5的可有试驾车,问试乘试驾,,0.15195894241333008,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6759] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问试乘试驾,0.5500829219818115
无历史消息,我准备去上海提车了[呲牙],要买车,,0.1491239070892334,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6689] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.4483792781829834
"时间：2025-06-19 19:47:26
内容：我准备去上海提车了[呲牙]",上海便宜啊,问价格,,0.14591217041015625,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6746] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4311530590057373
"时间：2025-06-19 19:47:26
内容：我准备去上海提车了[呲牙]

时间：2025-06-19 19:47:49
内容：上海便宜啊",2.0尊贵那边多拿一个w啊,问价格,,0.14804291725158691,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6865] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.2807321548461914
"时间：2025-06-19 19:47:26
内容：我准备去上海提车了[呲牙]

时间：2025-06-19 19:47:49
内容：上海便宜啊

时间：2025-06-19 19:48:26
内容：2.0尊贵那边多拿一个w啊",沪c,问政策,,0.14265108108520508,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6933] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.37683796882629395
"时间：2025-06-19 19:47:26
内容：我准备去上海提车了[呲牙]

时间：2025-06-19 19:47:49
内容：上海便宜啊

时间：2025-06-19 19:48:26
内容：2.0尊贵那边多拿一个w啊

时间：2025-06-19 19:48:54
内容：沪c",过个半年在平移回合肥,none,,0.1440269947052002,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7054] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.9886860847473145
"时间：2025-06-19 19:47:26
内容：我准备去上海提车了[呲牙]

时间：2025-06-19 19:47:49
内容：上海便宜啊

时间：2025-06-19 19:48:26
内容：2.0尊贵那边多拿一个w啊

时间：2025-06-19 19:48:54
内容：沪c

时间：2025-06-19 19:49:18
内容：过个半年在平移回合肥",是的，保养还要来你这,问政策,,0.14627695083618164,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7175] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4240710735321045
"时间：2025-06-19 19:40:49
内容：扶手箱下面小座椅干什么用的

时间：2025-06-19 19:41:55
内容：感觉有点丑[捂脸]

时间：2025-06-19 19:42:24
内容：小座椅丑",智跑优惠多少,问优惠,,0.14830994606018066,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6952] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.3800499439239502
无历史消息,我关注智跑,看车型,,0.1514599323272705,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.2717151641845703
"时间：2025-06-19 19:50:22
内容：我关注智跑",有置换,问置换,,0.14005470275878906,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6696] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问置换,0.42646026611328125
"时间：2025-06-19 19:50:22
内容：我关注智跑

时间：2025-06-19 19:51:48
内容：有置换",起亚,看车型,,0.1775531768798828,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6769] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问置换,0.35572004318237305
"时间：2025-06-19 19:50:22
内容：我关注智跑

时间：2025-06-19 19:51:48
内容：有置换

时间：2025-06-19 19:52:24
内容：起亚",不是,none,,0.1401510238647461,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6842] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问置换,0.41921496391296387
"时间：2025-06-19 19:34:57
内容：5次是免费保养，还是？

时间：2025-06-19 19:35:38
内容：荆州的

时间：2025-06-19 19:36:03
内容：湖北荆州

时间：2025-06-19 19:37:15
内容：可不可以5次全国免费保养，如可以，我搞一台[呲牙]

时间：2025-06-19 19:37:58
内容：好",价格可以，可以订,问价格,,0.14393329620361328,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7184] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.386829137802124
"时间：2025-06-19 19:35:38
内容：荆州的

时间：2025-06-19 19:36:03
内容：湖北荆州

时间：2025-06-19 19:37:15
内容：可不可以5次全国免费保养，如可以，我搞一台[呲牙]

时间：2025-06-19 19:37:58
内容：好

时间：2025-06-19 19:59:50
内容：价格可以，可以订",刚才在懂车帝看了下，[呲牙],none,,0.14322400093078613,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7197] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.7930951118469238
"时间：2025-06-19 19:36:03
内容：湖北荆州

时间：2025-06-19 19:37:15
内容：可不可以5次全国免费保养，如可以，我搞一台[呲牙]

时间：2025-06-19 19:37:58
内容：好

时间：2025-06-19 19:59:50
内容：价格可以，可以订

时间：2025-06-19 20:01:03
内容：刚才在懂车帝看了下，[呲牙]",好[呲牙],none,,0.14670896530151367,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7199] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.9923818111419678
无历史消息,电车大车续航多少,问配置,,0.15152525901794434,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6669] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.3590102195739746
"时间：2025-06-24 10:54:56
内容：电车大车续航多少",多少,问配置,,0.17533230781555176,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6708] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.6464183330535889
"时间：2025-06-24 10:54:56
内容：电车大车续航多少

时间：2025-06-24 10:55:40
内容：多少",纯电有吗,看车型,,0.15129709243774414,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6793] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.48423075675964355
无历史消息,艾8能不能分期啊,问政策,,0.14522004127502441,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6664] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",金融政策,0.4158446788787842
"时间：2025-06-24 11:02:40
内容：艾8能不能分期啊",2.0的劲版落地多少,问价格,,0.1492750644683838,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6736] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",金融政策,0.3315110206604004
"时间：2025-06-19 19:05:15
内容：智跑介绍下

时间：2025-06-19 19:16:16
内容：适合女性开的车有推荐吗

时间：2025-06-19 19:37:10
内容：你们位置在哪",现在介绍的是啥车,看车型,,0.15425419807434082,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6950] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.4863588809967041
"时间：2025-06-19 19:05:15
内容：智跑介绍下

时间：2025-06-19 19:16:16
内容：适合女性开的车有推荐吗

时间：2025-06-19 19:37:10
内容：你们位置在哪

时间：2025-06-24 11:05:22
内容：现在介绍的是啥车",轿车,none,,0.15482687950134277,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7023] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.5679388046264648
无历史消息,德清当地的丰田4S店,问地址,,0.1545710563659668,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6671] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.3466451168060303
"时间：2025-06-19 19:05:15
内容：智跑介绍下

时间：2025-06-19 19:16:16
内容：适合女性开的车有推荐吗

时间：2025-06-19 19:37:10
内容：你们位置在哪

时间：2025-06-24 11:05:22
内容：现在介绍的是啥车

时间：2025-06-24 11:05:46
内容：轿车",有优惠吗,问优惠,,0.15724515914916992,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7108] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.6241931915283203
"时间：2025-06-19 19:16:16
内容：适合女性开的车有推荐吗

时间：2025-06-19 19:37:10
内容：你们位置在哪

时间：2025-06-24 11:05:22
内容：现在介绍的是啥车

时间：2025-06-24 11:05:46
内容：轿车

时间：2025-06-24 11:35:11
内容：有优惠吗",了解车型,看车型,,0.16171503067016602,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7102] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3949310779571533
无历史消息,艾8有什么优惠吗,问优惠,,0.15706372261047363,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6664] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.36592793464660645
"时间：2025-06-24 14:11:42
内容：[发呆]",小车车不错,none,,0.15715718269348145,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6692] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.7535321712493896
无历史消息,回不去,none,,0.17479991912841797,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6639] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3789958953857422
"时间：2025-06-24 14:14:52
内容：回不去",叫你那个小姐姐，联系我老婆他又没谈,none,,0.1613481044769287,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6768] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.34227514266967773
"时间：2025-06-24 14:14:52
内容：回不去

时间：2025-06-24 14:15:21
内容：叫你那个小姐姐，联系我老婆他又没谈",等到最后一台我来冲,要买车,,0.1671760082244873,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6883] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3173489570617676
"时间：2025-06-24 14:14:52
内容：回不去

时间：2025-06-24 14:15:21
内容：叫你那个小姐姐，联系我老婆他又没谈

时间：2025-06-24 14:15:58
内容：等到最后一台我来冲",他又没说多少钱，说好了我号码给她直接过去买,要买车,,0.17017507553100586,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7070] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.35811305046081543
"时间：2025-06-24 14:14:52
内容：回不去

时间：2025-06-24 14:15:21
内容：叫你那个小姐姐，联系我老婆他又没谈

时间：2025-06-24 14:15:58
内容：等到最后一台我来冲

时间：2025-06-24 14:16:59
内容：他又没说多少钱，说好了我号码给她直接过去买",只说便宜，两百也是便宜[捂脸],问优惠,,0.16776394844055176,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7211] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.5189237594604492
"时间：2025-06-24 14:14:52
内容：回不去

时间：2025-06-24 14:15:21
内容：叫你那个小姐姐，联系我老婆他又没谈

时间：2025-06-24 14:15:58
内容：等到最后一台我来冲

时间：2025-06-24 14:16:59
内容：他又没说多少钱，说好了我号码给她直接过去买

时间：2025-06-24 14:17:22
内容：只说便宜，两百也是便宜[捂脸]",她说少两千，又没确定,问优惠,,0.17031216621398926,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7332] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.3631269931793213
"时间：2025-06-24 14:15:21
内容：叫你那个小姐姐，联系我老婆他又没谈

时间：2025-06-24 14:15:58
内容：等到最后一台我来冲

时间：2025-06-24 14:16:59
内容：他又没说多少钱，说好了我号码给她直接过去买

时间：2025-06-24 14:17:22
内容：只说便宜，两百也是便宜[捂脸]

时间：2025-06-24 14:17:51
内容：她说少两千，又没确定",来得去就好了,none,,0.17000889778137207,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7350] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3081369400024414
"时间：2025-06-24 14:15:58
内容：等到最后一台我来冲

时间：2025-06-24 14:16:59
内容：他又没说多少钱，说好了我号码给她直接过去买

时间：2025-06-24 14:17:22
内容：只说便宜，两百也是便宜[捂脸]

时间：2025-06-24 14:17:51
内容：她说少两千，又没确定

时间：2025-06-24 14:18:22
内容：来得去就好了",半夜假都请不出,none,,0.15688109397888184,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7290] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.36275196075439453
"时间：2025-06-24 14:16:59
内容：他又没说多少钱，说好了我号码给她直接过去买

时间：2025-06-24 14:17:22
内容：只说便宜，两百也是便宜[捂脸]

时间：2025-06-24 14:17:51
内容：她说少两千，又没确定

时间：2025-06-24 14:18:22
内容：来得去就好了

时间：2025-06-24 14:18:40
内容：半夜假都请不出",安吉这边到时候要去提车都还是要晚上去开回来,none,,0.16780495643615723,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7362] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.35065698623657227
"时间：2025-06-24 14:17:22
内容：只说便宜，两百也是便宜[捂脸]

时间：2025-06-24 14:17:51
内容：她说少两千，又没确定

时间：2025-06-24 14:18:22
内容：来得去就好了

时间：2025-06-24 14:18:40
内容：半夜假都请不出

时间：2025-06-24 14:19:17
内容：安吉这边到时候要去提车都还是要晚上去开回来",钱都交了五百了,none,,0.16379380226135254,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7278] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3951129913330078
无历史消息,虎8Plus,看车型,,0.1654829978942871,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6632] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3829011917114258
"时间：2025-06-24 14:20:15
内容：虎8Plus",最近优惠,问优惠,,0.16858696937561035,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6683] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.40027284622192383
"时间：2025-06-24 14:20:15
内容：虎8Plus

时间：2025-06-24 14:20:27
内容：最近优惠",看一下,none,,0.16150307655334473,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6762] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.41486096382141113
"时间：2025-06-24 14:20:15
内容：虎8Plus

时间：2025-06-24 14:20:27
内容：最近优惠

时间：2025-06-24 14:20:39
内容：看一下",好像加过,none,,0.15834403038024902,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6847] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.40842700004577637
"时间：2025-06-24 14:17:51
内容：她说少两千，又没确定

时间：2025-06-24 14:18:22
内容：来得去就好了

时间：2025-06-24 14:18:40
内容：半夜假都请不出

时间：2025-06-24 14:19:17
内容：安吉这边到时候要去提车都还是要晚上去开回来

时间：2025-06-24 14:19:54
内容：钱都交了五百了",估计请不出，我上次是转班的时候去看的,none,,0.16292977333068848,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7306] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4343130588531494
"时间：2025-06-24 14:18:22
内容：来得去就好了

时间：2025-06-24 14:18:40
内容：半夜假都请不出

时间：2025-06-24 14:19:17
内容：安吉这边到时候要去提车都还是要晚上去开回来

时间：2025-06-24 14:19:54
内容：钱都交了五百了

时间：2025-06-24 14:21:11
内容：估计请不出，我上次是转班的时候去看的",一天十二小时,none,,0.14979290962219238,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7282] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3644270896911621
"时间：2025-06-24 14:18:40
内容：半夜假都请不出

时间：2025-06-24 14:19:17
内容：安吉这边到时候要去提车都还是要晚上去开回来

时间：2025-06-24 14:19:54
内容：钱都交了五百了

时间：2025-06-24 14:21:11
内容：估计请不出，我上次是转班的时候去看的

时间：2025-06-24 14:21:48
内容：一天十二小时",不干去哪里要钱买车[捂脸],要买车,,0.16248798370361328,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7314] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3321382999420166
"时间：2025-06-24 14:20:15
内容：虎8Plus

时间：2025-06-24 14:20:27
内容：最近优惠

时间：2025-06-24 14:20:39
内容：看一下

时间：2025-06-24 14:21:03
内容：好像加过",最近什么行情,问价格,,0.1566939353942871,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6944] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.3740692138671875
"时间：2025-06-24 14:20:15
内容：虎8Plus

时间：2025-06-24 14:20:27
内容：最近优惠

时间：2025-06-24 14:20:39
内容：看一下

时间：2025-06-24 14:21:03
内容：好像加过

时间：2025-06-24 14:22:08
内容：最近什么行情",亲人的行吗,问政策,,0.15816092491149902,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7035] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4700801372528076
"时间：2025-06-24 14:20:27
内容：最近优惠

时间：2025-06-24 14:20:39
内容：看一下

时间：2025-06-24 14:21:03
内容：好像加过

时间：2025-06-24 14:22:08
内容：最近什么行情

时间：2025-06-24 14:22:47
内容：亲人的行吗",亲人的旧车。,想卖车,,0.16016793251037598,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7060] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问置换,0.7059569358825684
"时间：2025-06-24 14:20:39
内容：看一下

时间：2025-06-24 14:21:03
内容：好像加过

时间：2025-06-24 14:22:08
内容：最近什么行情

时间：2025-06-24 14:22:47
内容：亲人的行吗

时间：2025-06-24 14:23:03
内容：亲人的旧车。",可以,none,,0.16017889976501465,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7048] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5657367706298828
"时间：2025-06-24 14:21:03
内容：好像加过

时间：2025-06-24 14:22:08
内容：最近什么行情

时间：2025-06-24 14:22:47
内容：亲人的行吗

时间：2025-06-24 14:23:03
内容：亲人的旧车。

时间：2025-06-24 14:23:16
内容：可以",黑色,问颜色,,0.1592409610748291,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7042] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问颜色,1.3294219970703125
"时间：2025-06-24 14:22:08
内容：最近什么行情

时间：2025-06-24 14:22:47
内容：亲人的行吗

时间：2025-06-24 14:23:03
内容：亲人的旧车。

时间：2025-06-24 14:23:16
内容：可以

时间：2025-06-24 14:24:06
内容：黑色",有吗,看车型,,0.15836286544799805,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7030] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5455050468444824
"时间：2025-06-24 14:22:47
内容：亲人的行吗

时间：2025-06-24 14:23:03
内容：亲人的旧车。

时间：2025-06-24 14:23:16
内容：可以

时间：2025-06-24 14:24:06
内容：黑色

时间：2025-06-24 14:24:11
内容：有吗",这两个版本有什么区别？,问配置,,0.15842103958129883,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7060] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3412151336669922
"时间：2025-06-24 14:23:03
内容：亲人的旧车。

时间：2025-06-24 14:23:16
内容：可以

时间：2025-06-24 14:24:06
内容：黑色

时间：2025-06-24 14:24:11
内容：有吗

时间：2025-06-24 14:24:19
内容：这两个版本有什么区别？",这两个不都是新款吗？,问配置,,0.159088134765625,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7090] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.44138598442077637
"时间：2025-06-24 14:19:17
内容：安吉这边到时候要去提车都还是要晚上去开回来

时间：2025-06-24 14:19:54
内容：钱都交了五百了

时间：2025-06-24 14:21:11
内容：估计请不出，我上次是转班的时候去看的

时间：2025-06-24 14:21:48
内容：一天十二小时

时间：2025-06-24 14:22:01
内容：不干去哪里要钱买车[捂脸]",我这边谈的我知道不是最低价，还有两三千的空间，但是这边方便点,问优惠,,0.16453814506530762,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7452] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.3933279514312744
"时间：2025-06-24 14:23:16
内容：可以

时间：2025-06-24 14:24:06
内容：黑色

时间：2025-06-24 14:24:11
内容：有吗

时间：2025-06-24 14:24:19
内容：这两个版本有什么区别？

时间：2025-06-24 14:24:56
内容：这两个不都是新款吗？",中配的就可以。,问配置,,0.17499899864196777,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7096] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.31354808807373047
"时间：2025-06-24 14:24:11
内容：有吗

时间：2025-06-24 14:24:19
内容：这两个版本有什么区别？

时间：2025-06-24 14:24:56
内容：这两个不都是新款吗？

时间：2025-06-24 14:25:05
内容：中配的就可以。

时间：2025-06-24 14:26:24
内容：好",有空了过来,问试乘试驾,,0.15563178062438965,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7108] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4532492160797119
"时间：2025-06-24 14:19:54
内容：钱都交了五百了

时间：2025-06-24 14:21:11
内容：估计请不出，我上次是转班的时候去看的

时间：2025-06-24 14:21:48
内容：一天十二小时

时间：2025-06-24 14:22:01
内容：不干去哪里要钱买车[捂脸]

时间：2025-06-24 14:25:02
内容：我这边谈的我知道不是最低价，还有两三千的空间，但是这边方便点",最低能谈2.2，2.3，我谈了两个,问优惠,,0.1651151180267334,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7398] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.28966283798217773
"时间：2025-06-24 14:24:19
内容：这两个版本有什么区别？

时间：2025-06-24 14:24:56
内容：这两个不都是新款吗？

时间：2025-06-24 14:25:05
内容：中配的就可以。

时间：2025-06-24 14:26:24
内容：好

时间：2025-06-24 14:26:36
内容：有空了过来",我之前有过这个关注抖音这一块儿。,none,,0.16724300384521484,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7192] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3989276885986328
"时间：2025-06-24 14:24:56
内容：这两个不都是新款吗？

时间：2025-06-24 14:25:05
内容：中配的就可以。

时间：2025-06-24 14:26:24
内容：好

时间：2025-06-24 14:26:36
内容：有空了过来

时间：2025-06-24 14:27:06
内容：我之前有过这个关注抖音这一块儿。",去看车不方便,none,,0.16154909133911133,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7162] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问试乘试驾,0.6725952625274658
"时间：2025-06-24 14:21:11
内容：估计请不出，我上次是转班的时候去看的

时间：2025-06-24 14:21:48
内容：一天十二小时

时间：2025-06-24 14:22:01
内容：不干去哪里要钱买车[捂脸]

时间：2025-06-24 14:25:02
内容：我这边谈的我知道不是最低价，还有两三千的空间，但是这边方便点

时间：2025-06-24 14:26:47
内容：最低能谈2.2，2.3，我谈了两个",你那小姐姐舍不得拍板，光说没用,none,,0.1655421257019043,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7446] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3428070545196533
"时间：2025-06-24 14:25:05
内容：中配的就可以。

时间：2025-06-24 14:26:24
内容：好

时间：2025-06-24 14:26:36
内容：有空了过来

时间：2025-06-24 14:27:06
内容：我之前有过这个关注抖音这一块儿。

时间：2025-06-24 14:27:52
内容：去看车不方便",可以来接吗,问试乘试驾,,0.16458415985107422,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7132] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问试乘试驾,0.6854047775268555
"时间：2025-06-24 14:26:24
内容：好

时间：2025-06-24 14:26:36
内容：有空了过来

时间：2025-06-24 14:27:06
内容：我之前有过这个关注抖音这一块儿。

时间：2025-06-24 14:27:52
内容：去看车不方便

时间：2025-06-24 14:28:03
内容：可以来接吗",德清县,问地址,,0.17020893096923828,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7108] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.7968928813934326
"时间：2025-06-24 14:26:36
内容：有空了过来

时间：2025-06-24 14:27:06
内容：我之前有过这个关注抖音这一块儿。

时间：2025-06-24 14:27:52
内容：去看车不方便

时间：2025-06-24 14:28:03
内容：可以来接吗

时间：2025-06-24 14:28:24
内容：德清县",武康镇,问地址,,0.1708202362060547,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7120] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.3600330352783203
无历史消息,173,none,,0.16934490203857422,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6624] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.32400012016296387
"时间：2025-06-24 14:28:46
内容：173",1733,none,,0.16791391372680664,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6655] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.33595800399780273
"时间：2025-06-24 14:28:46
内容：173

时间：2025-06-24 14:28:48
内容：1733",4733,none,,0.1697070598602295,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6720] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,1.2221932411193848
"时间：2025-06-24 14:27:06
内容：我之前有过这个关注抖音这一块儿。

时间：2025-06-24 14:27:52
内容：去看车不方便

时间：2025-06-24 14:28:03
内容：可以来接吗

时间：2025-06-24 14:28:24
内容：德清县

时间：2025-06-24 14:28:37
内容：武康镇",打过啦,none,,0.16489005088806152,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7108] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.33004021644592285
"时间：2025-06-24 14:27:52
内容：去看车不方便

时间：2025-06-24 14:28:03
内容：可以来接吗

时间：2025-06-24 14:28:24
内容：德清县

时间：2025-06-24 14:28:37
内容：武康镇

时间：2025-06-24 14:29:30
内容：打过啦",我的电话号码,问联系方式,,0.17348575592041016,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7048] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问联系方式,0.3682432174682617
"时间：2025-06-24 14:28:03
内容：可以来接吗

时间：2025-06-24 14:28:24
内容：德清县

时间：2025-06-24 14:28:37
内容：武康镇

时间：2025-06-24 14:29:30
内容：打过啦

时间：2025-06-24 14:29:53
内容：我的电话号码",公屏上留的。,none,,0.16845417022705078,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7048] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.39847683906555176
"时间：2025-06-24 14:28:24
内容：德清县

时间：2025-06-24 14:28:37
内容：武康镇

时间：2025-06-24 14:29:30
内容：打过啦

时间：2025-06-24 14:29:53
内容：我的电话号码

时间：2025-06-24 14:30:26
内容：公屏上留的。",153,none,,0.17192578315734863,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7021] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3181331157684326
"时间：2025-06-24 14:28:37
内容：武康镇

时间：2025-06-24 14:29:30
内容：打过啦

时间：2025-06-24 14:29:53
内容：我的电话号码

时间：2025-06-24 14:30:26
内容：公屏上留的。

时间：2025-06-24 14:30:32
内容：153",的,none,,0.16578412055969238,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7009] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.34885406494140625
"时间：2025-06-24 14:29:30
内容：打过啦

时间：2025-06-24 14:29:53
内容：我的电话号码

时间：2025-06-24 14:30:26
内容：公屏上留的。

时间：2025-06-24 14:30:32
内容：153

时间：2025-06-24 14:30:36
内容：的",下啦,none,,0.1679520606994629,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7003] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.32209205627441406
无历史消息,只有奇瑞？？,看车型,,0.16964006423950195,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6657] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4430677890777588
"时间：2025-06-24 14:41:42
内容：只有奇瑞？？",是库存车还是,问库存车,,0.16868829727172852,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6720] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问库存车,0.3482081890106201
"时间：2025-06-24 14:41:42
内容：只有奇瑞？？

时间：2025-06-24 14:42:05
内容：是库存车还是",看看内饰,看车型,,0.1615738868713379,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6805] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.5544018745422363
"时间：2025-06-24 14:41:42
内容：只有奇瑞？？

时间：2025-06-24 14:42:05
内容：是库存车还是

时间：2025-06-24 14:42:15
内容：看看内饰",这个落地大概能要多少个,问价格,,0.15891504287719727,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6932] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.41966795921325684
"时间：2025-06-24 14:41:42
内容：只有奇瑞？？

时间：2025-06-24 14:42:05
内容：是库存车还是

时间：2025-06-24 14:42:15
内容：看看内饰

时间：2025-06-24 14:43:11
内容：这个落地大概能要多少个",没有旧的,看车型,,0.16292786598205566,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7017] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4825718402862549
无历史消息,智多少能落地,问价格,,0.16962909698486328,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6657] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.36626410484313965
无历史消息,黑户能做不,金融政策,,0.16634488105773926,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",金融政策,0.4555797576904297
无历史消息,3.4w是什么车,看车型,,0.16802406311035156,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6649] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.4461209774017334
"时间：2025-06-24 14:59:08
内容：3.4w是什么车",跑滴滴,none,,0.16974115371704102,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6694] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4262561798095703
无历史消息,关注主播不迷路,none,,0.17064118385314941,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6663] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.2693331241607666
无历史消息,那里的店？,问地址,,0.16563105583190918,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.3771219253540039
无历史消息,卡罗拉有没,看车型,,0.17046403884887695,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.37459802627563477
"时间：2025-06-24 15:27:45
内容：卡罗拉有没",1.2t带天窗那款多少钱,问价格,,0.1724400520324707,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6730] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4186398983001709
"时间：2025-06-24 15:27:45
内容：卡罗拉有没

时间：2025-06-24 15:28:42
内容：1.2t带天窗那款多少钱",置换补贴多少钱,问置换,,0.1672520637512207,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6833] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.3614771366119385
"时间：2025-06-24 15:27:45
内容：卡罗拉有没

时间：2025-06-24 15:28:42
内容：1.2t带天窗那款多少钱

时间：2025-06-24 15:29:35
内容：置换补贴多少钱",五菱宏光,none,,0.1819901466369629,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6918] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.3183410167694092
"时间：2025-06-24 15:27:45
内容：卡罗拉有没

时间：2025-06-24 15:28:42
内容：1.2t带天窗那款多少钱

时间：2025-06-24 15:29:35
内容：置换补贴多少钱

时间：2025-06-24 15:30:06
内容：五菱宏光",面包车,none,,0.18039703369140625,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6997] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4424259662628174
"时间：2025-06-24 15:27:45
内容：卡罗拉有没

时间：2025-06-24 15:28:42
内容：1.2t带天窗那款多少钱

时间：2025-06-24 15:29:35
内容：置换补贴多少钱

时间：2025-06-24 15:30:06
内容：五菱宏光

时间：2025-06-24 15:30:29
内容：面包车",上路带天窗的8万上路可以吗,问价格,,0.1851038932800293,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7131] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.44820713996887207
"时间：2025-06-24 15:28:42
内容：1.2t带天窗那款多少钱

时间：2025-06-24 15:29:35
内容：置换补贴多少钱

时间：2025-06-24 15:30:06
内容：五菱宏光

时间：2025-06-24 15:30:29
内容：面包车

时间：2025-06-24 15:31:15
内容：上路带天窗的8万上路可以吗",162卡罗拉,看车型,,0.1808459758758545,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7122] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5348882675170898
"时间：2025-06-24 15:29:35
内容：置换补贴多少钱

时间：2025-06-24 15:30:06
内容：五菱宏光

时间：2025-06-24 15:30:29
内容：面包车

时间：2025-06-24 15:31:15
内容：上路带天窗的8万上路可以吗

时间：2025-06-24 15:31:37
内容：162卡罗拉",1.2t卡罗拉,看车型,,0.17944693565368652,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7092] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.5333540439605713
"时间：2025-06-24 15:30:06
内容：五菱宏光

时间：2025-06-24 15:30:29
内容：面包车

时间：2025-06-24 15:31:15
内容：上路带天窗的8万上路可以吗

时间：2025-06-24 15:31:37
内容：162卡罗拉

时间：2025-06-24 15:32:01
内容：1.2t卡罗拉",你在武康哪里,问地址,,0.17482590675354004,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7086] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.4008619785308838
"时间：2025-06-24 15:30:29
内容：面包车

时间：2025-06-24 15:31:15
内容：上路带天窗的8万上路可以吗

时间：2025-06-24 15:31:37
内容：162卡罗拉

时间：2025-06-24 15:32:01
内容：1.2t卡罗拉

时间：2025-06-24 15:32:20
内容：你在武康哪里",宝马4s店哪里,问地址,,0.18280386924743652,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7094] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.5508458614349365
"时间：2025-06-24 15:31:15
内容：上路带天窗的8万上路可以吗

时间：2025-06-24 15:31:37
内容：162卡罗拉

时间：2025-06-24 15:32:01
内容：1.2t卡罗拉

时间：2025-06-24 15:32:20
内容：你在武康哪里

时间：2025-06-24 15:32:59
内容：宝马4s店哪里",1.2t啥配置,问配置,,0.17771077156066895,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7098] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.33923983573913574
"时间：2025-06-24 15:31:37
内容：162卡罗拉

时间：2025-06-24 15:32:01
内容：1.2t卡罗拉

时间：2025-06-24 15:32:20
内容：你在武康哪里

时间：2025-06-24 15:32:59
内容：宝马4s店哪里

时间：2025-06-24 15:33:24
内容：1.2t啥配置",看看,none,,0.18384599685668945,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7037] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.44944000244140625
无历史消息,荣放低配有吗,看车型,,0.1778700351715088,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6657] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3309781551361084
"时间：2025-06-25 11:51:00
内容：荣放低配有吗",什么价格,问价格,,0.17575287818908691,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6708] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.31946587562561035
无历史消息,凌放看看,看车型,,0.17631101608276367,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6645] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,1.3084030151367188
"时间：2025-06-25 12:03:47
内容：凌放看看",好多展厅都没停,none,,0.1742711067199707,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6714] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3939697742462158
"时间：2025-06-25 12:03:47
内容：凌放看看

时间：2025-06-25 12:04:36
内容：好多展厅都没停",不好卖，太贵,问价格,,0.16899681091308594,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6811] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3490109443664551
"时间：2025-06-25 12:03:47
内容：凌放看看

时间：2025-06-25 12:04:36
内容：好多展厅都没停

时间：2025-06-25 12:05:07
内容：不好卖，太贵",是不是要停产了,none,,0.17046499252319336,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6914] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4245772361755371
"时间：2025-06-25 12:03:47
内容：凌放看看

时间：2025-06-25 12:04:36
内容：好多展厅都没停

时间：2025-06-25 12:05:07
内容：不好卖，太贵

时间：2025-06-25 12:07:28
内容：是不是要停产了",发动机都一样的，贵了四五万,问价格,,0.16264915466308594,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7053] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.7982747554779053
无历史消息,拓界有吗,看车型,,0.15510201454162598,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6645] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3061177730560303
"时间：2025-06-25 16:01:09
内容：拓界有吗",现在现金优惠多少,问优惠,,0.16002893447875977,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6720] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.4582500457763672
"时间：2025-06-25 16:01:09
内容：拓界有吗

时间：2025-06-25 16:01:26
内容：现在现金优惠多少",+几？,问优惠,,0.16625189781188965,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6794] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.43590497970581055
无历史消息,--------------------,none,,0.1634387969970703,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6641] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3656141757965088
无历史消息,您好，主播,none,,0.15769481658935547,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3349158763885498
"时间：2025-06-25 16:04:22
内容：--------------------",买！,none,,0.14981341361999512,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6680] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.3657357692718506
"时间：2025-06-25 16:04:22
内容：--------------------

时间：2025-06-25 16:10:21
内容：买！",不！,none,,0.150313138961792,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6753] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5597069263458252
"时间：2025-06-25 16:04:22
内容：--------------------

时间：2025-06-25 16:10:21
内容：买！

时间：2025-06-25 16:10:22
内容：不！",起！,none,,0.15109610557556152,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6826] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5013246536254883
无历史消息,K5顶配落地多少,问价格,,0.15099382400512695,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6659] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.5389149188995361
无历史消息,新款嘉华什么时候上市,看车型,,0.18185019493103027,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6681] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.7972860336303711
"时间：2025-06-25 16:11:23
内容：K5顶配落地多少",20,问价格,,0.16570091247558594,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6688] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4705960750579834
"时间：2025-06-25 16:11:23
内容：K5顶配落地多少

时间：2025-06-25 16:12:27
内容：20",要买就20,问价格,,0.1558070182800293,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6769] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.5861930847167969
"时间：2025-06-25 16:11:23
内容：K5顶配落地多少

时间：2025-06-25 16:12:27
内容：20

时间：2025-06-25 16:12:36
内容：要买就20",大概落地多少钱,问价格,,0.15671610832214355,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6872] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.5689249038696289
无历史消息,最近在看这车，自己改灯改音响，店里还保吗,问政策,,0.16393399238586426,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6741] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.4058220386505127
"时间：2025-06-25 16:11:23
内容：K5顶配落地多少

时间：2025-06-25 16:12:27
内容：20

时间：2025-06-25 16:12:36
内容：要买就20

时间：2025-06-25 16:13:27
内容：大概落地多少钱",我的车置换不合适，我21款起亚k3才跑两万公里不合适,问置换,,0.15245890617370605,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7069] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问置换,0.31913328170776367
"时间：2025-06-25 16:11:23
内容：K5顶配落地多少

时间：2025-06-25 16:12:27
内容：20

时间：2025-06-25 16:12:36
内容：要买就20

时间：2025-06-25 16:13:27
内容：大概落地多少钱

时间：2025-06-25 16:14:17
内容：我的车置换不合适，我21款起亚k3才跑两万公里不合适",能补贴多少,问优惠,,0.1527388095855713,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7160] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.3146848678588867
无历史消息,为啥起亚首保用的道达尔机油那么垃圾。首保后比之前油耗还高,问配置,,0.17057585716247559,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6789] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5920870304107666
"时间：2025-06-25 16:14:13
内容：最近在看这车，自己改灯改音响，店里还保吗

时间：2025-06-25 16:14:30
内容：对",过段时间来当面聊，这车我要试驾,问试乘试驾,,0.15350699424743652,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6925] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问试乘试驾,0.35376787185668945
"时间：2025-06-25 16:04:22
内容：--------------------

时间：2025-06-25 16:10:21
内容：买！

时间：2025-06-25 16:10:22
内容：不！

时间：2025-06-25 16:10:24
内容：起！",啥？要改狮铂拓界音臬？,问置换,,0.15476775169372559,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6953] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4123067855834961
"时间：2025-06-25 16:14:13
内容：最近在看这车，自己改灯改音响，店里还保吗

时间：2025-06-25 16:14:30
内容：对

时间：2025-06-25 16:15:37
内容：过段时间来当面聊，这车我要试驾",我直接来店，电话一丢全是电话,问试乘试驾,,0.16258716583251953,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7070] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3803398609161377
"时间：2025-06-25 16:04:22
内容：--------------------

时间：2025-06-25 16:10:21
内容：买！

时间：2025-06-25 16:10:22
内容：不！

时间：2025-06-25 16:10:24
内容：起！

时间：2025-06-25 16:16:19
内容：啥？要改狮铂拓界音臬？",没有必要 了吧。这个原车音响很好了,问置换,,0.15838098526000977,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7111] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.37474513053894043
"时间：2025-06-25 16:14:13
内容：最近在看这车，自己改灯改音响，店里还保吗

时间：2025-06-25 16:14:30
内容：对

时间：2025-06-25 16:15:37
内容：过段时间来当面聊，这车我要试驾

时间：2025-06-25 16:16:33
内容：我直接来店，电话一丢全是电话",不保真,none,,0.1677250862121582,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7149] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.6810030937194824
"时间：2025-06-25 16:10:21
内容：买！

时间：2025-06-25 16:10:22
内容：不！

时间：2025-06-25 16:10:24
内容：起！

时间：2025-06-25 16:16:19
内容：啥？要改狮铂拓界音臬？

时间：2025-06-25 16:17:12
内容：没有必要 了吧。这个原车音响很好了",顶配的是贴牌哈曼卡顿音响。没有意思,问配置,,0.16487908363342285,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7193] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.3254570960998535
"时间：2025-06-25 16:10:22
内容：不！

时间：2025-06-25 16:10:24
内容：起！

时间：2025-06-25 16:16:19
内容：啥？要改狮铂拓界音臬？

时间：2025-06-25 16:17:12
内容：没有必要 了吧。这个原车音响很好了

时间：2025-06-25 16:17:37
内容：顶配的是贴牌哈曼卡顿音响。没有意思",我买的2.0T尊贵。觉得音响够用了。,问配置,,0.16751885414123535,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7269] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.33043527603149414
"时间：2025-06-25 16:10:24
内容：起！

时间：2025-06-25 16:16:19
内容：啥？要改狮铂拓界音臬？

时间：2025-06-25 16:17:12
内容：没有必要 了吧。这个原车音响很好了

时间：2025-06-25 16:17:37
内容：顶配的是贴牌哈曼卡顿音响。没有意思

时间：2025-06-25 16:18:53
内容：我买的2.0T尊贵。觉得音响够用了。",真要是觉得音响差。还不如买个 蓝牙音响  jbl pulse 5  像个水杯一样。就可以了,问配置,,0.1689291000366211,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7447] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.374744176864624
"时间：2025-06-25 16:16:19
内容：啥？要改狮铂拓界音臬？

时间：2025-06-25 16:17:12
内容：没有必要 了吧。这个原车音响很好了

时间：2025-06-25 16:17:37
内容：顶配的是贴牌哈曼卡顿音响。没有意思

时间：2025-06-25 16:18:53
内容：我买的2.0T尊贵。觉得音响够用了。

时间：2025-06-25 16:19:40
内容：真要是觉得音响差。还不如买个 蓝牙音响  jbl pulse 5  像个水杯一样。就可以了",还可以去哪里带哪里。,none,,0.16179704666137695,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7495] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.33449387550354004
"时间：2025-06-19 19:04:25
内容：在安庆出差都给你点赞[灵机一动]

时间：2025-06-19 19:07:08
内容：主播要出个镜

时间：2025-06-19 19:07:28
内容：出镜粉丝就蹭蹭往上涨",听说应届毕业生购车你们有额外政策？,问政策,,0.16701483726501465,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7054] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.47054076194763184
"时间：2025-06-25 16:17:12
内容：没有必要 了吧。这个原车音响很好了

时间：2025-06-25 16:17:37
内容：顶配的是贴牌哈曼卡顿音响。没有意思

时间：2025-06-25 16:18:53
内容：我买的2.0T尊贵。觉得音响够用了。

时间：2025-06-25 16:19:40
内容：真要是觉得音响差。还不如买个 蓝牙音响  jbl pulse 5  像个水杯一样。就可以了

时间：2025-06-25 16:19:46
内容：还可以去哪里带哪里。",路过合肥，感觉建设还没有我们这边县城好,none,,0.16635394096374512,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7543] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3468201160430908
"时间：2025-06-25 16:17:37
内容：顶配的是贴牌哈曼卡顿音响。没有意思

时间：2025-06-25 16:18:53
内容：我买的2.0T尊贵。觉得音响够用了。

时间：2025-06-25 16:19:40
内容：真要是觉得音响差。还不如买个 蓝牙音响  jbl pulse 5  像个水杯一样。就可以了

时间：2025-06-25 16:19:46
内容：还可以去哪里带哪里。

时间：2025-06-25 16:25:30
内容：路过合肥，感觉建设还没有我们这边县城好",宜兴,问地址,,0.1604921817779541,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7458] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.47071003913879395
无历史消息,店才开的吧,none,,0.15771007537841797,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.7039940357208252
"时间：2025-06-25 16:27:41
内容：店才开的吧",之前我手机收到厂家发的信息,none,,0.1580491065979004,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6756] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.36618494987487793
"时间：2025-06-25 16:27:41
内容：店才开的吧

时间：2025-06-25 16:28:04
内容：之前我手机收到厂家发的信息",给我,none,,0.16967391967773438,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6829] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.9501028060913086
无历史消息,这么大门面,none,,0.1776888370513916,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.37578392028808594
"时间：2025-06-25 16:27:41
内容：店才开的吧

时间：2025-06-25 16:28:04
内容：之前我手机收到厂家发的信息

时间：2025-06-25 16:28:11
内容：给我",对的,none,,0.17707014083862305,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6902] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.30343103408813477
"时间：2025-06-25 16:27:41
内容：店才开的吧

时间：2025-06-25 16:28:04
内容：之前我手机收到厂家发的信息

时间：2025-06-25 16:28:11
内容：给我

时间：2025-06-25 16:28:19
内容：对的",有售后服务吗,问政策,,0.17531394958496094,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6999] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.3922100067138672
"时间：2025-06-25 16:27:41
内容：店才开的吧

时间：2025-06-25 16:28:04
内容：之前我手机收到厂家发的信息

时间：2025-06-25 16:28:11
内容：给我

时间：2025-06-25 16:28:19
内容：对的

时间：2025-06-25 16:28:36
内容：有售后服务吗",我短信有,none,,0.17243099212646484,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7084] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.4724752902984619
无历史消息,拓界,看车型,,0.17391490936279297,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6633] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3968677520751953
"时间：2025-06-26 10:59:56
内容：拓界",介绍一下优惠多少,问优惠,,0.1784658432006836,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6708] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.7586832046508789
"时间：2025-06-25 16:18:53
内容：我买的2.0T尊贵。觉得音响够用了。

时间：2025-06-25 16:19:40
内容：真要是觉得音响差。还不如买个 蓝牙音响  jbl pulse 5  像个水杯一样。就可以了

时间：2025-06-25 16:19:46
内容：还可以去哪里带哪里。

时间：2025-06-25 16:25:30
内容：路过合肥，感觉建设还没有我们这边县城好

时间：2025-06-25 16:25:48
内容：宜兴",这个这个这个，那个那个那个。,none,,0.17715120315551758,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7440] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.36048102378845215
"时间：2025-06-25 16:19:40
内容：真要是觉得音响差。还不如买个 蓝牙音响  jbl pulse 5  像个水杯一样。就可以了

时间：2025-06-25 16:19:46
内容：还可以去哪里带哪里。

时间：2025-06-25 16:25:30
内容：路过合肥，感觉建设还没有我们这边县城好

时间：2025-06-25 16:25:48
内容：宜兴

时间：2025-06-26 11:08:20
内容：这个这个这个，那个那个那个。",都不买,none,,0.1798250675201416,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7370] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4347219467163086
"时间：2025-06-25 16:19:46
内容：还可以去哪里带哪里。

时间：2025-06-25 16:25:30
内容：路过合肥，感觉建设还没有我们这边县城好

时间：2025-06-25 16:25:48
内容：宜兴

时间：2025-06-26 11:08:20
内容：这个这个这个，那个那个那个。

时间：2025-06-26 11:08:31
内容：都不买",都推沟里,none,,0.17785215377807617,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7204] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.7281308174133301
"时间：2025-06-25 16:25:30
内容：路过合肥，感觉建设还没有我们这边县城好

时间：2025-06-25 16:25:48
内容：宜兴

时间：2025-06-26 11:08:20
内容：这个这个这个，那个那个那个。

时间：2025-06-26 11:08:31
内容：都不买

时间：2025-06-26 11:08:36
内容：都推沟里",[捂脸],none,,0.185899019241333,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7158] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3674952983856201
无历史消息,720续，低配多少钱,问价格,,0.17642593383789062,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6666] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,1.8192429542541504
无历史消息,店在合肥哪个区,问地址,,0.17774271965026855,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6663] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.4160890579223633
无历史消息,720多钱,问价格,,0.17637085914611816,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6636] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.37786388397216797
"时间：2025-06-26 11:42:17
内容：720多钱",国道能跑多远,问配置,,0.17473196983337402,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6699] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5178179740905762
"时间：2025-06-26 11:42:17
内容：720多钱

时间：2025-06-26 11:43:16
内容：国道能跑多远",有定速巡航吗,问配置,,0.17429137229919434,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6796] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.8763372898101807
"时间：2025-06-26 11:42:17
内容：720多钱

时间：2025-06-26 11:43:16
内容：国道能跑多远

时间：2025-06-26 11:43:51
内容：有定速巡航吗",质保？,问政策,,0.1773381233215332,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6875] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.6012930870056152
"时间：2025-06-26 11:42:17
内容：720多钱

时间：2025-06-26 11:43:16
内容：国道能跑多远

时间：2025-06-26 11:43:51
内容：有定速巡航吗

时间：2025-06-26 11:44:53
内容：质保？",内宽多少,看车型,,0.1717681884765625,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6960] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.39638185501098633
"时间：2025-06-26 11:42:17
内容：720多钱

时间：2025-06-26 11:43:16
内容：国道能跑多远

时间：2025-06-26 11:43:51
内容：有定速巡航吗

时间：2025-06-26 11:44:53
内容：质保？

时间：2025-06-26 11:45:45
内容：内宽多少",最窄110,看车型,,0.17245912551879883,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7036] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3554110527038574
"时间：2025-06-26 11:43:16
内容：国道能跑多远

时间：2025-06-26 11:43:51
内容：有定速巡航吗

时间：2025-06-26 11:44:53
内容：质保？

时间：2025-06-26 11:45:45
内容：内宽多少

时间：2025-06-26 11:46:52
内容：最窄110",长,none,,0.17524313926696777,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7027] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.6607151031494141
"时间：2025-06-26 11:43:51
内容：有定速巡航吗

时间：2025-06-26 11:44:53
内容：质保？

时间：2025-06-26 11:45:45
内容：内宽多少

时间：2025-06-26 11:46:52
内容：最窄110

时间：2025-06-26 11:47:07
内容：长",现在这样多长,问配置,,0.18717694282531738,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7027] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.3850109577178955
"时间：2025-06-26 11:44:53
内容：质保？

时间：2025-06-26 11:45:45
内容：内宽多少

时间：2025-06-26 11:46:52
内容：最窄110

时间：2025-06-26 11:47:07
内容：长

时间：2025-06-26 11:47:59
内容：现在这样多长",车外尺寸,问配置,,0.16869878768920898,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7015] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.4005579948425293
无历史消息,预约了，没发我,问试乘试驾,,0.17905807495117188,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6663] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.39911508560180664
无历史消息,但是我一直没看到实体，我只看到图片和展厅的。,看车型,,0.18795418739318848,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6753] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5814309120178223
"时间：2025-06-26 19:07:04
内容：但是我一直没看到实体，我只看到图片和展厅的。",这里只有蚌埠和阜阳有，我到合肥买不更好吗,none,,0.18485713005065918,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6900] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.4117708206176758
"时间：2025-06-19 19:17:20
内容：赛图斯那台带全景天窗吗",阜阳雪峰 是一家店吗,问地址,,0.17615175247192383,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6769] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.5023519992828369
"时间：2025-06-19 19:17:20
内容：赛图斯那台带全景天窗吗

时间：2025-06-26 19:08:18
内容：阜阳雪峰 是一家店吗",一个集团的,none,,0.17543506622314453,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6860] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.587040901184082
"时间：2025-06-26 19:07:04
内容：但是我一直没看到实体，我只看到图片和展厅的。

时间：2025-06-26 19:07:52
内容：这里只有蚌埠和阜阳有，我到合肥买不更好吗",你们那个门市叫我到阜阳提我没有去的。,none,,0.1764359474182129,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7069] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.3944129943847656
"时间：2025-06-26 19:07:04
内容：但是我一直没看到实体，我只看到图片和展厅的。

时间：2025-06-26 19:07:52
内容：这里只有蚌埠和阜阳有，我到合肥买不更好吗

时间：2025-06-26 19:09:14
内容：你们那个门市叫我到阜阳提我没有去的。",阜阳店的。,none,,0.17427992820739746,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7160] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.4048292636871338
"时间：2025-06-26 19:07:04
内容：但是我一直没看到实体，我只看到图片和展厅的。

时间：2025-06-26 19:07:52
内容：这里只有蚌埠和阜阳有，我到合肥买不更好吗

时间：2025-06-26 19:09:14
内容：你们那个门市叫我到阜阳提我没有去的。

时间：2025-06-26 19:09:44
内容：阜阳店的。",合肥店在哪个方向,问地址,,0.1746068000793457,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7269] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,1.4590201377868652
"时间：2025-06-26 19:07:04
内容：但是我一直没看到实体，我只看到图片和展厅的。

时间：2025-06-26 19:07:52
内容：这里只有蚌埠和阜阳有，我到合肥买不更好吗

时间：2025-06-26 19:09:14
内容：你们那个门市叫我到阜阳提我没有去的。

时间：2025-06-26 19:09:44
内容：阜阳店的。

时间：2025-06-26 19:10:20
内容：合肥店在哪个方向",在哪个区,问地址,,0.17648911476135254,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7354] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.687736988067627
"时间：2025-06-26 19:07:52
内容：这里只有蚌埠和阜阳有，我到合肥买不更好吗

时间：2025-06-26 19:09:14
内容：你们那个门市叫我到阜阳提我没有去的。

时间：2025-06-26 19:09:44
内容：阜阳店的。

时间：2025-06-26 19:10:20
内容：合肥店在哪个方向

时间：2025-06-26 19:10:56
内容：在哪个区",我肯定来试驾一下。,问试乘试驾,,0.17463088035583496,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7276] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问试乘试驾,0.8625631332397461
"时间：2025-06-26 19:09:14
内容：你们那个门市叫我到阜阳提我没有去的。

时间：2025-06-26 19:09:44
内容：阜阳店的。

时间：2025-06-26 19:10:20
内容：合肥店在哪个方向

时间：2025-06-26 19:10:56
内容：在哪个区

时间：2025-06-26 19:18:14
内容：我肯定来试驾一下。",这个我跟阜阳的销售也拉了不少。,none,,0.17261004447937012,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7246] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.6548862457275391
"时间：2025-06-26 19:09:44
内容：阜阳店的。

时间：2025-06-26 19:10:20
内容：合肥店在哪个方向

时间：2025-06-26 19:10:56
内容：在哪个区

时间：2025-06-26 19:18:14
内容：我肯定来试驾一下。

时间：2025-06-26 19:18:55
内容：这个我跟阜阳的销售也拉了不少。",直接从你那提。,要买车,,0.209975004196167,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7180] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.34751200675964355
"时间：2025-06-26 19:10:20
内容：合肥店在哪个方向

时间：2025-06-26 19:10:56
内容：在哪个区

时间：2025-06-26 19:18:14
内容：我肯定来试驾一下。

时间：2025-06-26 19:18:55
内容：这个我跟阜阳的销售也拉了不少。

时间：2025-06-26 19:19:26
内容：直接从你那提。",5万不到。,问价格,,0.16786789894104004,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7175] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4606781005859375
无历史消息,给我来100辆,要买车,,0.17905306816101074,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6648] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3373301029205322
"时间：2025-06-26 19:10:56
内容：在哪个区

时间：2025-06-26 19:18:14
内容：我肯定来试驾一下。

时间：2025-06-26 19:18:55
内容：这个我跟阜阳的销售也拉了不少。

时间：2025-06-26 19:19:26
内容：直接从你那提。

时间：2025-06-26 19:19:49
内容：5万不到。",包牌，包税，包保险。,问政策,,0.17555785179138184,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7187] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.5840272903442383
"时间：2025-06-26 19:18:14
内容：我肯定来试驾一下。

时间：2025-06-26 19:18:55
内容：这个我跟阜阳的销售也拉了不少。

时间：2025-06-26 19:19:26
内容：直接从你那提。

时间：2025-06-26 19:19:49
内容：5万不到。

时间：2025-06-26 19:21:15
内容：包牌，包税，包保险。",我的车是15000，，那个产值3000。,none,,0.18219494819641113,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7238] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,2.3851399421691895
无历史消息,去年14w 2.0豪华带置换都不肯卖,想卖车,,0.16901183128356934,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6694] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,1.0759971141815186
"时间：2025-06-26 19:26:06
内容：去年14w 2.0豪华带置换都不肯卖",长春街那家吧  我不说是谁,none,,0.16699814796447754,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6789] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.34627509117126465
"时间：2025-06-26 19:26:06
内容：去年14w 2.0豪华带置换都不肯卖

时间：2025-06-26 19:27:12
内容：长春街那家吧  我不说是谁",已经提了途胜了,none,,0.16444802284240723,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6892] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5355207920074463
"时间：2025-06-26 19:18:55
内容：这个我跟阜阳的销售也拉了不少。

时间：2025-06-26 19:19:26
内容：直接从你那提。

时间：2025-06-26 19:19:49
内容：5万不到。

时间：2025-06-26 19:21:15
内容：包牌，包税，包保险。

时间：2025-06-26 19:21:52
内容：我的车是15000，，那个产值3000。",79800减12000减3000减15000--49800元,问优惠,,0.16099190711975098,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7234] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4087181091308594
"时间：2025-06-26 19:19:26
内容：直接从你那提。

时间：2025-06-26 19:19:49
内容：5万不到。

时间：2025-06-26 19:21:15
内容：包牌，包税，包保险。

时间：2025-06-26 19:21:52
内容：我的车是15000，，那个产值3000。

时间：2025-06-26 19:27:55
内容：79800减12000减3000减15000--49800元",高配的加4000。,问价格,,0.16535282135009766,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7178] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4078950881958008
"时间：2025-06-26 19:19:49
内容：5万不到。

时间：2025-06-26 19:21:15
内容：包牌，包税，包保险。

时间：2025-06-26 19:21:52
内容：我的车是15000，，那个产值3000。

时间：2025-06-26 19:27:55
内容：79800减12000减3000减15000--49800元

时间：2025-06-26 19:29:11
内容：高配的加4000。",跟他还价了。,问优惠,,0.170090913772583,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7172] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.457399845123291
"时间：2025-06-26 19:21:15
内容：包牌，包税，包保险。

时间：2025-06-26 19:21:52
内容：我的车是15000，，那个产值3000。

时间：2025-06-26 19:27:55
内容：79800减12000减3000减15000--49800元

时间：2025-06-26 19:29:11
内容：高配的加4000。

时间：2025-06-26 19:29:22
内容：跟他还价了。",我们过年就开始了，我们从3月份就开始拉的。,none,,0.17186498641967773,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7268] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5625910758972168
"时间：2025-06-26 19:21:52
内容：我的车是15000，，那个产值3000。

时间：2025-06-26 19:27:55
内容：79800减12000减3000减15000--49800元

时间：2025-06-26 19:29:11
内容：高配的加4000。

时间：2025-06-26 19:29:22
内容：跟他还价了。

时间：2025-06-26 19:30:06
内容：我们过年就开始了，我们从3月份就开始拉的。",但是我到现在没有看到实际的车。,看车型,,0.17482328414916992,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7298] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.441478967666626
"时间：2025-06-26 19:27:55
内容：79800减12000减3000减15000--49800元

时间：2025-06-26 19:29:11
内容：高配的加4000。

时间：2025-06-26 19:29:22
内容：跟他还价了。

时间：2025-06-26 19:30:06
内容：我们过年就开始了，我们从3月份就开始拉的。

时间：2025-06-26 19:30:39
内容：但是我到现在没有看到实际的车。",不行我就买高配。,问配置,,0.17663788795471191,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7271] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.45758795738220215
"时间：2025-06-26 19:29:11
内容：高配的加4000。

时间：2025-06-26 19:29:22
内容：跟他还价了。

时间：2025-06-26 19:30:06
内容：我们过年就开始了，我们从3月份就开始拉的。

时间：2025-06-26 19:30:39
内容：但是我到现在没有看到实际的车。

时间：2025-06-26 19:31:15
内容：不行我就买高配。",我看到盐城上面那个车上说过了，就是59800。,问价格,,0.17023706436157227,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7334] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.38285088539123535
无历史消息,新狮铂拓界什么时候上,看车型,,0.16759204864501953,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6681] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.313687801361084
"时间：2025-06-26 19:29:22
内容：跟他还价了。

时间：2025-06-26 19:30:06
内容：我们过年就开始了，我们从3月份就开始拉的。

时间：2025-06-26 19:30:39
内容：但是我到现在没有看到实际的车。

时间：2025-06-26 19:31:15
内容：不行我就买高配。

时间：2025-06-26 19:33:34
内容：我看到盐城上面那个车上说过了，就是59800。",你贵姓,none,,0.16765117645263672,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7318] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5778839588165283
"时间：2025-06-26 19:30:06
内容：我们过年就开始了，我们从3月份就开始拉的。

时间：2025-06-26 19:30:39
内容：但是我到现在没有看到实际的车。

时间：2025-06-26 19:31:15
内容：不行我就买高配。

时间：2025-06-26 19:33:34
内容：我看到盐城上面那个车上说过了，就是59800。

时间：2025-06-26 19:34:22
内容：你贵姓",我这段时间忙，学生放假了，我可能过去。,none,,0.17217087745666504,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7396] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.40169596672058105
"时间：2025-06-26 19:30:39
内容：但是我到现在没有看到实际的车。

时间：2025-06-26 19:31:15
内容：不行我就买高配。

时间：2025-06-26 19:33:34
内容：我看到盐城上面那个车上说过了，就是59800。

时间：2025-06-26 19:34:22
内容：你贵姓

时间：2025-06-26 19:35:11
内容：我这段时间忙，学生放假了，我可能过去。",你看我的头像吗？我的头像是拿照相机的。,none,,0.16888904571533203,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7389] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.48029112815856934
"时间：2025-06-26 19:31:15
内容：不行我就买高配。

时间：2025-06-26 19:33:34
内容：我看到盐城上面那个车上说过了，就是59800。

时间：2025-06-26 19:34:22
内容：你贵姓

时间：2025-06-26 19:35:11
内容：我这段时间忙，学生放假了，我可能过去。

时间：2025-06-26 19:35:45
内容：你看我的头像吗？我的头像是拿照相机的。",搞摄影的。,none,,0.16832804679870605,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7329] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5930051803588867
"时间：2025-06-26 19:34:27
内容：[捂脸]",我只要便宜的,问价格,,0.17483019828796387,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6698] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.34004998207092285
"时间：2025-06-26 19:33:34
内容：我看到盐城上面那个车上说过了，就是59800。

时间：2025-06-26 19:34:22
内容：你贵姓

时间：2025-06-26 19:35:11
内容：我这段时间忙，学生放假了，我可能过去。

时间：2025-06-26 19:35:45
内容：你看我的头像吗？我的头像是拿照相机的。

时间：2025-06-26 19:36:12
内容：搞摄影的。",我在我们淮南市目前没有看到一台。,看车型,,0.18503093719482422,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7377] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.382321834564209
无历史消息,塞图斯舒适版我们当地没,看车型,,0.1856851577758789,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6687] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.5223181247711182
"时间：2025-06-26 19:34:27
内容：[捂脸]

时间：2025-06-26 19:36:23
内容：我只要便宜的",奕跑看看,看车型,,0.1702561378479004,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6783] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.4209868907928467
"时间：2025-06-26 19:34:22
内容：你贵姓

时间：2025-06-26 19:35:11
内容：我这段时间忙，学生放假了，我可能过去。

时间：2025-06-26 19:35:45
内容：你看我的头像吗？我的头像是拿照相机的。

时间：2025-06-26 19:36:12
内容：搞摄影的。

时间：2025-06-26 19:36:41
内容：我在我们淮南市目前没有看到一台。",我的牌照，我自己的车牌照保留，到时候是平移还是直接过户,问政策,,0.17878389358520508,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7426] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问置换,0.36190199851989746
"时间：2025-06-26 19:34:27
内容：[捂脸]

时间：2025-06-26 19:36:23
内容：我只要便宜的

时间：2025-06-26 19:37:39
内容：奕跑看看",什么价,问价格,,0.16667604446411133,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6862] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.3732168674468994
"时间：2025-06-26 19:34:27
内容：[捂脸]

时间：2025-06-26 19:36:23
内容：我只要便宜的

时间：2025-06-26 19:37:39
内容：奕跑看看

时间：2025-06-26 19:39:56
内容：什么价",顶是黑色的吗,问颜色,,0.1864480972290039,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6959] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问颜色,0.36635899543762207
"时间：2025-06-26 19:35:11
内容：我这段时间忙，学生放假了，我可能过去。

时间：2025-06-26 19:35:45
内容：你看我的头像吗？我的头像是拿照相机的。

时间：2025-06-26 19:36:12
内容：搞摄影的。

时间：2025-06-26 19:36:41
内容：我在我们淮南市目前没有看到一台。

时间：2025-06-26 19:38:24
内容：我的牌照，我自己的车牌照保留，到时候是平移还是直接过户",就要白的。,看车型,,0.17267084121704102,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7438] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问颜色,0.3883190155029297
"时间：2025-06-26 19:34:27
内容：[捂脸]

时间：2025-06-26 19:36:23
内容：我只要便宜的

时间：2025-06-26 19:37:39
内容：奕跑看看

时间：2025-06-26 19:39:56
内容：什么价

时间：2025-06-26 19:40:36
内容：顶是黑色的吗",三大件咋样,问配置,,0.16649889945983887,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7050] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.6004941463470459
"时间：2025-06-26 19:35:45
内容：你看我的头像吗？我的头像是拿照相机的。

时间：2025-06-26 19:36:12
内容：搞摄影的。

时间：2025-06-26 19:36:41
内容：我在我们淮南市目前没有看到一台。

时间：2025-06-26 19:38:24
内容：我的牌照，我自己的车牌照保留，到时候是平移还是直接过户

时间：2025-06-26 19:41:27
内容：就要白的。",我就定白色。,要买车,,0.1661219596862793,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7360] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问颜色,0.5426139831542969
"时间：2025-06-26 19:37:39
内容：塞图斯舒适版我们当地没",11.99最高优惠多少,问优惠,,0.18082404136657715,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6755] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.4283719062805176
"时间：2025-06-26 19:36:12
内容：搞摄影的。

时间：2025-06-26 19:36:41
内容：我在我们淮南市目前没有看到一台。

时间：2025-06-26 19:38:24
内容：我的牌照，我自己的车牌照保留，到时候是平移还是直接过户

时间：2025-06-26 19:41:27
内容：就要白的。

时间：2025-06-26 19:41:48
内容：我就定白色。",0,none,,0.18459510803222656,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7247] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.45633721351623535
"时间：2025-06-26 19:36:41
内容：我在我们淮南市目前没有看到一台。

时间：2025-06-26 19:38:24
内容：我的牌照，我自己的车牌照保留，到时候是平移还是直接过户

时间：2025-06-26 19:41:27
内容：就要白的。

时间：2025-06-26 19:41:48
内容：我就定白色。

时间：2025-06-26 19:42:10
内容：0",我直接定高配。,要买车,,0.18181490898132324,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7259] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.3463888168334961
无历史消息,没有索兰托是吧,看车型,,0.18143987655639648,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6663] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.35628175735473633
"时间：2025-06-26 19:36:23
内容：我只要便宜的

时间：2025-06-26 19:37:39
内容：奕跑看看

时间：2025-06-26 19:39:56
内容：什么价

时间：2025-06-26 19:40:36
内容：顶是黑色的吗

时间：2025-06-26 19:41:46
内容：三大件咋样",可是新款,看车型,,0.180833101272583,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7060] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.6709082126617432
"时间：2025-06-26 19:44:00
内容：没有索兰托是吧",新标好看,none,,0.17352604866027832,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6714] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.38404107093811035
"时间：2025-06-26 19:38:24
内容：我的牌照，我自己的车牌照保留，到时候是平移还是直接过户

时间：2025-06-26 19:41:27
内容：就要白的。

时间：2025-06-26 19:41:48
内容：我就定白色。

时间：2025-06-26 19:42:10
内容：0

时间：2025-06-26 19:42:17
内容：我直接定高配。",新标好看，可以展望未来。,none,,0.17766284942626953,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7235] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3756418228149414
"时间：2025-06-26 19:44:00
内容：没有索兰托是吧

时间：2025-06-26 19:44:48
内容：新标好看",说新标不好看的，他们眼光顶多到武大郎裤裆,看车型,,0.179182767868042,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6895] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3812720775604248
"时间：2025-06-26 19:44:00
内容：没有索兰托是吧

时间：2025-06-26 19:44:48
内容：新标好看

时间：2025-06-26 19:45:55
内容：说新标不好看的，他们眼光顶多到武大郎裤裆",特喜欢嘉华,看车型,,0.18124985694885254,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6986] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.6098568439483643
"时间：2025-06-26 19:37:39
内容：奕跑看看

时间：2025-06-26 19:39:56
内容：什么价

时间：2025-06-26 19:40:36
内容：顶是黑色的吗

时间：2025-06-26 19:41:46
内容：三大件咋样

时间：2025-06-26 19:44:08
内容：可是新款",我什么都看，合适就买,要买车,,0.1875460147857666,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7084] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",要买车,0.3621358871459961
"时间：2025-06-26 19:40:36
内容：顶是黑色的吗

时间：2025-06-26 19:41:46
内容：三大件咋样

时间：2025-06-26 19:44:08
内容：可是新款

时间：2025-06-26 19:46:46
内容：我什么都看，合适就买

时间：2025-06-26 19:47:09
内容：对的",和现代发动机是一个厂家吧,问配置,,0.1850109100341797,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7126] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.3401479721069336
无历史消息,嘉华还有吗？,看车型,,0.17510104179382324,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6657] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.7944810390472412
"时间：2025-06-26 19:49:54
内容：嘉华还有吗？",什么行情可以落地，置换＋贷款,问政策,,0.1799149513244629,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6768] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",金融政策,0.37113118171691895
"时间：2025-06-26 19:49:54
内容：嘉华还有吗？

时间：2025-06-26 19:50:11
内容：什么行情可以落地，置换＋贷款",嘉华,看车型,,0.17629027366638184,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6841] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.4394650459289551
无历史消息,[看]居然要工时费,问政策,,0.17299699783325195,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6665] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.6121459007263184
"时间：2025-06-26 19:52:23
内容：[看]居然要工时费",哦，去买的店，就不要工时费了是吧,问政策,,0.16857004165649414,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6788] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.288787841796875
"时间：2025-06-26 19:52:23
内容：[看]居然要工时费

时间：2025-06-26 19:53:31
内容：哦，去买的店，就不要工时费了是吧",得，还是要工时费,问政策,,0.16631698608398438,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6897] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4968869686126709
"时间：2025-06-26 19:52:23
内容：[看]居然要工时费

时间：2025-06-26 19:53:31
内容：哦，去买的店，就不要工时费了是吧

时间：2025-06-26 19:53:51
内容：得，还是要工时费",我以前买的傲跑，都是免费送三年六次保养的，不要工时费,问政策,,0.1661827564239502,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7114] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.37801313400268555
"时间：2025-06-26 19:52:23
内容：[看]居然要工时费

时间：2025-06-26 19:53:31
内容：哦，去买的店，就不要工时费了是吧

时间：2025-06-26 19:53:51
内容：得，还是要工时费

时间：2025-06-26 19:54:23
内容：我以前买的傲跑，都是免费送三年六次保养的，不要工时费",20年的,none,,0.1614069938659668,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7189] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.42249178886413574
"时间：2025-06-26 19:52:23
内容：[看]居然要工时费

时间：2025-06-26 19:53:31
内容：哦，去买的店，就不要工时费了是吧

时间：2025-06-26 19:53:51
内容：得，还是要工时费

时间：2025-06-26 19:54:23
内容：我以前买的傲跑，都是免费送三年六次保养的，不要工时费

时间：2025-06-26 19:54:45
内容：20年的",厂家送的保养,问政策,,0.16206073760986328,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7286] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问优惠,0.4988880157470703
"时间：2025-06-26 19:53:31
内容：哦，去买的店，就不要工时费了是吧

时间：2025-06-26 19:53:51
内容：得，还是要工时费

时间：2025-06-26 19:54:23
内容：我以前买的傲跑，都是免费送三年六次保养的，不要工时费

时间：2025-06-26 19:54:45
内容：20年的

时间：2025-06-26 19:55:51
内容：厂家送的保养",你们还要收工时费,问政策,,0.15392208099365234,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7290] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问政策,0.3933711051940918
"时间：2025-06-26 19:53:51
内容：得，还是要工时费

时间：2025-06-26 19:54:23
内容：我以前买的傲跑，都是免费送三年六次保养的，不要工时费

时间：2025-06-26 19:54:45
内容：20年的

时间：2025-06-26 19:55:51
内容：厂家送的保养

时间：2025-06-26 19:55:59
内容：你们还要收工时费",[抠鼻]得保养材料给我吧,none,,0.16266703605651855,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7256] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.6871039867401123
"时间：2025-06-26 19:54:23
内容：我以前买的傲跑，都是免费送三年六次保养的，不要工时费

时间：2025-06-26 19:54:45
内容：20年的

时间：2025-06-26 19:55:51
内容：厂家送的保养

时间：2025-06-26 19:55:59
内容：你们还要收工时费

时间：2025-06-26 19:56:32
内容：[抠鼻]得保养材料给我吧",我去路边给老板几十块钱，让他换,none,,0.1632518768310547,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7298] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3969409465789795
"时间：2025-06-26 19:54:45
内容：20年的

时间：2025-06-26 19:55:51
内容：厂家送的保养

时间：2025-06-26 19:55:59
内容：你们还要收工时费

时间：2025-06-26 19:56:32
内容：[抠鼻]得保养材料给我吧

时间：2025-06-26 19:56:47
内容：我去路边给老板几十块钱，让他换",明年吧[看],none,,0.16252899169921875,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7168] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3591921329498291
"时间：2025-06-26 19:55:51
内容：厂家送的保养

时间：2025-06-26 19:55:59
内容：你们还要收工时费

时间：2025-06-26 19:56:32
内容：[抠鼻]得保养材料给我吧

时间：2025-06-26 19:56:47
内容：我去路边给老板几十块钱，让他换

时间：2025-06-26 19:57:15
内容：明年吧[看]",让我试驾一下途胜和狮铂拓界,问试乘试驾,,0.16092991828918457,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7232] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问试乘试驾,0.6519548892974854
"时间：2025-06-26 19:41:27
内容：就要白的。

时间：2025-06-26 19:41:48
内容：我就定白色。

时间：2025-06-26 19:42:10
内容：0

时间：2025-06-26 19:42:17
内容：我直接定高配。

时间：2025-06-26 19:45:45
内容：新标好看，可以展望未来。",我来找的时候就找帅哥。,none,,0.15198278427124023,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7139] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.39430880546569824
"时间：2025-06-26 19:41:48
内容：我就定白色。

时间：2025-06-26 19:42:10
内容：0

时间：2025-06-26 19:42:17
内容：我直接定高配。

时间：2025-06-26 19:45:45
内容：新标好看，可以展望未来。

时间：2025-06-26 19:58:01
内容：我来找的时候就找帅哥。",最高的小刘。,none,,0.1544480323791504,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7145] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,1.0069403648376465
"时间：2025-06-26 19:42:10
内容：0

时间：2025-06-26 19:42:17
内容：我直接定高配。

时间：2025-06-26 19:45:45
内容：新标好看，可以展望未来。

时间：2025-06-26 19:58:01
内容：我来找的时候就找帅哥。

时间：2025-06-26 19:58:10
内容：最高的小刘。",我到合肥就找你。,none,,0.15479516983032227,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7157] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.5482180118560791
"时间：2025-06-26 19:42:17
内容：我直接定高配。

时间：2025-06-26 19:45:45
内容：新标好看，可以展望未来。

时间：2025-06-26 19:58:01
内容：我来找的时候就找帅哥。

时间：2025-06-26 19:58:10
内容：最高的小刘。

时间：2025-06-26 19:58:53
内容：我到合肥就找你。",蚌埠到我这只有50km。,none,,0.15624022483825684,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7208] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,3.3410561084747314
"时间：2025-06-26 19:45:45
内容：新标好看，可以展望未来。

时间：2025-06-26 19:58:01
内容：我来找的时候就找帅哥。

时间：2025-06-26 19:58:10
内容：最高的小刘。

时间：2025-06-26 19:58:53
内容：我到合肥就找你。

时间：2025-06-26 19:59:31
内容：蚌埠到我这只有50km。",我就在合肥定下来了。,要买车,,0.15580487251281738,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7226] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.44709086418151855
无历史消息,K5多少米,问价格,,0.1717538833618164,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6641] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.42021608352661133
"时间：2025-06-26 19:58:01
内容：我来找的时候就找帅哥。

时间：2025-06-26 19:58:10
内容：最高的小刘。

时间：2025-06-26 19:58:53
内容：我到合肥就找你。

时间：2025-06-26 19:59:31
内容：蚌埠到我这只有50km。

时间：2025-06-26 20:00:19
内容：我就在合肥定下来了。",家小孩在合肥呢。,none,,0.1714010238647461,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7202] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.47088003158569336
"时间：2025-06-26 20:00:23
内容：K5多少米",1.5,问配置,,0.1758420467376709,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6671] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.38025498390197754
"时间：2025-06-26 20:00:23
内容：K5多少米

时间：2025-06-26 20:00:49
内容：1.5

时间：2025-06-26 20:01:04
内容：有置换",标配多少落地,问价格,,0.17469310760498047,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6847] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.531588077545166
"时间：2025-06-26 20:00:23
内容：K5多少米

时间：2025-06-26 20:00:49
内容：1.5

时间：2025-06-26 20:01:04
内容：有置换

时间：2025-06-26 20:01:12
内容：标配多少落地",我没车 家里有,none,,0.1830430030822754,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6945] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3312199115753174
"时间：2025-06-26 20:00:23
内容：K5多少米

时间：2025-06-26 20:00:49
内容：1.5

时间：2025-06-26 20:01:04
内容：有置换

时间：2025-06-26 20:01:12
内容：标配多少落地

时间：2025-06-26 20:01:31
内容：我没车 家里有",用我家人名字买,问政策,,0.1790628433227539,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7048] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.29092931747436523
"时间：2025-06-26 20:00:49
内容：1.5

时间：2025-06-26 20:01:04
内容：有置换

时间：2025-06-26 20:01:12
内容：标配多少落地

时间：2025-06-26 20:01:31
内容：我没车 家里有

时间：2025-06-26 20:01:39
内容：用我家人名字买",大众,看车型,,0.17189621925354004,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7040] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.38334107398986816
无历史消息,拓界1.5,看车型,,0.17433404922485352,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6636] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3515489101409912
"时间：2025-06-26 20:03:57
内容：拓界1.5",加包,问配置,,0.17184066772460938,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6675] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.4132859706878662
"时间：2025-06-26 20:03:57
内容：拓界1.5

时间：2025-06-26 20:04:01
内容：加包

时间：2025-06-26 20:04:10
内容：有",尊贵加包,问配置,,0.17956209182739258,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6827] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.3580281734466553
无历史消息,这车下半年上改款吗,看车型,,0.17881512641906738,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6675] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.34633588790893555
无历史消息,拓界都这价了[发呆],问价格,,0.1655869483947754,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6671] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.44608426094055176
"时间：2025-06-26 20:06:07
内容：拓界都这价了[发呆]",赛图斯中配全款要多少了,问价格,,0.15907597541809082,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6764] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.3885841369628906
"时间：2025-06-26 20:06:07
内容：拓界都这价了[发呆]

时间：2025-06-26 20:07:30
内容：赛图斯中配全款要多少了

时间：2025-06-26 20:07:41
内容：没",有个老k2还在用[捂脸],想卖车,,0.16860389709472656,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6944] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3692901134490967
无历史消息,有没有4万左右的车,看车型,,0.17002081871032715,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6670] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.5540599822998047
"时间：2025-06-26 19:58:10
内容：最高的小刘。

时间：2025-06-26 19:58:53
内容：我到合肥就找你。

时间：2025-06-26 19:59:31
内容：蚌埠到我这只有50km。

时间：2025-06-26 20:00:19
内容：我就在合肥定下来了。

时间：2025-06-26 20:00:29
内容：家小孩在合肥呢。",塞图斯也好看。,看车型,,0.1585841178894043,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7178] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3733997344970703
"时间：2025-06-26 20:06:07
内容：拓界都这价了[发呆]

时间：2025-06-26 20:07:30
内容：赛图斯中配全款要多少了

时间：2025-06-26 20:07:41
内容：没

时间：2025-06-26 20:08:21
内容：有个老k2还在用[捂脸]",赛图斯家用代步很不错的,问配置,,0.172684907913208,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7071] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.33042192459106445
"时间：2025-06-26 19:58:53
内容：我到合肥就找你。

时间：2025-06-26 19:59:31
内容：蚌埠到我这只有50km。

时间：2025-06-26 20:00:19
内容：我就在合肥定下来了。

时间：2025-06-26 20:00:29
内容：家小孩在合肥呢。

时间：2025-06-26 20:10:48
内容：塞图斯也好看。",你已经达到专业的级别了。,none,,0.1797487735748291,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7214] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4003138542175293
"时间：2025-06-26 20:06:07
内容：拓界都这价了[发呆]

时间：2025-06-26 20:07:30
内容：赛图斯中配全款要多少了

时间：2025-06-26 20:07:41
内容：没

时间：2025-06-26 20:08:21
内容：有个老k2还在用[捂脸]

时间：2025-06-26 20:11:25
内容：赛图斯家用代步很不错的",我就喜欢这样的销售，不喜欢花里胡哨的[捂脸],none,,0.2504591941833496,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7254] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.6515200138092041
"时间：2025-06-26 20:07:30
内容：赛图斯中配全款要多少了

时间：2025-06-26 20:07:41
内容：没

时间：2025-06-26 20:08:21
内容：有个老k2还在用[捂脸]

时间：2025-06-26 20:11:25
内容：赛图斯家用代步很不错的

时间：2025-06-26 20:12:17
内容：我就喜欢这样的销售，不喜欢花里胡哨的[捂脸]",有对象吗[捂脸],撩妹,,0.17605972290039062,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7242] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",撩妹,0.3605539798736572
"时间：2025-06-26 20:07:41
内容：没

时间：2025-06-26 20:08:21
内容：有个老k2还在用[捂脸]

时间：2025-06-26 20:11:25
内容：赛图斯家用代步很不错的

时间：2025-06-26 20:12:17
内容：我就喜欢这样的销售，不喜欢花里胡哨的[捂脸]

时间：2025-06-26 20:12:41
内容：有对象吗[捂脸]",脸皮挺厚[捂脸],撩妹,,0.17725014686584473,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7214] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",撩妹,0.4224510192871094
"时间：2025-06-26 20:08:21
内容：有个老k2还在用[捂脸]

时间：2025-06-26 20:11:25
内容：赛图斯家用代步很不错的

时间：2025-06-26 20:12:17
内容：我就喜欢这样的销售，不喜欢花里胡哨的[捂脸]

时间：2025-06-26 20:12:41
内容：有对象吗[捂脸]

时间：2025-06-26 20:13:26
内容：脸皮挺厚[捂脸]",[捂脸][捂脸],none,,0.16845965385437012,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7236] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",撩妹,0.4162158966064453
无历史消息,哇，小姐姐好漂亮，讲车讲的很好,撩妹,,0.1660761833190918,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6711] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",撩妹,0.3252711296081543
"时间：2025-06-27 10:16:33
内容：哇，小姐姐好漂亮，讲车讲的很好",爱了爱了，我已经关注了,none,,0.16845393180847168,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6804] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",撩妹,0.45929861068725586
无历史消息,好车,none,,0.1765730381011963,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6633] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3908200263977051
无历史消息,漂亮,撩妹,,0.18379712104797363,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6633] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3962361812591553
"时间：2025-06-27 10:20:31
内容：[爱心][爱心][爱心][爱心][爱心]",好看,撩妹,,0.1759171485900879,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6730] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3776438236236572
无历史消息,己预约,问试乘试驾,,0.1738300323486328,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6639] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3708038330078125
无历史消息,直播气质和车真般配,撩妹,,0.16663002967834473,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6675] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.43346500396728516
无历史消息,美女讲的真好,撩妹,,0.16895627975463867,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6657] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",撩妹,0.5146710872650146
"时间：2025-06-27 10:33:34
内容：上午好",支持国产车,none,,0.16728711128234863,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6696] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.41029906272888184
无历史消息,好车,none,,0.18060898780822754,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6633] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4107201099395752
"时间：2025-06-27 10:33:34
内容：上午好

时间：2025-06-27 10:33:53
内容：支持国产车",高端大气上档次,none,,0.18022823333740234,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6799] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3600280284881592
无历史消息,Q6现在多少钱啊,问价格,,0.18628597259521484,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6659] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.551506757736206
"时间：2025-06-27 10:40:41
内容：[赞][赞][赞][赞]",已预约,问试乘试驾,,0.17484092712402344,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6698] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.38221025466918945
无历史消息,车子不错,none,,0.1754920482635498,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6645] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.34068727493286133
"时间：2025-06-27 10:42:03
内容：车子不错",好车,none,,0.17341184616088867,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6684] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.44584107398986816
无历史消息,香车美女,撩妹,,0.17206621170043945,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6645] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",撩妹,0.3232760429382324
无历史消息,a7l45rs竞速套件全款落地多少钱,问价格,,0.17734885215759277,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6694] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.34970593452453613
"时间：2025-06-27 10:56:30
内容：a7l45rs竞速套件全款落地多少钱",能改通风和抬显嘛,问置换,,0.18463683128356934,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6769] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.7370727062225342
"时间：2025-06-27 10:34:46
内容：@饭饭姐 请点击下方小风车留资，会有专人给您回复哦~

时间：2025-06-27 10:40:54
内容：@和平岁月 请点击下方小风车留资，会有专人给您回复哦~",@Regret. 请点击下方小风车留资，会有专人给您回复哦~,none,,0.18493294715881348,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7127] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3781721591949463
"时间：2025-06-27 10:56:30
内容：a7l45rs竞速套件全款落地多少钱

时间：2025-06-27 10:56:38
内容：能改通风和抬显嘛",我没办法预约,问试乘试驾,,0.17936086654663086,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6866] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.34971094131469727
"时间：2025-06-27 10:56:30
内容：a7l45rs竞速套件全款落地多少钱

时间：2025-06-27 10:56:38
内容：能改通风和抬显嘛

时间：2025-06-27 10:57:08
内容：我没办法预约",手机号有一点点问题,问联系方式,,0.1875619888305664,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6981] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.46103811264038086
"时间：2025-06-27 10:56:30
内容：a7l45rs竞速套件全款落地多少钱

时间：2025-06-27 10:56:38
内容：能改通风和抬显嘛

时间：2025-06-27 10:57:08
内容：我没办法预约

时间：2025-06-27 10:57:17
内容：手机号有一点点问题",我私信了,问联系方式,,0.18255114555358887,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7066] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.41431212425231934
"时间：2025-06-27 10:56:30
内容：a7l45rs竞速套件全款落地多少钱

时间：2025-06-27 10:56:38
内容：能改通风和抬显嘛

时间：2025-06-27 10:57:08
内容：我没办法预约

时间：2025-06-27 10:57:17
内容：手机号有一点点问题

时间：2025-06-27 10:57:24
内容：我私信了",我私信了姐姐,none,,0.17053961753845215,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7163] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5563807487487793
"时间：2025-06-27 10:56:38
内容：能改通风和抬显嘛

时间：2025-06-27 10:57:08
内容：我没办法预约

时间：2025-06-27 10:57:17
内容：手机号有一点点问题

时间：2025-06-27 10:57:24
内容：我私信了

时间：2025-06-27 10:57:53
内容：我私信了姐姐",OK了姐姐 我一号去润华宝马保养 顺便去看看车,问试乘试驾,,0.17480015754699707,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7208] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.40131592750549316
"时间：2025-06-27 10:57:08
内容：我没办法预约

时间：2025-06-27 10:57:17
内容：手机号有一点点问题

时间：2025-06-27 10:57:24
内容：我私信了

时间：2025-06-27 10:57:53
内容：我私信了姐姐

时间：2025-06-27 10:58:29
内容：OK了姐姐 我一号去润华宝马保养 顺便去看看车",7.1号过去,问试乘试驾,,0.1812610626220703,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7181] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4731431007385254
"时间：2025-06-27 10:57:17
内容：手机号有一点点问题

时间：2025-06-27 10:57:24
内容：我私信了

时间：2025-06-27 10:57:53
内容：我私信了姐姐

时间：2025-06-27 10:58:29
内容：OK了姐姐 我一号去润华宝马保养 顺便去看看车

时间：2025-06-27 10:58:56
内容：7.1号过去",我一号去保养 不是一会去保养姐姐[捂脸],none,,0.17771410942077637,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7250] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.40033817291259766
"时间：2025-06-27 10:57:24
内容：我私信了

时间：2025-06-27 10:57:53
内容：我私信了姐姐

时间：2025-06-27 10:58:29
内容：OK了姐姐 我一号去润华宝马保养 顺便去看看车

时间：2025-06-27 10:58:56
内容：7.1号过去

时间：2025-06-27 10:59:37
内容：我一号去保养 不是一会去保养姐姐[捂脸]",您可能看错了,none,,0.18769001960754395,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7232] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.33343505859375
"时间：2025-06-27 10:57:53
内容：我私信了姐姐

时间：2025-06-27 10:58:29
内容：OK了姐姐 我一号去润华宝马保养 顺便去看看车

时间：2025-06-27 10:58:56
内容：7.1号过去

时间：2025-06-27 10:59:37
内容：我一号去保养 不是一会去保养姐姐[捂脸]

时间：2025-06-27 10:59:45
内容：您可能看错了",好的,none,,0.18461203575134277,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7220] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3942737579345703
"时间：2025-06-27 10:58:29
内容：OK了姐姐 我一号去润华宝马保养 顺便去看看车

时间：2025-06-27 10:58:56
内容：7.1号过去

时间：2025-06-27 10:59:37
内容：我一号去保养 不是一会去保养姐姐[捂脸]

时间：2025-06-27 10:59:45
内容：您可能看错了

时间：2025-06-27 11:00:03
内容：好的",1号见,none,,0.1813039779663086,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7197] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.44905591011047363
无历史消息,德清当地的丰田4S店,问地址,,0.16564512252807617,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6671] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,0.33473825454711914
无历史消息,rav4什么行情,问价格,,0.16147208213806152,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6649] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,4.0632781982421875
无历史消息,德清当地的丰田4S店,问地址,,0.15247321128845215,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6671] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问地址,1.3189351558685303
无历史消息,亚洲龙低配多少钱,问价格,,0.16570377349853516,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6669] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.36629700660705566
无历史消息,还有新闻工作者,none,,0.15920019149780273,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6663] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.5091819763183594
无历史消息,焕驰现在啥价,问价格,,0.163344144821167,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6657] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.45955920219421387
无历史消息,钢材多少兆帕,问配置,,0.1639261245727539,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6657] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.31962108612060547
无历史消息,什么车,看车型,,0.1634960174560547,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6639] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.3833811283111572
"时间：2025-06-27 15:29:18
内容：什么车",落地多少,问价格,,0.172501802444458,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6690] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.4823341369628906
"时间：2025-06-27 15:29:18
内容：什么车

时间：2025-06-27 15:29:41
内容：落地多少",我有14年福瑞迪,想卖车,,0.16411995887756348,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6789] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.3834712505340576
"时间：2025-06-27 15:28:32
内容：钢材多少兆帕",这是什么配置,问配置,,0.152479887008667,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6720] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.5289390087127686
"时间：2025-06-27 15:28:32
内容：钢材多少兆帕

时间：2025-06-27 15:30:26
内容：这是什么配置",如果全款大概多少落地,问价格,,0.14797401428222656,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6841] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.34590983390808105
"时间：2025-06-27 15:28:32
内容：钢材多少兆帕

时间：2025-06-27 15:30:26
内容：这是什么配置

时间：2025-06-27 15:31:10
内容：如果全款大概多少落地",豪华版呢,问价格,,0.18145990371704102,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6926] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",看车型,0.34325313568115234
"时间：2025-06-27 15:28:32
内容：钢材多少兆帕

时间：2025-06-27 15:30:26
内容：这是什么配置

时间：2025-06-27 15:31:10
内容：如果全款大概多少落地

时间：2025-06-27 15:31:32
内容：豪华版呢",要手续费吧,问政策,,0.15691184997558594,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7017] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",金融政策,0.43758702278137207
"时间：2025-06-27 15:28:32
内容：钢材多少兆帕

时间：2025-06-27 15:30:26
内容：这是什么配置

时间：2025-06-27 15:31:10
内容：如果全款大概多少落地

时间：2025-06-27 15:31:32
内容：豪华版呢

时间：2025-06-27 15:31:58
内容：要手续费吧",现在这么透明了嘛,none,,0.16464519500732422,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7126] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4088413715362549
"时间：2025-06-27 15:30:26
内容：这是什么配置

时间：2025-06-27 15:31:10
内容：如果全款大概多少落地

时间：2025-06-27 15:31:32
内容：豪华版呢

时间：2025-06-27 15:31:58
内容：要手续费吧

时间：2025-06-27 15:32:18
内容：现在这么透明了嘛",城市油耗多少,问配置,,0.16439127922058105,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 7126] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问配置,0.5583438873291016
无历史消息,你好 嘉华高配落地几个,问价格,,0.16354012489318848,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6682] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",问价格,0.5367109775543213
"时间：2025-06-27 15:14:10
内容：还有新闻工作者",52,none,,0.163970947265625,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6692] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.36193108558654785
"时间：2025-06-27 15:14:10
内容：还有新闻工作者

时间：2025-06-27 15:33:28
内容：52",保时捷,看车型,,0.16690301895141602,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6771] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.34755396842956543
无历史消息,这家不行，,none,,0.16649484634399414,"HTTP 400: {""error"":{""message"":""JSON parse error: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot construct instance of `com.wd.paas.api.domain.v4.chat.ChatCompletionRequest$Thinking` (although at least one Creator exists): no boolean/Boolean-argument constructor/factory method to deserialize from boolean value (true)\n at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 6651] (through reference chain: com.wd.paas.api.domain.v4.chat.ChatCompletionRequest[\""thinking\""])"",""code"":""400""}}",None,0.4486348628997803
