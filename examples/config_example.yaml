# AI模型评估系统 - 配置示例
# 此文件展示了各种配置选项和最佳实践

# 提供商配置
providers:
  # OpenAI提供商
  - id: openai
    name: OpenAI
    base_url: https://api.openai.com/v1/
    api_key: ${OPENAI_API_KEY}  # 使用环境变量
    is_active: true

  # Anthropic提供商
  - id: anthropic
    name: Anthropic
    base_url: https://api.anthropic.com/v1/
    api_key: ${ANTHROPIC_API_KEY}
    is_active: true

  # 自定义提供商示例（例如，本地部署）
  - id: local_llm
    name: 本地LLM服务器
    base_url: http://localhost:8000/v1/
    api_key: local-api-key
    is_active: false  # 默认禁用

  # Azure OpenAI示例
  - id: azure_openai
    name: Azure OpenAI
    base_url: https://your-resource.openai.azure.com/openai/deployments/
    api_key: ${AZURE_OPENAI_API_KEY}
    is_active: false

# 模型配置
models:
  # OpenAI模型
  - id: gpt4_turbo
    provider_id: openai
    model_name: gpt-4-turbo-preview
    display_name: GPT-4 Turbo
    temperature: 0.7
    thinking_enabled: false
    max_tokens: 4096

  - id: gpt4
    provider_id: openai
    model_name: gpt-4
    display_name: GPT-4
    temperature: 0.7
    thinking_enabled: false
    max_tokens: 8192

  - id: gpt35_turbo
    provider_id: openai
    model_name: gpt-3.5-turbo
    display_name: GPT-3.5 Turbo
    temperature: 0.7
    thinking_enabled: false
    max_tokens: 4096

  # Anthropic模型
  - id: claude3_opus
    provider_id: anthropic
    model_name: claude-3-opus-20240229
    display_name: Claude 3 Opus
    temperature: 0.7
    thinking_enabled: true
    max_tokens: 4096

  - id: claude3_sonnet
    provider_id: anthropic
    model_name: claude-3-sonnet-20240229
    display_name: Claude 3 Sonnet
    temperature: 0.7
    thinking_enabled: true
    max_tokens: 4096

  - id: claude3_haiku
    provider_id: anthropic
    model_name: claude-3-haiku-20240307
    display_name: Claude 3 Haiku
    temperature: 0.7
    thinking_enabled: false
    max_tokens: 4096

  # 自定义模型示例
  - id: local_model
    provider_id: local_llm
    model_name: llama-2-7b-chat
    display_name: Llama 2 7B Chat
    temperature: 0.8
    thinking_enabled: false
    max_tokens: 2048

# 提示模板配置
prompt_templates:
  # 基础问答模板
  - id: qa_basic
    name: 基础问答
    template: |
      问题: {original_prompt}
      
      请提供清晰准确的答案。
    variables: [original_prompt]
    description: 简单的问答模板

  # 带上下文的问答模板
  - id: qa_with_context
    name: 带上下文的问答
    template: |
      问题: {original_prompt}
      
      上下文A: {variable_a}
      上下文B: {variable_b}
      
      基于提供的上下文，请提供清晰准确的答案。
    variables: [original_prompt, variable_a, variable_b]
    description: 带有额外上下文的问答模板

  # 比较模板
  - id: comparison
    name: 比较任务
    template: |
      比较以下两个选项:
      
      选项A: {variable_a}
      选项B: {variable_b}
      
      问题: {original_prompt}
      
      请提供详细的比较和建议。
    variables: [original_prompt, variable_a, variable_b]
    description: 用于比较任务的模板

  # 翻译模板
  - id: translation
    name: 翻译任务
    template: |
      将以下文本从{variable_a}翻译为{variable_b}:
      
      文本: {original_prompt}
      
      翻译:
    variables: [original_prompt, variable_a, variable_b]
    description: 用于翻译任务的模板

  # 代码生成模板
  - id: code_generation
    name: 代码生成
    template: |
      编程语言: {variable_a}
      需求: {original_prompt}
      
      附加上下文: {variable_b}
      
      请生成满足需求的清洁、有良好注释的代码。
      
      代码:
    variables: [original_prompt, variable_a, variable_b]
    description: 用于代码生成任务的模板

  # 创意写作模板
  - id: creative_writing
    name: 创意写作
    template: |
      类型: {variable_a}
      主题: {variable_b}
      提示: {original_prompt}
      
      请基于以上参数写一篇创意作品。
    variables: [original_prompt, variable_a, variable_b]
    description: 用于创意写作任务的模板

  # 分析模板
  - id: analysis
    name: 分析任务
    template: |
      主题: {original_prompt}
      
      数据/信息A: {variable_a}
      数据/信息B: {variable_b}
      
      请考虑所有提供的信息进行全面分析。
      包括关键见解、模式和结论。
    variables: [original_prompt, variable_a, variable_b]
    description: 用于分析任务的模板

  # 推理模板
  - id: reasoning
    name: 逻辑推理
    template: |
      问题: {original_prompt}
      
      给定信息:
      - {variable_a}
      - {variable_b}
      
      请逐步解决这个问题，展示您的推理过程。
    variables: [original_prompt, variable_a, variable_b]
    description: 用于逻辑推理任务的模板