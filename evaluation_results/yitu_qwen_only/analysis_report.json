{"file_info": {"result_file": "/Users/<USER>/kiro/对比/ai-model-evaluation/evaluation_results/yitu_qwen_only/yitu_qwen_only_results.csv", "total_rows": 345, "columns": ["history", "live_comment", "expected_result", "qwen-plus_result", "qwen-plus_execution_time"]}, "model_performance": {"expected": {"total_requests": 345, "successful_requests": 345, "success_rate": 1.0, "average_execution_time": 0.0, "result_distribution": {"none": 107, "看车型": 51, "问价格": 45, "问配置": 35, "问政策": 20, "问地址": 17, "问优惠": 16, "问试乘试驾": 13, "要买车": 11, "撩妹": 10, "问置换": 8, "想卖车": 4, "问联系方式": 3, "金融政策": 2, "问颜色": 2, "问库存车": 1}}, "qwen-plus": {"total_requests": 345, "successful_requests": 199, "success_rate": 0.5768115942028985, "average_execution_time": 0.473674492214037, "result_distribution": {"问价格": 41, "看车型": 31, "问地址": 27, "问配置": 25, "问优惠": 21, "要买车": 10, "问政策": 9, "撩妹": 8, "问置换": 7, "问试乘试驾": 5, "金融政策": 5, "问颜色": 5, "问库存车": 2, "问车型": 1, "问联系方式": 1, "想卖车": 1}}}}