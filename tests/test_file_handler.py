"""
Tests for file handler service.
"""

import tempfile
import os
from pathlib import Path

import pandas as pd
import pytest

from ai_model_evaluation.services.file_handler import <PERSON>Handler
from ai_model_evaluation.models.core import EvaluationRow, ResultRow
from ai_model_evaluation.utils.exceptions import FileProcessingError


class TestFileHandler:
    """Test FileHandler class."""
    
    def create_test_csv(self, data=None, filename="test.csv"):
        """Create a temporary CSV file with test data."""
        if data is None:
            data = {
                'original_prompt': ['Prompt 1', 'Prompt 2', 'Prompt 3'],
                'variable_a': ['Value A1', 'Value A2', 'Value A3'],
                'variable_b': ['Value B1', 'Value B2', 'Value B3'],
                'expected_result': ['Expected 1', 'Expected 2', 'Expected 3']
            }
        
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            return f.name
    
    def create_test_excel(self, data=None, filename="test.xlsx"):
        """Create a temporary Excel file with test data."""
        if data is None:
            data = {
                'original_prompt': ['Prompt 1', 'Prompt 2', 'Prompt 3'],
                'variable_a': ['Value A1', 'Value A2', 'Value A3'],
                'variable_b': ['Value B1', 'Value B2', 'Value B3'],
                'expected_result': ['Expected 1', 'Expected 2', 'Expected 3']
            }
        
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            df.to_excel(f.name, index=False, engine='openpyxl')
            return f.name
    
    def test_read_csv_file(self):
        """Test reading CSV file."""
        csv_path = self.create_test_csv()
        
        try:
            handler = FileHandler()
            rows = handler.read_evaluation_data(csv_path)
            
            assert len(rows) == 3
            assert isinstance(rows[0], EvaluationRow)
            
            assert rows[0].original_prompt == 'Prompt 1'
            assert rows[0].variable_a == 'Value A1'
            assert rows[0].variable_b == 'Value B1'
            assert rows[0].expected_result == 'Expected 1'
            
            assert rows[1].original_prompt == 'Prompt 2'
            assert rows[2].original_prompt == 'Prompt 3'
            
        finally:
            os.unlink(csv_path)
    
    def test_read_excel_file(self):
        """Test reading Excel file."""
        excel_path = self.create_test_excel()
        
        try:
            handler = FileHandler()
            rows = handler.read_evaluation_data(excel_path)
            
            assert len(rows) == 3
            assert isinstance(rows[0], EvaluationRow)
            
            assert rows[0].original_prompt == 'Prompt 1'
            assert rows[0].variable_a == 'Value A1'
            assert rows[0].variable_b == 'Value B1'
            assert rows[0].expected_result == 'Expected 1'
            
        finally:
            os.unlink(excel_path)
    
    def test_read_file_without_expected_result(self):
        """Test reading file without expected_result column."""
        data = {
            'original_prompt': ['Prompt 1', 'Prompt 2'],
            'variable_a': ['Value A1', 'Value A2'],
            'variable_b': ['Value B1', 'Value B2']
        }
        csv_path = self.create_test_csv(data)
        
        try:
            handler = FileHandler()
            rows = handler.read_evaluation_data(csv_path)
            
            assert len(rows) == 2
            assert rows[0].expected_result is None
            assert rows[1].expected_result is None
            
        finally:
            os.unlink(csv_path)
    
    def test_read_nonexistent_file(self):
        """Test reading nonexistent file raises error."""
        handler = FileHandler()
        
        with pytest.raises(FileProcessingError, match="File not found"):
            handler.read_evaluation_data("/nonexistent/file.csv")
    
    def test_read_unsupported_file_format(self):
        """Test reading unsupported file format raises error."""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write(b"some text content")
            txt_path = f.name
        
        try:
            handler = FileHandler()
            
            with pytest.raises(FileProcessingError, match="Unsupported file format"):
                handler.read_evaluation_data(txt_path)
                
        finally:
            os.unlink(txt_path)
    
    def test_read_file_missing_required_columns(self):
        """Test reading file with missing required columns."""
        data = {
            'original_prompt': ['Prompt 1', 'Prompt 2'],
            'variable_a': ['Value A1', 'Value A2']
            # Missing variable_b
        }
        csv_path = self.create_test_csv(data)
        
        try:
            handler = FileHandler()
            
            with pytest.raises(FileProcessingError, match="Missing required columns"):
                handler.read_evaluation_data(csv_path)
                
        finally:
            os.unlink(csv_path)
    
    def test_validate_file_format_valid(self):
        """Test validating valid file format."""
        csv_path = self.create_test_csv()
        
        try:
            handler = FileHandler()
            result = handler.validate_file_format(csv_path)
            
            assert result.is_valid is True
            assert result.row_count == 3
            assert len(result.column_names) == 4
            assert 'original_prompt' in result.column_names
            assert 'variable_a' in result.column_names
            assert 'variable_b' in result.column_names
            assert 'expected_result' in result.column_names
            
        finally:
            os.unlink(csv_path)
    
    def test_validate_file_format_invalid(self):
        """Test validating invalid file format."""
        data = {
            'wrong_column': ['Value 1', 'Value 2'],
            'another_wrong': ['Value A', 'Value B']
        }
        csv_path = self.create_test_csv(data)
        
        try:
            handler = FileHandler()
            result = handler.validate_file_format(csv_path)
            
            assert result.is_valid is False
            assert len(result.errors) > 0
            assert any("Missing required columns" in error for error in result.errors)
            
        finally:
            os.unlink(csv_path)
    
    def test_validate_file_format_with_extra_columns(self):
        """Test validating file with extra columns."""
        data = {
            'original_prompt': ['Prompt 1', 'Prompt 2'],
            'variable_a': ['Value A1', 'Value A2'],
            'variable_b': ['Value B1', 'Value B2'],
            'expected_result': ['Expected 1', 'Expected 2'],
            'extra_column': ['Extra 1', 'Extra 2']
        }
        csv_path = self.create_test_csv(data)
        
        try:
            handler = FileHandler()
            result = handler.validate_file_format(csv_path)
            
            assert result.is_valid is True
            assert len(result.warnings) > 0
            assert any("Extra columns found" in warning for warning in result.warnings)
            
        finally:
            os.unlink(csv_path)
    
    def test_write_results_csv(self):
        """Test writing results to CSV file."""
        # Create test results
        results = [
            ResultRow(
                original_prompt="Prompt 1",
                variable_a="Value A1",
                variable_b="Value B1",
                expected_result="Expected 1",
                model_results={"model1": "Result 1", "model2": "Result 2"},
                execution_time={"model1": 1.5, "model2": 2.0},
                error_info={"model1": None, "model2": None}
            ),
            ResultRow(
                original_prompt="Prompt 2",
                variable_a="Value A2",
                variable_b="Value B2",
                expected_result="Expected 2",
                model_results={"model1": "Result 3", "model2": "Result 4"},
                execution_time={"model1": 1.2, "model2": 1.8},
                error_info={"model1": None, "model2": "Some error"}
            )
        ]
        
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
            output_path = f.name
        
        try:
            handler = FileHandler()
            success = handler.write_results(results, output_path)
            
            assert success is True
            assert Path(output_path).exists()
            
            # Verify content
            df = pd.read_csv(output_path)
            assert len(df) == 2
            assert 'original_prompt' in df.columns
            assert 'model1_result' in df.columns
            assert 'model2_result' in df.columns
            assert 'model1_execution_time' in df.columns
            assert 'model2_execution_time' in df.columns
            
            assert df.iloc[0]['original_prompt'] == 'Prompt 1'
            assert df.iloc[0]['model1_result'] == 'Result 1'
            assert df.iloc[0]['model2_result'] == 'Result 2'
            assert df.iloc[1]['model2_error'] == 'Some error'
            
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    def test_write_results_excel(self):
        """Test writing results to Excel file."""
        results = [
            ResultRow(
                original_prompt="Prompt 1",
                variable_a="Value A1",
                variable_b="Value B1",
                expected_result="Expected 1",
                model_results={"model1": "Result 1"},
                execution_time={"model1": 1.5},
                error_info={"model1": None}
            )
        ]
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            output_path = f.name
        
        try:
            handler = FileHandler()
            success = handler.write_results(results, output_path)
            
            assert success is True
            assert Path(output_path).exists()
            
            # Verify content
            df = pd.read_excel(output_path, engine='openpyxl')
            assert len(df) == 1
            assert 'original_prompt' in df.columns
            assert 'model1_result' in df.columns
            
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    def test_write_empty_results(self):
        """Test writing empty results raises error."""
        handler = FileHandler()
        
        with pytest.raises(FileProcessingError, match="No results to write"):
            handler.write_results([], "output.csv")
    
    def test_get_file_info_existing_file(self):
        """Test getting file info for existing file."""
        csv_path = self.create_test_csv()
        
        try:
            handler = FileHandler()
            info = handler.get_file_info(csv_path)
            
            assert info['exists'] is True
            assert info['extension'] == '.csv'
            assert info['is_supported'] is True
            assert info['row_count'] == 3
            assert info['column_count'] == 4
            assert info['is_valid'] is True
            assert 'original_prompt' in info['columns']
            
        finally:
            os.unlink(csv_path)
    
    def test_get_file_info_nonexistent_file(self):
        """Test getting file info for nonexistent file."""
        handler = FileHandler()
        info = handler.get_file_info("/nonexistent/file.csv")
        
        assert info['exists'] is False
        assert info['size'] == 0
        assert info['extension'] == '.csv'
        assert info['is_supported'] is False
        assert info['row_count'] == 0
        assert info['column_count'] == 0
    
    def test_read_file_with_empty_cells(self):
        """Test reading file with empty cells."""
        data = {
            'original_prompt': ['Prompt 1', '', 'Prompt 3'],
            'variable_a': ['Value A1', 'Value A2', ''],
            'variable_b': ['Value B1', 'Value B2', 'Value B3'],
            'expected_result': ['Expected 1', None, 'Expected 3']
        }
        csv_path = self.create_test_csv(data)
        
        try:
            handler = FileHandler()
            rows = handler.read_evaluation_data(csv_path)
            
            assert len(rows) == 3
            assert rows[0].original_prompt == 'Prompt 1'
            assert rows[1].original_prompt == ''  # Empty string
            assert rows[2].variable_a == ''       # Empty string
            assert rows[1].expected_result is None
            
        finally:
            os.unlink(csv_path)
    
    def test_create_results_summary(self):
        """Test creating results summary."""
        results = [
            ResultRow(
                original_prompt="Prompt 1",
                variable_a="Value A1",
                variable_b="Value B1",
                expected_result="Expected 1",
                model_results={"model1": "Result 1", "model2": "Result 2"},
                execution_time={"model1": 1.5, "model2": 2.0},
                error_info={"model1": None, "model2": None}
            ),
            ResultRow(
                original_prompt="Prompt 2",
                variable_a="Value A2",
                variable_b="Value B2",
                expected_result="Expected 2",
                model_results={"model1": "Result 3", "model2": "Result 4"},
                execution_time={"model1": 1.2, "model2": 1.8},
                error_info={"model1": None, "model2": "API Error"}
            )
        ]
        
        handler = FileHandler()
        summary = handler.create_results_summary(results)
        
        assert summary['total_rows'] == 2
        assert summary['model_count'] == 2
        assert summary['overall_success_rate'] == 0.75  # 3 out of 4 requests successful
        
        # Check model1 metrics
        model1_metrics = summary['model_metrics']['model1']
        assert model1_metrics['total_requests'] == 2
        assert model1_metrics['successful_requests'] == 2
        assert model1_metrics['failed_requests'] == 0
        assert model1_metrics['success_rate'] == 1.0
        assert model1_metrics['avg_execution_time'] == 1.35  # (1.5 + 1.2) / 2
        
        # Check model2 metrics
        model2_metrics = summary['model_metrics']['model2']
        assert model2_metrics['total_requests'] == 2
        assert model2_metrics['successful_requests'] == 1
        assert model2_metrics['failed_requests'] == 1
        assert model2_metrics['success_rate'] == 0.5
        assert model2_metrics['avg_execution_time'] == 1.9  # (2.0 + 1.8) / 2
    
    def test_export_analysis_report_csv(self):
        """Test exporting analysis report to CSV."""
        analysis_data = {
            'summary': {
                'total_rows': 10,
                'model_count': 2,
                'overall_success_rate': 0.85
            },
            'model_metrics': {
                'model1': {
                    'success_rate': 0.9,
                    'avg_execution_time': 1.5
                },
                'model2': {
                    'success_rate': 0.8,
                    'avg_execution_time': 2.0
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
            output_path = f.name
        
        try:
            handler = FileHandler()
            success = handler.export_analysis_report(analysis_data, output_path)
            
            assert success is True
            assert Path(output_path).exists()
            
            # Verify content exists (basic check)
            with open(output_path, 'r') as f:
                content = f.read()
                assert 'Total Rows' in content
                assert 'Model Count' in content
                assert 'Success Rate' in content
            
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    def test_export_analysis_report_excel(self):
        """Test exporting analysis report to Excel."""
        analysis_data = {
            'summary': {
                'total_rows': 10,
                'model_count': 2,
                'overall_success_rate': 0.85
            },
            'model_metrics': {
                'model1': {
                    'success_rate': 0.9,
                    'avg_execution_time': 1.5,
                    'total_requests': 10,
                    'successful_requests': 9,
                    'failed_requests': 1
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            output_path = f.name
        
        try:
            handler = FileHandler()
            success = handler.export_analysis_report(analysis_data, output_path)
            
            assert success is True
            assert Path(output_path).exists()
            
            # Verify Excel file can be read
            df_summary = pd.read_excel(output_path, sheet_name='Summary', engine='openpyxl')
            assert len(df_summary) >= 3  # At least 3 summary metrics
            
            df_metrics = pd.read_excel(output_path, sheet_name='Model Metrics', engine='openpyxl')
            assert len(df_metrics) == 1  # One model
            assert 'Model ID' in df_metrics.columns
            
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)