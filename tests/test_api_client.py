"""
Tests for AI API client.
"""

import asyncio
from unittest.mock import AsyncMock, patch

import pytest
from aioresponses import aioresponses

from ai_model_evaluation.services.api_client import AIAPIClient, APIClientManager
from ai_model_evaluation.models.core import Provider, ModelConfig, APIRequest, APIResponse
from ai_model_evaluation.utils.exceptions import APIError


class TestAIAPIClient:
    """Test AIAPIClient class."""
    
    def create_test_provider(self):
        """Create a test provider."""
        return Provider(
            id="test_provider",
            name="Test Provider",
            base_url="https://api.test.com/v1/",
            api_key="test_key"
        )
    
    def create_test_model(self):
        """Create a test model."""
        return ModelConfig(
            id="test_model",
            provider_id="test_provider",
            model_name="test-model",
            display_name="Test Model",
            temperature=0.7
        )
    
    @pytest.mark.asyncio
    async def test_send_request_success(self):
        """Test successful API request."""
        provider = self.create_test_provider()
        model = self.create_test_model()
        
        # Mock successful response
        mock_response = {
            "choices": [
                {
                    "message": {
                        "content": "Test response content"
                    }
                }
            ]
        }
        
        with aioresponses() as m:
            m.post(
                "https://api.test.com/v1/chat/completions",
                payload=mock_response,
                status=200
            )
            
            async with AIAPIClient(provider) as client:
                response = await client.send_request(model, "Test prompt")
                
                assert response.success is True
                assert response.content == "Test response content"
                assert response.error_message is None
                assert response.execution_time > 0
    
    @pytest.mark.asyncio
    async def test_send_request_http_error(self):
        """Test API request with HTTP error."""
        provider = self.create_test_provider()
        model = self.create_test_model()
        
        with aioresponses() as m:
            m.post(
                "https://api.test.com/v1/chat/completions",
                status=400,
                payload={"error": "Bad request"}
            )
            
            async with AIAPIClient(provider) as client:
                response = await client.send_request(model, "Test prompt")
                
                assert response.success is False
                assert "HTTP 400" in response.error_message
                assert response.content == ""
    
    @pytest.mark.asyncio
    async def test_send_request_retry_on_server_error(self):
        """Test request retry on server error."""
        provider = self.create_test_provider()
        model = self.create_test_model()
        
        with aioresponses() as m:
            # First request fails with 500
            m.post(
                "https://api.test.com/v1/chat/completions",
                status=500,
                payload={"error": "Internal server error"}
            )
            
            # Second request succeeds
            m.post(
                "https://api.test.com/v1/chat/completions",
                payload={
                    "choices": [{"message": {"content": "Success after retry"}}]
                },
                status=200
            )
            
            async with AIAPIClient(provider) as client:
                response = await client.send_request(model, "Test prompt")
                
                assert response.success is True
                assert response.content == "Success after retry"
    
    @pytest.mark.asyncio
    async def test_batch_requests(self):
        """Test batch request processing."""
        provider = self.create_test_provider()
        model = self.create_test_model()
        
        # Create test requests
        requests = [
            APIRequest(model=model, prompt="Prompt 1", request_id="req1"),
            APIRequest(model=model, prompt="Prompt 2", request_id="req2"),
            APIRequest(model=model, prompt="Prompt 3", request_id="req3")
        ]
        
        with aioresponses() as m:
            # Mock responses for all requests
            for i in range(3):
                m.post(
                    "https://api.test.com/v1/chat/completions",
                    payload={
                        "choices": [{"message": {"content": f"Response {i+1}"}}]
                    },
                    status=200
                )
            
            async with AIAPIClient(provider, max_concurrent=2) as client:
                responses = await client.batch_requests(requests)
                
                assert len(responses) == 3
                assert all(r.success for r in responses)
                assert responses[0].request_id == "req1"
                assert responses[1].request_id == "req2"
                assert responses[2].request_id == "req3"
    
    @pytest.mark.asyncio
    async def test_batch_requests_with_failures(self):
        """Test batch requests with some failures."""
        provider = self.create_test_provider()
        model = self.create_test_model()
        
        requests = [
            APIRequest(model=model, prompt="Prompt 1", request_id="req1"),
            APIRequest(model=model, prompt="Prompt 2", request_id="req2")
        ]
        
        with aioresponses() as m:
            # First request succeeds
            m.post(
                "https://api.test.com/v1/chat/completions",
                payload={
                    "choices": [{"message": {"content": "Success"}}]
                },
                status=200
            )
            
            # Second request fails
            m.post(
                "https://api.test.com/v1/chat/completions",
                status=400,
                payload={"error": "Bad request"}
            )
            
            async with AIAPIClient(provider) as client:
                responses = await client.batch_requests(requests)
                
                assert len(responses) == 2
                assert responses[0].success is True
                assert responses[0].content == "Success"
                assert responses[1].success is False
                assert "HTTP 400" in responses[1].error_message
    
    @pytest.mark.asyncio
    async def test_validate_connection_success(self):
        """Test successful connection validation."""
        provider = self.create_test_provider()
        
        with aioresponses() as m:
            m.post(
                "https://api.test.com/v1/chat/completions",
                payload={
                    "choices": [{"message": {"content": "Hello"}}]
                },
                status=200
            )
            
            async with AIAPIClient(provider) as client:
                is_valid = await client.validate_connection()
                assert is_valid is True
    
    @pytest.mark.asyncio
    async def test_validate_connection_failure(self):
        """Test failed connection validation."""
        provider = self.create_test_provider()
        
        with aioresponses() as m:
            m.post(
                "https://api.test.com/v1/chat/completions",
                status=401,
                payload={"error": "Unauthorized"}
            )
            
            async with AIAPIClient(provider) as client:
                is_valid = await client.validate_connection()
                assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_get_connection_info(self):
        """Test getting connection information."""
        provider = self.create_test_provider()
        
        with aioresponses() as m:
            m.post(
                "https://api.test.com/v1/chat/completions",
                payload={
                    "choices": [{"message": {"content": "Hello"}}]
                },
                status=200
            )
            
            async with AIAPIClient(provider) as client:
                info = await client.get_connection_info()
                
                assert info['provider_id'] == "test_provider"
                assert info['provider_name'] == "Test Provider"
                assert info['base_url'] == "https://api.test.com/v1/"
                assert info['is_connected'] is True
                assert info['response_time'] > 0
                assert info['error_message'] is None
    
    @pytest.mark.asyncio
    async def test_send_request_parameter_validation_error(self):
        """Test request with invalid parameters."""
        provider = self.create_test_provider()
        
        # Create model with invalid parameters
        invalid_model = ModelConfig(
            id="invalid_model",
            provider_id="test_provider",
            model_name="",  # Empty model name
            display_name="Invalid Model",
            temperature=3.0,  # Invalid temperature
        )
        
        async with AIAPIClient(provider) as client:
            response = await client.send_request(invalid_model, "")  # Empty prompt
            
            assert response.success is False
            assert "Parameter validation failed" in response.error_message
            assert response.execution_time == 0.0
    
    @pytest.mark.asyncio
    async def test_send_request_with_custom_parameters(self):
        """Test request with custom OpenAI parameters."""
        provider = self.create_test_provider()
        
        # Create model with custom parameters
        model = ModelConfig(
            id="custom_model",
            provider_id="test_provider",
            model_name="gpt-4",
            display_name="Custom Model",
            temperature=0.7,
            other_params={
                "top_p": 0.9,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.2
            }
        )
        
        with aioresponses() as m:
            m.post(
                "https://api.test.com/v1/chat/completions",
                payload={
                    "choices": [{"message": {"content": "Custom response"}}]
                },
                status=200
            )
            
            async with AIAPIClient(provider) as client:
                response = await client.send_request(model, "Test prompt")
                
                assert response.success is True
                assert response.content == "Custom response"
                
                # Verify the request was made with custom parameters
                request_data = m.requests[0][1]
                import json
                request_json = json.loads(request_data.data)
                
                assert request_json["top_p"] == 0.9
                assert request_json["frequency_penalty"] == 0.1
                assert request_json["presence_penalty"] == 0.2
    
    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test using client as context manager."""
        provider = self.create_test_provider()
        
        async with AIAPIClient(provider) as client:
            assert client._session is not None
            assert not client._session.closed
        
        # Session should be closed after exiting context
        assert client._session is None or client._session.closed


class TestAPIClientManager:
    """Test APIClientManager class."""
    
    def create_test_provider(self, provider_id="test_provider"):
        """Create a test provider."""
        return Provider(
            id=provider_id,
            name=f"Test Provider {provider_id}",
            base_url=f"https://api.{provider_id}.com/v1/",
            api_key=f"{provider_id}_key"
        )
    
    def test_add_client(self):
        """Test adding a client."""
        manager = APIClientManager()
        provider = self.create_test_provider()
        
        client = manager.add_client(provider)
        
        assert isinstance(client, AIAPIClient)
        assert client.provider == provider
        assert manager.get_client("test_provider") == client
    
    def test_get_client_nonexistent(self):
        """Test getting nonexistent client."""
        manager = APIClientManager()
        
        client = manager.get_client("nonexistent")
        assert client is None
    
    def test_remove_client(self):
        """Test removing a client."""
        manager = APIClientManager()
        provider = self.create_test_provider()
        
        manager.add_client(provider)
        assert manager.get_client("test_provider") is not None
        
        result = manager.remove_client("test_provider")
        assert result is True
        assert manager.get_client("test_provider") is None
        
        # Try to remove again
        result = manager.remove_client("test_provider")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_all_connections(self):
        """Test validating all connections."""
        manager = APIClientManager()
        
        provider1 = self.create_test_provider("provider1")
        provider2 = self.create_test_provider("provider2")
        
        client1 = manager.add_client(provider1)
        client2 = manager.add_client(provider2)
        
        # Mock validation results
        with patch.object(client1, 'validate_connection', return_value=True), \
             patch.object(client2, 'validate_connection', return_value=False):
            
            results = await manager.validate_all_connections()
            
            assert results["provider1"] is True
            assert results["provider2"] is False
    
    @pytest.mark.asyncio
    async def test_get_all_connection_info(self):
        """Test getting all connection info."""
        manager = APIClientManager()
        
        provider1 = self.create_test_provider("provider1")
        client1 = manager.add_client(provider1)
        
        mock_info = {
            'provider_id': 'provider1',
            'is_connected': True,
            'response_time': 0.5
        }
        
        with patch.object(client1, 'get_connection_info', return_value=mock_info):
            results = await manager.get_all_connection_info()
            
            assert "provider1" in results
            assert results["provider1"] == mock_info
    
    @pytest.mark.asyncio
    async def test_close_all(self):
        """Test closing all clients."""
        manager = APIClientManager()
        
        provider1 = self.create_test_provider("provider1")
        provider2 = self.create_test_provider("provider2")
        
        client1 = manager.add_client(provider1)
        client2 = manager.add_client(provider2)
        
        # Mock close methods
        client1.close = AsyncMock()
        client2.close = AsyncMock()
        
        await manager.close_all()
        
        client1.close.assert_called_once()
        client2.close.assert_called_once()
        assert len(manager._clients) == 0