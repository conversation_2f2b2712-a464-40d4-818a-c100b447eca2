
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>意图识别评估报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .stats { display: flex; gap: 20px; flex-wrap: wrap; }
        .stat-card { background-color: #e9f5ff; padding: 15px; border-radius: 5px; min-width: 200px; }
        .stat-number { font-size: 2em; font-weight: bold; color: #0066cc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>意图识别评估报告</h1>
        <p>生成时间: 2025-08-04 15:42:20</p>
    </div>
    
    <div class="section">
        <h2>评估概览</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">345</div>
                <div>总测试样本</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">200</div>
                <div>qwen-plus成功数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">58.0%</div>
                <div>qwen-plus成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">63.5%</div>
                <div>准确率</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>模型表现</h2>
        <table>
            <tr>
                <th>模型</th>
                <th>成功请求数</th>
                <th>成功率</th>
                <th>状态</th>
            </tr>
            <tr>
                <td>qwen-plus (通义千问Plus)</td>
                <td>200/345</td>
                <td>58.0%</td>
                <td class="success">正常运行</td>
            </tr>
            <tr>
                <td>ernie-4 (文心一言4.0)</td>
                <td>0/345</td>
                <td>0.0%</td>
                <td class="error">API认证失败</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>意图识别结果分布 (qwen-plus)</h2>
        <table>
            <tr>
                <th>意图类别</th>
                <th>识别次数</th>
                <th>占比</th>
            </tr>

            <tr>
                <td>问价格</td>
                <td>39</td>
                <td>19.5%</td>
            </tr>

            <tr>
                <td>看车型</td>
                <td>30</td>
                <td>15.0%</td>
            </tr>

            <tr>
                <td>问优惠</td>
                <td>27</td>
                <td>13.5%</td>
            </tr>

            <tr>
                <td>问地址</td>
                <td>25</td>
                <td>12.5%</td>
            </tr>

            <tr>
                <td>问配置</td>
                <td>23</td>
                <td>11.5%</td>
            </tr>

            <tr>
                <td>要买车</td>
                <td>12</td>
                <td>6.0%</td>
            </tr>

            <tr>
                <td>问政策</td>
                <td>9</td>
                <td>4.5%</td>
            </tr>

            <tr>
                <td>撩妹</td>
                <td>8</td>
                <td>4.0%</td>
            </tr>

            <tr>
                <td>问置换</td>
                <td>7</td>
                <td>3.5%</td>
            </tr>

            <tr>
                <td>问试乘试驾</td>
                <td>6</td>
                <td>3.0%</td>
            </tr>

            <tr>
                <td>金融政策</td>
                <td>4</td>
                <td>2.0%</td>
            </tr>

            <tr>
                <td>问颜色</td>
                <td>4</td>
                <td>2.0%</td>
            </tr>

            <tr>
                <td>问车型</td>
                <td>3</td>
                <td>1.5%</td>
            </tr>

            <tr>
                <td>问库存车</td>
                <td>2</td>
                <td>1.0%</td>
            </tr>

            <tr>
                <td>问联系方式</td>
                <td>1</td>
                <td>0.5%</td>
            </tr>

        </table>
    </div>
    
    <div class="section">
        <h2>问题分析</h2>
        <h3>ernie-4模型问题</h3>
        <p class="error">所有请求都返回HTTP 401错误，错误信息："The model does not exist or you do not have access to it."</p>
        <p>可能原因：</p>
        <ul>
            <li>API密钥无效或过期</li>
            <li>模型名称配置错误</li>
            <li>账户权限不足</li>
        </ul>
        
        <h3>qwen-plus模型表现</h3>
        <p class="success">成功处理了58%的请求，表现良好。</p>
        <p>主要识别的意图类别包括：问价格、看车型、问优惠、问地址等，符合汽车销售场景。</p>
    </div>
    
    <div class="section">
        <h2>建议</h2>
        <ol>
            <li><strong>修复ernie-4配置</strong>：检查API密钥和模型名称配置</li>
            <li><strong>提高成功率</strong>：分析qwen-plus失败的42%请求，可能需要调整并发数或重试机制</li>
            <li><strong>准确率评估</strong>：增加更多带标签的测试数据来评估模型准确性</li>
            <li><strong>性能优化</strong>：考虑调整请求超时和并发设置</li>
        </ol>
    </div>
</body>
</html>
