"""
Integration tests for the AI Model Evaluation System.
"""

import asyncio
import tempfile
import os
import json
from pathlib import Path
from unittest.mock import patch, AsyncMock, MagicMock

import pandas as pd
import pytest
import yaml

from ai_model_evaluation.config.manager import ConfigManager
from ai_model_evaluation.services.evaluation import Evaluation<PERSON><PERSON><PERSON>, EvaluationTaskManager
from ai_model_evaluation.services.file_handler import <PERSON>Handler
from ai_model_evaluation.services.analysis import AnalysisEngine
from ai_model_evaluation.services.report_generator import ReportGenerator
from ai_model_evaluation.services.history_manager import HistoryManager
from ai_model_evaluation.models.core import Provider, ModelConfig, PromptTemplate, TaskStatus


class TestIntegration:
    """Integration tests for the complete evaluation workflow."""
    
    def create_test_config(self):
        """Create a complete test configuration."""
        return {
            'providers': [
                {
                    'id': 'test_provider',
                    'name': 'Test Provider',
                    'base_url': 'https://api.test.com/v1/',
                    'api_key': 'test_key',
                    'is_active': True
                }
            ],
            'models': [
                {
                    'id': 'test_model_1',
                    'provider_id': 'test_provider',
                    'model_name': 'test-model-1',
                    'display_name': 'Test Model 1',
                    'temperature': 0.7,
                    'thinking_enabled': False
                },
                {
                    'id': 'test_model_2',
                    'provider_id': 'test_provider',
                    'model_name': 'test-model-2',
                    'display_name': 'Test Model 2',
                    'temperature': 0.5,
                    'thinking_enabled': True
                }
            ],
            'prompt_templates': [
                {
                    'id': 'test_template',
                    'name': 'Test Template',
                    'template': 'Question: {original_prompt}\n\nContext A: {variable_a}\nContext B: {variable_b}\n\nAnswer:',
                    'variables': ['original_prompt', 'variable_a', 'variable_b'],
                    'description': 'Test template for integration testing'
                }
            ]
        }
    
    def create_test_data(self):
        """Create test evaluation data."""
        return pd.DataFrame({
            'original_prompt': [
                'What is the capital of France?',
                'What is 2 + 2?',
                'Translate "hello" to Spanish'
            ],
            'variable_a': [
                'France',
                '2',
                'hello'
            ],
            'variable_b': [
                'capital city',
                'addition',
                'Spanish'
            ],
            'expected_result': [
                'Paris',
                '4',
                'hola'
            ]
        })
    
    def create_mock_api_responses(self):
        """Create mock API responses for testing."""
        return {
            'test_model_1': [
                {'content': 'Paris', 'success': True, 'execution_time': 1.2},
                {'content': '4', 'success': True, 'execution_time': 0.8},
                {'content': 'hola', 'success': True, 'execution_time': 1.0}
            ],
            'test_model_2': [
                {'content': 'Paris', 'success': True, 'execution_time': 1.5},
                {'content': '', 'success': False, 'execution_time': 0.0, 'error': 'API timeout'},
                {'content': 'hola', 'success': True, 'execution_time': 1.3}
            ]
        }
    
    @pytest.mark.asyncio
    async def test_complete_evaluation_workflow(self):
        """Test the complete evaluation workflow from configuration to results."""
        # Create temporary files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            config_path = temp_path / 'config.yaml'
            data_path = temp_path / 'test_data.csv'
            output_dir = temp_path / 'output'
            output_dir.mkdir()
            
            # Create configuration file
            config_data = self.create_test_config()
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f)
            
            # Create test data file
            test_data = self.create_test_data()
            test_data.to_csv(data_path, index=False)
            
            # Mock API responses
            mock_responses = self.create_mock_api_responses()
            
            # Test configuration loading
            config_manager = ConfigManager(str(config_path))
            config = config_manager.load_config()
            
            assert len(config.providers) == 1
            assert len(config.models) == 2
            assert len(config.prompt_templates) == 1
            
            # Test file handling
            file_handler = FileHandler()
            evaluation_data = file_handler.read_evaluation_data(str(data_path))
            
            assert len(evaluation_data) == 3
            assert evaluation_data[0].original_prompt == 'What is the capital of France?'
            
            # Test template processing
            template = config.get_prompt_template('test_template')
            assert template is not None
            
            from ai_model_evaluation.services.prompt_generator import PromptGenerator
            prompt_generator = PromptGenerator()
            
            generated_prompts = []
            for data_row in evaluation_data:
                prompt = prompt_generator.generate_prompt(template, data_row)
                generated_prompts.append(prompt)
            
            assert len(generated_prompts) == 3
            assert 'What is the capital of France?' in generated_prompts[0]
            assert 'Context A: France' in generated_prompts[0]
            
            # Mock the evaluation engine's API calls
            with patch('ai_model_evaluation.services.api_client.APIClient') as mock_api_client:
                # Set up mock responses
                mock_client_instance = AsyncMock()
                mock_api_client.return_value = mock_client_instance
                
                async def mock_call_api(prompt, model_config):
                    model_id = model_config.id
                    # Find the appropriate response based on prompt content
                    if 'France' in prompt:
                        response_data = mock_responses[model_id][0]
                    elif '2 + 2' in prompt:
                        response_data = mock_responses[model_id][1]
                    else:  # Spanish translation
                        response_data = mock_responses[model_id][2]
                    
                    from ai_model_evaluation.models.core import APIResponse
                    return APIResponse(
                        request_id=f"{model_id}_test",
                        content=response_data['content'],
                        success=response_data['success'],
                        error_message=response_data.get('error'),
                        execution_time=response_data['execution_time']
                    )
                
                mock_client_instance.call_api = mock_call_api
                
                # Test evaluation execution
                task_manager = EvaluationTaskManager()
                
                task = task_manager.create_task(
                    name="Integration Test",
                    prompt_template_id="test_template",
                    selected_models=["test_model_1", "test_model_2"],
                    input_file_path=str(data_path),
                    output_directory=str(output_dir)
                )
                
                assert task.status == TaskStatus.PENDING
                
                # Execute evaluation
                providers = config.providers
                models = [config.get_model(mid) for mid in ["test_model_1", "test_model_2"]]
                
                results = await task_manager.execute_task(
                    task.id, providers, models, template, evaluation_data
                )
                
                # Verify results
                assert len(results) == 3
                
                # Check first result (both models should succeed)
                result1 = results[0]
                assert result1.original_prompt == 'What is the capital of France?'
                assert result1.model_results['test_model_1'] == 'Paris'
                assert result1.model_results['test_model_2'] == 'Paris'
                assert result1.error_info['test_model_1'] is None
                assert result1.error_info['test_model_2'] is None
                
                # Check second result (model2 should fail)
                result2 = results[1]
                assert result2.original_prompt == 'What is 2 + 2?'
                assert result2.model_results['test_model_1'] == '4'
                assert result2.model_results['test_model_2'] == ''
                assert result2.error_info['test_model_1'] is None
                assert result2.error_info['test_model_2'] == 'API timeout'
                
                # Test file output
                output_file = output_dir / 'results.csv'
                file_handler.write_results(results, str(output_file))
                
                assert output_file.exists()
                
                # Verify output file content
                output_df = pd.read_csv(output_file)
                assert len(output_df) == 3
                assert 'test_model_1_result' in output_df.columns
                assert 'test_model_2_result' in output_df.columns
                assert 'test_model_1_execution_time' in output_df.columns
                assert 'test_model_2_error' in output_df.columns
                
                # Test analysis
                analysis_engine = AnalysisEngine()
                analysis_results = analysis_engine.load_results(str(output_file))
                
                assert len(analysis_results) == 3
                
                analysis = analysis_engine.analyze_results(analysis_results)
                assert analysis.total_rows == 3
                assert analysis.model_count == 2
                assert analysis.success_rates['test_model_1'] == 1.0  # 3/3 success
                assert analysis.success_rates['test_model_2'] == 2/3  # 2/3 success
                
                # Test report generation
                report_generator = ReportGenerator()
                
                # Generate text report
                text_report = report_generator.generate_text_report(analysis_results)
                assert 'AI Model Evaluation Report' in text_report
                assert 'test_model_1' in text_report
                assert 'test_model_2' in text_report
                
                # Generate JSON report
                json_report = report_generator.generate_json_report(analysis_results)
                assert 'metadata' in json_report
                assert 'executive_summary' in json_report
                assert json_report['executive_summary']['total_rows'] == 3
                assert json_report['executive_summary']['model_count'] == 2
                
                # Test history management
                history_manager = HistoryManager(str(temp_path / 'history.db'))
                
                # Save task to history
                task.status = TaskStatus.COMPLETED
                task.completed_at = task.started_at  # Simulate completion
                history_manager.save_task(task)
                
                # Save results to history
                history_manager.save_task_results(task.id, analysis_results)
                
                # Log some events
                history_manager.log_task_event(task.id, 'INFO', 'Task started')
                history_manager.log_task_event(task.id, 'WARNING', 'Model timeout', {'model': 'test_model_2'})
                history_manager.log_task_event(task.id, 'INFO', 'Task completed')
                
                # Verify history
                retrieved_task = history_manager.get_task(task.id)
                assert retrieved_task is not None
                assert retrieved_task.name == "Integration Test"
                assert retrieved_task.status == TaskStatus.COMPLETED
                
                retrieved_results = history_manager.get_task_results(task.id)
                assert len(retrieved_results) == 3
                
                logs = history_manager.get_task_logs(task.id)
                assert len(logs) == 3
                assert logs[0]['message'] == 'Task started'
                assert logs[1]['level'] == 'WARNING'
                assert logs[2]['message'] == 'Task completed'
    
    def test_error_handling_workflow(self):
        """Test error handling throughout the workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Test invalid configuration
            config_manager = ConfigManager(str(temp_path / 'nonexistent.yaml'))
            
            with pytest.raises(Exception):  # Should raise configuration error
                config_manager.load_config()
            
            # Test invalid data file
            file_handler = FileHandler()
            
            with pytest.raises(Exception):  # Should raise file processing error
                file_handler.read_evaluation_data(str(temp_path / 'nonexistent.csv'))
            
            # Test analysis with empty results
            analysis_engine = AnalysisEngine()
            analysis = analysis_engine.analyze_results([])
            
            assert analysis.total_rows == 0
            assert analysis.model_count == 0
            assert analysis.success_rates == {}
    
    def test_performance_workflow(self):
        """Test performance with larger datasets."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            data_path = temp_path / 'large_data.csv'
            
            # Create larger test dataset
            large_data = pd.DataFrame({
                'original_prompt': [f'Question {i}' for i in range(100)],
                'variable_a': [f'Context A {i}' for i in range(100)],
                'variable_b': [f'Context B {i}' for i in range(100)],
                'expected_result': [f'Answer {i}' for i in range(100)]
            })
            
            large_data.to_csv(data_path, index=False)
            
            # Test file handling performance
            import time
            
            file_handler = FileHandler()
            
            start_time = time.time()
            evaluation_data = file_handler.read_evaluation_data(str(data_path))
            load_time = time.time() - start_time
            
            assert len(evaluation_data) == 100
            assert load_time < 5.0  # Should load within 5 seconds
            
            # Test analysis performance with mock results
            from ai_model_evaluation.models.core import ResultRow
            
            mock_results = []
            for i in range(100):
                result = ResultRow(
                    original_prompt=f'Question {i}',
                    variable_a=f'Context A {i}',
                    variable_b=f'Context B {i}',
                    expected_result=f'Answer {i}',
                    model_results={'model1': f'Response {i}', 'model2': f'Response {i}'},
                    execution_time={'model1': 1.0, 'model2': 1.2},
                    error_info={'model1': None, 'model2': None}
                )
                mock_results.append(result)
            
            analysis_engine = AnalysisEngine()
            
            start_time = time.time()
            analysis = analysis_engine.analyze_results(mock_results)
            analysis_time = time.time() - start_time
            
            assert analysis.total_rows == 100
            assert analysis_time < 2.0  # Should analyze within 2 seconds
    
    def test_concurrent_evaluation_workflow(self):
        """Test concurrent evaluation handling."""
        # This test would require more complex async mocking
        # For now, we'll test the basic concurrent structure
        
        task_manager = EvaluationTaskManager()
        
        # Create multiple tasks
        task1 = task_manager.create_task(
            name="Concurrent Test 1",
            prompt_template_id="test_template",
            selected_models=["model1"],
            input_file_path="test1.csv",
            output_directory="output1/"
        )
        
        task2 = task_manager.create_task(
            name="Concurrent Test 2",
            prompt_template_id="test_template",
            selected_models=["model2"],
            input_file_path="test2.csv",
            output_directory="output2/"
        )
        
        # Verify tasks are created with unique IDs
        assert task1.id != task2.id
        assert task1.name != task2.name
        
        # Test task management
        all_tasks = task_manager.list_tasks()
        assert len(all_tasks) >= 2
        
        # Test task filtering
        pending_tasks = task_manager.get_tasks_by_status(TaskStatus.PENDING)
        assert len(pending_tasks) >= 2
    
    def test_data_validation_workflow(self):
        """Test data validation throughout the workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Test invalid CSV data
            invalid_data = pd.DataFrame({
                'wrong_column': ['data1', 'data2'],
                'another_wrong': ['data3', 'data4']
            })
            
            invalid_path = temp_path / 'invalid.csv'
            invalid_data.to_csv(invalid_path, index=False)
            
            file_handler = FileHandler()
            
            # Should handle missing required columns gracefully
            evaluation_data = file_handler.read_evaluation_data(str(invalid_path))
            
            # Should create empty or default values for missing columns
            assert len(evaluation_data) == 2
            
            # Test template validation
            from ai_model_evaluation.services.prompt_generator import PromptGenerator
            from ai_model_evaluation.models.core import PromptTemplate
            
            template = PromptTemplate(
                id='invalid_template',
                name='Invalid Template',
                template='This template uses {nonexistent_variable}',
                variables=['nonexistent_variable'],
                description='Template with invalid variables'
            )
            
            prompt_generator = PromptGenerator()
            
            # Should handle missing variables gracefully
            try:
                prompt = prompt_generator.generate_prompt(template, evaluation_data[0])
                # Should either generate with placeholders or raise appropriate error
                assert isinstance(prompt, str)
            except Exception as e:
                # Should be a specific template error, not a generic exception
                assert 'template' in str(e).lower() or 'variable' in str(e).lower()
    
    def test_configuration_validation_workflow(self):
        """Test configuration validation workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Test various invalid configurations
            invalid_configs = [
                # Missing required fields
                {'providers': [{'id': 'test', 'name': 'Test'}]},  # Missing base_url, api_key
                
                # Invalid model reference
                {
                    'providers': [{'id': 'test', 'name': 'Test', 'base_url': 'http://test.com', 'api_key': 'key'}],
                    'models': [{'id': 'model1', 'provider_id': 'nonexistent', 'model_name': 'test'}]
                },
                
                # Invalid template
                {
                    'providers': [{'id': 'test', 'name': 'Test', 'base_url': 'http://test.com', 'api_key': 'key'}],
                    'prompt_templates': [{'id': 'template1', 'name': 'Test', 'template': 'test'}]  # Missing variables
                }
            ]
            
            for i, invalid_config in enumerate(invalid_configs):
                config_path = temp_path / f'invalid_config_{i}.yaml'
                
                with open(config_path, 'w') as f:
                    yaml.dump(invalid_config, f)
                
                config_manager = ConfigManager(str(config_path))
                
                # Should either handle gracefully or raise appropriate errors
                try:
                    config = config_manager.load_config()
                    # If it loads, verify it handles missing data appropriately
                    if hasattr(config, 'providers'):
                        assert isinstance(config.providers, list)
                except Exception as e:
                    # Should be specific configuration errors
                    assert any(keyword in str(e).lower() for keyword in ['config', 'validation', 'missing', 'invalid'])
    
    def test_memory_usage_workflow(self):
        """Test memory usage with various data sizes."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create and process various sized datasets
        sizes = [10, 50, 100]
        
        for size in sizes:
            # Create test data
            test_data = []
            for i in range(size):
                from ai_model_evaluation.models.core import ResultRow
                result = ResultRow(
                    original_prompt=f'Long prompt with lots of text to test memory usage {i}' * 10,
                    variable_a=f'Variable A {i}' * 5,
                    variable_b=f'Variable B {i}' * 5,
                    expected_result=f'Expected result {i}',
                    model_results={
                        'model1': f'Long response from model 1 for item {i}' * 20,
                        'model2': f'Long response from model 2 for item {i}' * 20
                    },
                    execution_time={'model1': 1.0, 'model2': 1.2},
                    error_info={'model1': None, 'model2': None}
                )
                test_data.append(result)
            
            # Process with analysis engine
            analysis_engine = AnalysisEngine()
            analysis = analysis_engine.analyze_results(test_data)
            
            # Check memory usage
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            
            # Memory increase should be reasonable (less than 100MB for test data)
            assert memory_increase < 100, f"Memory usage too high: {memory_increase}MB for {size} items"
            
            # Clean up
            del test_data
            del analysis