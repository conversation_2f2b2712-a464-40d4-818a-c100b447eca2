"""
Tests for evaluation engine.
"""

import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest

from ai_model_evaluation.services.evaluation import Evaluation<PERSON><PERSON><PERSON>, EvaluationTaskManager
from ai_model_evaluation.models.core import (
    Provider, ModelConfig, PromptTemplate, EvaluationRow, EvaluationTask, 
    APIResponse, TaskStatus
)
from ai_model_evaluation.utils.exceptions import EvaluationError, ConfigurationError


class TestEvaluationEngine:
    """Test EvaluationEngine class."""
    
    def create_test_provider(self):
        """Create a test provider."""
        return Provider(
            id="test_provider",
            name="Test Provider",
            base_url="https://api.test.com/v1/",
            api_key="test_key"
        )
    
    def create_test_model(self):
        """Create a test model."""
        return ModelConfig(
            id="test_model",
            provider_id="test_provider",
            model_name="test-model",
            display_name="Test Model"
        )
    
    def create_test_template(self):
        """Create a test template."""
        return PromptTemplate(
            id="test_template",
            name="Test Template",
            template="{original_prompt}\n\nA: {variable_a}\nB: {variable_b}",
            variables=["original_prompt", "variable_a", "variable_b"],
            description="Test template"
        )
    
    def create_test_evaluation_data(self):
        """Create test evaluation data."""
        return [
            EvaluationRow(
                original_prompt="Compare options",
                variable_a="Option A",
                variable_b="Option B",
                expected_result="A is better"
            ),
            EvaluationRow(
                original_prompt="Choose best",
                variable_a="Choice A",
                variable_b="Choice B",
                expected_result="B is better"
            )
        ]
    
    def create_test_task(self):
        """Create a test evaluation task."""
        return EvaluationTask(
            id="test_task",
            name="Test Task",
            prompt_template_id="test_template",
            selected_models=["test_model"],
            input_file_path="test_input.csv",
            output_directory="test_output/",
            status=TaskStatus.PENDING
        )
    
    @pytest.mark.asyncio
    async def test_process_batch_success(self):
        """Test successful batch processing."""
        engine = EvaluationEngine()
        
        provider = self.create_test_provider()
        model = self.create_test_model()
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        # Mock API client
        mock_client = AsyncMock()
        mock_responses = [
            APIResponse(
                request_id="req1",
                content="Response 1",
                success=True,
                execution_time=1.0
            ),
            APIResponse(
                request_id="req2",
                content="Response 2",
                success=True,
                execution_time=1.5
            )
        ]
        mock_client.batch_requests.return_value = mock_responses
        
        # Mock client manager
        engine.client_manager.get_client = MagicMock(return_value=mock_client)
        
        results = await engine.process_batch(evaluation_data, [model], template)
        
        assert len(results) == 2
        
        # Check first result
        result1 = results[0]
        assert result1.original_prompt == "Compare options"
        assert result1.variable_a == "Option A"
        assert result1.variable_b == "Option B"
        assert result1.model_results["test_model"] == "Response 1"
        assert result1.execution_time["test_model"] == 1.0
        assert result1.error_info["test_model"] is None
        
        # Check second result
        result2 = results[1]
        assert result2.original_prompt == "Choose best"
        assert result2.model_results["test_model"] == "Response 2"
        assert result2.execution_time["test_model"] == 1.5
    
    @pytest.mark.asyncio
    async def test_process_batch_with_api_errors(self):
        """Test batch processing with API errors."""
        engine = EvaluationEngine()
        
        provider = self.create_test_provider()
        model = self.create_test_model()
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        # Mock API client with error response
        mock_client = AsyncMock()
        mock_responses = [
            APIResponse(
                request_id="req1",
                content="Response 1",
                success=True,
                execution_time=1.0
            ),
            APIResponse(
                request_id="req2",
                content="",
                success=False,
                error_message="API Error",
                execution_time=0.5
            )
        ]
        mock_client.batch_requests.return_value = mock_responses
        
        engine.client_manager.get_client = MagicMock(return_value=mock_client)
        
        results = await engine.process_batch(evaluation_data, [model], template)
        
        assert len(results) == 2
        
        # First result should be successful
        assert results[0].model_results["test_model"] == "Response 1"
        assert results[0].error_info["test_model"] is None
        
        # Second result should have error
        assert results[1].model_results["test_model"] == ""
        assert results[1].error_info["test_model"] == "API Error"
    
    @pytest.mark.asyncio
    async def test_process_batch_no_client(self):
        """Test batch processing when no client is available."""
        engine = EvaluationEngine()
        
        model = self.create_test_model()
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        # Mock client manager to return None (no client)
        engine.client_manager.get_client = MagicMock(return_value=None)
        
        results = await engine.process_batch(evaluation_data, [model], template)
        
        assert len(results) == 2
        
        # Both results should have errors
        for result in results:
            assert result.model_results["test_model"] == ""
            assert "No API client available" in result.error_info["test_model"]
    
    @pytest.mark.asyncio
    async def test_process_batch_empty_data(self):
        """Test batch processing with empty data."""
        engine = EvaluationEngine()
        
        model = self.create_test_model()
        template = self.create_test_template()
        
        results = await engine.process_batch([], [model], template)
        
        assert len(results) == 0
    
    @pytest.mark.asyncio
    async def test_run_evaluation_success(self):
        """Test successful evaluation run."""
        engine = EvaluationEngine()
        
        task = self.create_test_task()
        providers = [self.create_test_provider()]
        models = [self.create_test_model()]
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        # Mock process_batch
        mock_results = [MagicMock(), MagicMock()]
        with patch.object(engine, 'process_batch', return_value=mock_results):
            with patch.object(engine, '_setup_api_clients'):
                results = await engine.run_evaluation(
                    task, providers, models, template, evaluation_data
                )
        
        assert results == mock_results
        assert task.status == TaskStatus.COMPLETED
        assert task.completed_at is not None
    
    @pytest.mark.asyncio
    async def test_run_evaluation_validation_error(self):
        """Test evaluation run with validation error."""
        engine = EvaluationEngine()
        
        task = self.create_test_task()
        providers = []  # Empty providers should cause validation error
        models = [self.create_test_model()]
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        with pytest.raises(EvaluationError, match="Evaluation failed"):
            await engine.run_evaluation(
                task, providers, models, template, evaluation_data
            )
        
        assert task.status == TaskStatus.FAILED
        assert task.error_message is not None
    
    def test_generate_prompt(self):
        """Test prompt generation."""
        engine = EvaluationEngine()
        template = self.create_test_template()
        
        variables = {
            "original_prompt": "Test prompt",
            "variable_a": "A value",
            "variable_b": "B value"
        }
        
        prompt = engine.generate_prompt(template, variables)
        
        expected = "Test prompt\n\nA: A value\nB: B value"
        assert prompt == expected
    
    def test_track_progress(self):
        """Test progress tracking."""
        engine = EvaluationEngine()
        
        progress_values = []
        def progress_callback(progress):
            progress_values.append(progress)
        
        engine.track_progress(progress_callback)
        
        # Simulate progress updates
        engine._update_progress(0.5)
        engine._update_progress(1.0)
        
        assert progress_values == [0.5, 1.0]
    
    def test_get_current_task_status_no_task(self):
        """Test getting current task status when no task is running."""
        engine = EvaluationEngine()
        
        status = engine.get_current_task_status()
        assert status is None
    
    def test_get_current_task_status_with_task(self):
        """Test getting current task status with running task."""
        engine = EvaluationEngine()
        task = self.create_test_task()
        engine._current_task = task
        
        status = engine.get_current_task_status()
        
        assert status is not None
        assert status['task_id'] == "test_task"
        assert status['task_name'] == "Test Task"
        assert status['status'] == TaskStatus.PENDING.value
    
    @pytest.mark.asyncio
    async def test_validate_models_connectivity(self):
        """Test model connectivity validation."""
        engine = EvaluationEngine()
        
        providers = [self.create_test_provider()]
        models = [self.create_test_model()]
        
        # Mock API client
        mock_client = AsyncMock()
        mock_client.validate_connection.return_value = True
        
        with patch.object(engine, '_setup_api_clients'):
            engine.client_manager.get_client = MagicMock(return_value=mock_client)
            
            connectivity = await engine.validate_models_connectivity(providers, models)
        
        assert connectivity["test_model"] is True
    
    def test_validate_evaluation_inputs_valid(self):
        """Test validation with valid inputs."""
        engine = EvaluationEngine()
        
        providers = [self.create_test_provider()]
        models = [self.create_test_model()]
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        # Should not raise any exception
        engine._validate_evaluation_inputs(providers, models, template, evaluation_data)
    
    def test_validate_evaluation_inputs_no_providers(self):
        """Test validation with no providers."""
        engine = EvaluationEngine()
        
        models = [self.create_test_model()]
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        with pytest.raises(ConfigurationError, match="No providers provided"):
            engine._validate_evaluation_inputs([], models, template, evaluation_data)
    
    def test_validate_evaluation_inputs_no_models(self):
        """Test validation with no models."""
        engine = EvaluationEngine()
        
        providers = [self.create_test_provider()]
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        with pytest.raises(ConfigurationError, match="No models provided"):
            engine._validate_evaluation_inputs(providers, [], template, evaluation_data)
    
    def test_validate_evaluation_inputs_no_data(self):
        """Test validation with no evaluation data."""
        engine = EvaluationEngine()
        
        providers = [self.create_test_provider()]
        models = [self.create_test_model()]
        template = self.create_test_template()
        
        with pytest.raises(ConfigurationError, match="No evaluation data provided"):
            engine._validate_evaluation_inputs(providers, models, template, [])
    
    def test_validate_evaluation_inputs_unknown_provider(self):
        """Test validation with model referencing unknown provider."""
        engine = EvaluationEngine()
        
        providers = [self.create_test_provider()]
        
        # Create model with unknown provider
        model = ModelConfig(
            id="test_model",
            provider_id="unknown_provider",
            model_name="test-model",
            display_name="Test Model"
        )
        
        template = self.create_test_template()
        evaluation_data = self.create_test_evaluation_data()
        
        with pytest.raises(ConfigurationError, match="references unknown provider"):
            engine._validate_evaluation_inputs(providers, [model], template, evaluation_data)


class TestEvaluationTaskManager:
    """Test EvaluationTaskManager class."""
    
    def test_create_task(self):
        """Test creating a task."""
        manager = EvaluationTaskManager()
        
        task = manager.create_task(
            name="Test Task",
            prompt_template_id="template1",
            selected_models=["model1", "model2"],
            input_file_path="input.csv",
            output_directory="output/"
        )
        
        assert task.name == "Test Task"
        assert task.prompt_template_id == "template1"
        assert task.selected_models == ["model1", "model2"]
        assert task.input_file_path == "input.csv"
        assert task.output_directory == "output/"
        assert task.status == TaskStatus.PENDING
        assert task.id in manager.tasks
    
    def test_get_task(self):
        """Test getting a task."""
        manager = EvaluationTaskManager()
        
        task = manager.create_task(
            name="Test Task",
            prompt_template_id="template1",
            selected_models=["model1"],
            input_file_path="input.csv",
            output_directory="output/"
        )
        
        retrieved_task = manager.get_task(task.id)
        assert retrieved_task == task
        
        # Test nonexistent task
        assert manager.get_task("nonexistent") is None
    
    def test_list_tasks(self):
        """Test listing tasks."""
        manager = EvaluationTaskManager()
        
        task1 = manager.create_task("Task 1", "template1", ["model1"], "input1.csv", "output1/")
        task2 = manager.create_task("Task 2", "template2", ["model2"], "input2.csv", "output2/")
        
        tasks = manager.list_tasks()
        assert len(tasks) == 2
        assert task1 in tasks
        assert task2 in tasks
    
    def test_get_tasks_by_status(self):
        """Test getting tasks by status."""
        manager = EvaluationTaskManager()
        
        task1 = manager.create_task("Task 1", "template1", ["model1"], "input1.csv", "output1/")
        task2 = manager.create_task("Task 2", "template2", ["model2"], "input2.csv", "output2/")
        
        # Both should be pending initially
        pending_tasks = manager.get_tasks_by_status(TaskStatus.PENDING)
        assert len(pending_tasks) == 2
        
        # Change one task status
        task1.status = TaskStatus.COMPLETED
        
        pending_tasks = manager.get_tasks_by_status(TaskStatus.PENDING)
        completed_tasks = manager.get_tasks_by_status(TaskStatus.COMPLETED)
        
        assert len(pending_tasks) == 1
        assert len(completed_tasks) == 1
        assert task2 in pending_tasks
        assert task1 in completed_tasks
    
    @pytest.mark.asyncio
    async def test_execute_task_success(self):
        """Test successful task execution."""
        manager = EvaluationTaskManager()
        
        task = manager.create_task("Test Task", "template1", ["model1"], "input.csv", "output/")
        
        providers = [Provider(id="p1", name="P1", base_url="https://api.p1.com/v1/", api_key="key1")]
        models = [ModelConfig(id="model1", provider_id="p1", model_name="m1", display_name="M1")]
        template = PromptTemplate(id="t1", name="T1", template="{original_prompt}", variables=["original_prompt"], description="T1")
        evaluation_data = [EvaluationRow(original_prompt="Test", variable_a="A", variable_b="B")]
        
        # Mock engine run_evaluation
        mock_results = [MagicMock()]
        with patch.object(manager.engine, 'run_evaluation', return_value=mock_results):
            results = await manager.execute_task(task.id, providers, models, template, evaluation_data)
        
        assert results == mock_results
    
    @pytest.mark.asyncio
    async def test_execute_task_not_found(self):
        """Test executing nonexistent task."""
        manager = EvaluationTaskManager()
        
        with pytest.raises(EvaluationError, match="Task not found"):
            await manager.execute_task("nonexistent", [], [], MagicMock(), [])
    
    @pytest.mark.asyncio
    async def test_execute_task_wrong_status(self):
        """Test executing task with wrong status."""
        manager = EvaluationTaskManager()
        
        task = manager.create_task("Test Task", "template1", ["model1"], "input.csv", "output/")
        task.status = TaskStatus.COMPLETED  # Change status
        
        with pytest.raises(EvaluationError, match="not in pending status"):
            await manager.execute_task(task.id, [], [], MagicMock(), [])
    
    def test_cancel_task(self):
        """Test cancelling a task."""
        manager = EvaluationTaskManager()
        
        task = manager.create_task("Test Task", "template1", ["model1"], "input.csv", "output/")
        
        # Should be able to cancel pending task
        result = manager.cancel_task(task.id)
        assert result is True
        assert task.status == TaskStatus.CANCELLED
        
        # Should not be able to cancel already cancelled task
        result = manager.cancel_task(task.id)
        assert result is False
        
        # Test nonexistent task
        result = manager.cancel_task("nonexistent")
        assert result is False
    
    def test_delete_task(self):
        """Test deleting a task."""
        manager = EvaluationTaskManager()
        
        task = manager.create_task("Test Task", "template1", ["model1"], "input.csv", "output/")
        task_id = task.id
        
        # Should be able to delete task
        result = manager.delete_task(task_id)
        assert result is True
        assert task_id not in manager.tasks
        
        # Should not be able to delete nonexistent task
        result = manager.delete_task(task_id)
        assert result is False
    
    def test_get_task_statistics(self):
        """Test getting task statistics."""
        manager = EvaluationTaskManager()
        
        # Create tasks with different statuses
        task1 = manager.create_task("Task 1", "template1", ["model1"], "input1.csv", "output1/")
        task2 = manager.create_task("Task 2", "template2", ["model2"], "input2.csv", "output2/")
        task3 = manager.create_task("Task 3", "template3", ["model3"], "input3.csv", "output3/")
        
        task1.status = TaskStatus.COMPLETED
        task2.status = TaskStatus.FAILED
        # task3 remains PENDING
        
        stats = manager.get_task_statistics()
        
        assert stats['total'] == 3
        assert stats[TaskStatus.PENDING.value] == 1
        assert stats[TaskStatus.COMPLETED.value] == 1
        assert stats[TaskStatus.FAILED.value] == 1
        assert stats[TaskStatus.RUNNING.value] == 0
        assert stats[TaskStatus.CANCELLED.value] == 0