"""
Tests for configuration loader.
"""

import os
import tempfile
from pathlib import Path

import pytest
import yaml

from ai_model_evaluation.config.loader import ConfigLoader
from ai_model_evaluation.models.core import Provider, ModelConfig, PromptTemplate
from ai_model_evaluation.utils.exceptions import ConfigurationError


class TestConfigLoader:
    """Test ConfigLoader class."""
    
    def test_load_valid_config(self):
        """Test loading valid configuration."""
        config_data = {
            'providers': [
                {
                    'id': 'test_provider',
                    'name': 'Test Provider',
                    'base_url': 'https://api.test.com/v1/',
                    'api_key': 'test_key'
                }
            ],
            'models': [
                {
                    'id': 'test_model',
                    'provider_id': 'test_provider',
                    'model_name': 'test-model',
                    'display_name': 'Test Model',
                    'temperature': 0.8,
                    'thinking_enabled': True,
                    'max_tokens': 1000
                }
            ],
            'prompt_templates': [
                {
                    'id': 'test_template',
                    'name': 'Test Template',
                    'template': 'Hello {name}',
                    'variables': ['name'],
                    'description': 'Test template'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            loader = ConfigLoader(config_path)
            config = loader.load()
            
            assert len(config.providers) == 1
            assert len(config.models) == 1
            assert len(config.prompt_templates) == 1
            
            provider = config.providers[0]
            assert provider.id == 'test_provider'
            assert provider.name == 'Test Provider'
            assert provider.base_url == 'https://api.test.com/v1/'
            assert provider.api_key == 'test_key'
            
            model = config.models[0]
            assert model.id == 'test_model'
            assert model.provider_id == 'test_provider'
            assert model.temperature == 0.8
            assert model.thinking_enabled is True
            assert model.max_tokens == 1000
            
            template = config.prompt_templates[0]
            assert template.id == 'test_template'
            assert template.template == 'Hello {name}'
            assert template.variables == ['name']
            
        finally:
            os.unlink(config_path)
    
    def test_load_nonexistent_file(self):
        """Test loading nonexistent file raises error."""
        loader = ConfigLoader('/nonexistent/config.yaml')
        
        with pytest.raises(ConfigurationError, match="Configuration file not found"):
            loader.load()
    
    def test_load_invalid_yaml(self):
        """Test loading invalid YAML raises error."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            config_path = f.name
        
        try:
            loader = ConfigLoader(config_path)
            
            with pytest.raises(ConfigurationError, match="Invalid YAML syntax"):
                loader.load()
        finally:
            os.unlink(config_path)
    
    def test_environment_variable_substitution(self):
        """Test environment variable substitution."""
        config_data = {
            'providers': [
                {
                    'id': 'test_provider',
                    'name': 'Test Provider',
                    'base_url': 'https://api.test.com/v1/',
                    'api_key': '${TEST_API_KEY}'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        # Set environment variable
        os.environ['TEST_API_KEY'] = 'secret_key_from_env'
        
        try:
            loader = ConfigLoader(config_path)
            config = loader.load()
            
            provider = config.providers[0]
            assert provider.api_key == 'secret_key_from_env'
            
        finally:
            os.unlink(config_path)
            if 'TEST_API_KEY' in os.environ:
                del os.environ['TEST_API_KEY']
    
    def test_environment_variable_with_default(self):
        """Test environment variable substitution with default value."""
        config_data = {
            'providers': [
                {
                    'id': 'test_provider',
                    'name': 'Test Provider',
                    'base_url': 'https://api.test.com/v1/',
                    'api_key': '${NONEXISTENT_KEY:default_value}'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            loader = ConfigLoader(config_path)
            config = loader.load()
            
            provider = config.providers[0]
            assert provider.api_key == 'default_value'
            
        finally:
            os.unlink(config_path)
    
    def test_validation_duplicate_provider_ids(self):
        """Test validation catches duplicate provider IDs."""
        config_data = {
            'providers': [
                {
                    'id': 'duplicate_id',
                    'name': 'Provider 1',
                    'base_url': 'https://api1.test.com/v1/',
                    'api_key': 'key1'
                },
                {
                    'id': 'duplicate_id',
                    'name': 'Provider 2',
                    'base_url': 'https://api2.test.com/v1/',
                    'api_key': 'key2'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            loader = ConfigLoader(config_path)
            config = loader.load(validate=False)  # Load without validation first
            
            validation_result = loader.validate()
            assert not validation_result.is_valid
            assert any("Duplicate provider ID" in error for error in validation_result.errors)
            
        finally:
            os.unlink(config_path)
    
    def test_validation_model_references_unknown_provider(self):
        """Test validation catches models referencing unknown providers."""
        config_data = {
            'providers': [
                {
                    'id': 'known_provider',
                    'name': 'Known Provider',
                    'base_url': 'https://api.test.com/v1/',
                    'api_key': 'key'
                }
            ],
            'models': [
                {
                    'id': 'test_model',
                    'provider_id': 'unknown_provider',
                    'model_name': 'test-model',
                    'display_name': 'Test Model'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            loader = ConfigLoader(config_path)
            config = loader.load(validate=False)
            
            validation_result = loader.validate()
            assert not validation_result.is_valid
            assert any("references unknown provider" in error for error in validation_result.errors)
            
        finally:
            os.unlink(config_path)
    
    def test_save_config(self):
        """Test saving configuration to file."""
        from ai_model_evaluation.models.config import ConfigData
        
        # Create test configuration
        provider = Provider(
            id='test_provider',
            name='Test Provider',
            base_url='https://api.test.com/v1/',
            api_key='test_key'
        )
        
        model = ModelConfig(
            id='test_model',
            provider_id='test_provider',
            model_name='test-model',
            display_name='Test Model'
        )
        
        template = PromptTemplate(
            id='test_template',
            name='Test Template',
            template='Hello {name}',
            variables=['name'],
            description='Test template'
        )
        
        config_data = ConfigData(
            providers=[provider],
            models=[model],
            prompt_templates=[template]
        )
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config_path = f.name
        
        try:
            loader = ConfigLoader(config_path)
            loader.save(config_data, backup=False)
            
            # Load the saved file and verify
            saved_config = loader.load()
            
            assert len(saved_config.providers) == 1
            assert len(saved_config.models) == 1
            assert len(saved_config.prompt_templates) == 1
            
            assert saved_config.providers[0].id == 'test_provider'
            assert saved_config.models[0].id == 'test_model'
            assert saved_config.prompt_templates[0].id == 'test_template'
            
        finally:
            if os.path.exists(config_path):
                os.unlink(config_path)