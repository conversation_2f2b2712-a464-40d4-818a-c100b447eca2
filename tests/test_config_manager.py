"""
Tests for configuration manager.
"""

import tempfile
import os
from pathlib import Path

import pytest
import yaml

from ai_model_evaluation.config.manager import ConfigManager
from ai_model_evaluation.models.core import Provider, ModelConfig, PromptTemplate
from ai_model_evaluation.utils.exceptions import ConfigurationError


class TestConfigManager:
    """Test ConfigManager class."""
    
    def create_test_config_file(self):
        """Create a temporary test configuration file."""
        config_data = {
            'providers': [
                {
                    'id': 'test_provider',
                    'name': 'Test Provider',
                    'base_url': 'https://api.test.com/v1/',
                    'api_key': 'test_key'
                }
            ],
            'models': [
                {
                    'id': 'test_model',
                    'provider_id': 'test_provider',
                    'model_name': 'test-model',
                    'display_name': 'Test Model'
                }
            ],
            'prompt_templates': [
                {
                    'id': 'test_template',
                    'name': 'Test Template',
                    'template': 'Hello {name}',
                    'variables': ['name'],
                    'description': 'Test template'
                }
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            return f.name
    
    def test_load_config(self):
        """Test loading configuration."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            config = manager.load_config()
            
            assert len(config.providers) == 1
            assert len(config.models) == 1
            assert len(config.prompt_templates) == 1
            
        finally:
            os.unlink(config_path)
    
    def test_config_data_property_without_loading(self):
        """Test accessing config_data property without loading raises error."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            
            with pytest.raises(ConfigurationError, match="Configuration not loaded"):
                _ = manager.config_data
                
        finally:
            os.unlink(config_path)
    
    def test_add_provider(self):
        """Test adding a provider."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            provider = manager.add_provider(
                name="New Provider",
                base_url="https://api.new.com/v1/",
                api_key="new_key"
            )
            
            assert provider.id == "new_provider"  # Generated from name
            assert provider.name == "New Provider"
            assert len(manager.list_providers()) == 2
            
        finally:
            os.unlink(config_path)
    
    def test_add_provider_with_custom_id(self):
        """Test adding provider with custom ID."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            provider = manager.add_provider(
                name="Custom Provider",
                base_url="https://api.custom.com/v1/",
                api_key="custom_key",
                provider_id="custom_id"
            )
            
            assert provider.id == "custom_id"
            assert provider.name == "Custom Provider"
            
        finally:
            os.unlink(config_path)
    
    def test_get_provider(self):
        """Test getting provider by ID."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            provider = manager.get_provider("test_provider")
            assert provider is not None
            assert provider.id == "test_provider"
            
            # Test nonexistent provider
            assert manager.get_provider("nonexistent") is None
            
        finally:
            os.unlink(config_path)
    
    def test_remove_provider(self):
        """Test removing provider."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            # Should have 1 provider and 1 model initially
            assert len(manager.list_providers()) == 1
            assert len(manager.list_models()) == 1
            
            # Remove provider
            result = manager.remove_provider("test_provider")
            assert result is True
            
            # Should have no providers or models now
            assert len(manager.list_providers()) == 0
            assert len(manager.list_models()) == 0  # Models should be removed too
            
            # Try to remove nonexistent provider
            result = manager.remove_provider("nonexistent")
            assert result is False
            
        finally:
            os.unlink(config_path)
    
    def test_add_model(self):
        """Test adding a model."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            model = ModelConfig(
                id="new_model",
                provider_id="test_provider",
                model_name="new-model",
                display_name="New Model"
            )
            
            added_model = manager.add_model("test_provider", model)
            
            assert added_model.id == "new_model"
            assert added_model.provider_id == "test_provider"
            assert len(manager.list_models()) == 2
            
        finally:
            os.unlink(config_path)
    
    def test_list_models_by_provider(self):
        """Test listing models filtered by provider."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            # Add another provider and model
            manager.add_provider("provider2", "https://api2.com/v1/", "key2")
            model2 = ModelConfig(
                id="model2",
                provider_id="provider2",
                model_name="model-2",
                display_name="Model 2"
            )
            manager.add_model("provider2", model2)
            
            # Test filtering
            provider1_models = manager.list_models("test_provider")
            provider2_models = manager.list_models("provider2")
            all_models = manager.list_models()
            
            assert len(provider1_models) == 1
            assert len(provider2_models) == 1
            assert len(all_models) == 2
            
            assert provider1_models[0].provider_id == "test_provider"
            assert provider2_models[0].provider_id == "provider2"
            
        finally:
            os.unlink(config_path)
    
    def test_get_models_for_evaluation(self):
        """Test getting models for evaluation."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            # Test with existing model
            models = manager.get_models_for_evaluation(["test_model"])
            assert len(models) == 1
            assert models[0].id == "test_model"
            
            # Test with nonexistent model
            with pytest.raises(ConfigurationError, match="Models not found"):
                manager.get_models_for_evaluation(["nonexistent_model"])
            
            # Test with mix of existing and nonexistent
            with pytest.raises(ConfigurationError, match="Models not found"):
                manager.get_models_for_evaluation(["test_model", "nonexistent"])
                
        finally:
            os.unlink(config_path)
    
    def test_get_provider_for_model(self):
        """Test getting provider for a model."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            provider = manager.get_provider_for_model("test_model")
            assert provider is not None
            assert provider.id == "test_provider"
            
            # Test with nonexistent model
            provider = manager.get_provider_for_model("nonexistent")
            assert provider is None
            
        finally:
            os.unlink(config_path)
    
    def test_prompt_template_management(self):
        """Test prompt template management."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            # Test getting existing template
            template = manager.get_prompt_template("test_template")
            assert template is not None
            assert template.id == "test_template"
            
            # Test adding new template
            new_template = PromptTemplate(
                id="new_template",
                name="New Template",
                template="Hi {user}",
                variables=["user"],
                description="New template"
            )
            
            added_template = manager.add_prompt_template(new_template)
            assert added_template.id == "new_template"
            assert len(manager.list_prompt_templates()) == 2
            
            # Test removing template
            result = manager.remove_prompt_template("test_template")
            assert result is True
            assert len(manager.list_prompt_templates()) == 1
            
        finally:
            os.unlink(config_path)
    
    def test_create_default_config(self):
        """Test creating default configuration."""
        manager = ConfigManager()
        config = manager.create_default_config()
        
        assert len(config.providers) == 0  # No providers in default
        assert len(config.models) == 0     # No models in default
        assert len(config.prompt_templates) == 1  # One default template
        
        template = config.prompt_templates[0]
        assert template.id == "default"
        assert "original_prompt" in template.variables
        assert "variable_a" in template.variables
        assert "variable_b" in template.variables
    
    def test_save_config(self):
        """Test saving configuration."""
        config_path = self.create_test_config_file()
        
        try:
            manager = ConfigManager(config_path)
            manager.load_config()
            
            # Add a new provider
            manager.add_provider("new_provider", "https://new.com/v1/", "new_key")
            
            # Save configuration
            manager.save_config(backup=False)
            
            # Create new manager and load to verify
            new_manager = ConfigManager(config_path)
            new_config = new_manager.load_config()
            
            assert len(new_config.providers) == 2
            assert new_config.get_provider("new_provider") is not None
            
        finally:
            os.unlink(config_path)