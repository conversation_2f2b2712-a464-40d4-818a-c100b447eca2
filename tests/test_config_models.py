"""
Tests for configuration data models.
"""

import pytest
from datetime import datetime

from ai_model_evaluation.models.core import Provider, ModelConfig, PromptTemplate
from ai_model_evaluation.models.config import (
    ConfigData,
    ConfigValidationResult,
    ProviderConnectionStatus,
)


class TestConfigData:
    """Test ConfigData model."""
    
    def test_empty_config_creation(self):
        """Test creating empty configuration."""
        config = ConfigData()
        
        assert config.providers == []
        assert config.models == []
        assert config.prompt_templates == []
    
    def test_add_provider(self):
        """Test adding a provider."""
        config = ConfigData()
        provider = Provider(
            id="test_provider",
            name="Test Provider",
            base_url="https://api.test.com/v1/",
            api_key="test_key"
        )
        
        config.add_provider(provider)
        
        assert len(config.providers) == 1
        assert config.providers[0] == provider
        assert config.get_provider("test_provider") == provider
    
    def test_add_duplicate_provider(self):
        """Test adding duplicate provider raises error."""
        config = ConfigData()
        provider1 = Provider(
            id="test_provider",
            name="Test Provider 1",
            base_url="https://api.test1.com/v1/",
            api_key="key1"
        )
        provider2 = Provider(
            id="test_provider",  # Same ID
            name="Test Provider 2",
            base_url="https://api.test2.com/v1/",
            api_key="key2"
        )
        
        config.add_provider(provider1)
        
        with pytest.raises(ValueError, match="Provider with ID test_provider already exists"):
            config.add_provider(provider2)
    
    def test_add_model(self):
        """Test adding a model."""
        config = ConfigData()
        
        # First add a provider
        provider = Provider(
            id="test_provider",
            name="Test Provider",
            base_url="https://api.test.com/v1/",
            api_key="test_key"
        )
        config.add_provider(provider)
        
        # Then add a model
        model = ModelConfig(
            id="test_model",
            provider_id="test_provider",
            model_name="test-model",
            display_name="Test Model"
        )
        config.add_model(model)
        
        assert len(config.models) == 1
        assert config.models[0] == model
        assert config.get_model("test_model") == model
    
    def test_add_model_without_provider(self):
        """Test adding model without provider raises error."""
        config = ConfigData()
        model = ModelConfig(
            id="test_model",
            provider_id="nonexistent_provider",
            model_name="test-model",
            display_name="Test Model"
        )
        
        with pytest.raises(ValueError, match="Provider nonexistent_provider does not exist"):
            config.add_model(model)
    
    def test_get_models_by_provider(self):
        """Test getting models by provider."""
        config = ConfigData()
        
        # Add providers
        provider1 = Provider(id="p1", name="P1", base_url="https://p1.com/v1/", api_key="key1")
        provider2 = Provider(id="p2", name="P2", base_url="https://p2.com/v1/", api_key="key2")
        config.add_provider(provider1)
        config.add_provider(provider2)
        
        # Add models
        model1 = ModelConfig(id="m1", provider_id="p1", model_name="model1", display_name="Model 1")
        model2 = ModelConfig(id="m2", provider_id="p1", model_name="model2", display_name="Model 2")
        model3 = ModelConfig(id="m3", provider_id="p2", model_name="model3", display_name="Model 3")
        
        config.add_model(model1)
        config.add_model(model2)
        config.add_model(model3)
        
        p1_models = config.get_models_by_provider("p1")
        p2_models = config.get_models_by_provider("p2")
        
        assert len(p1_models) == 2
        assert len(p2_models) == 1
        assert model1 in p1_models
        assert model2 in p1_models
        assert model3 in p2_models
    
    def test_remove_provider_removes_models(self):
        """Test removing provider also removes its models."""
        config = ConfigData()
        
        # Add provider and models
        provider = Provider(id="p1", name="P1", base_url="https://p1.com/v1/", api_key="key1")
        config.add_provider(provider)
        
        model1 = ModelConfig(id="m1", provider_id="p1", model_name="model1", display_name="Model 1")
        model2 = ModelConfig(id="m2", provider_id="p1", model_name="model2", display_name="Model 2")
        config.add_model(model1)
        config.add_model(model2)
        
        assert len(config.providers) == 1
        assert len(config.models) == 2
        
        # Remove provider
        result = config.remove_provider("p1")
        
        assert result is True
        assert len(config.providers) == 0
        assert len(config.models) == 0  # Models should be removed too
    
    def test_add_prompt_template(self):
        """Test adding a prompt template."""
        config = ConfigData()
        template = PromptTemplate(
            id="test_template",
            name="Test Template",
            template="Hello {name}",
            variables=["name"],
            description="Test template"
        )
        
        config.add_prompt_template(template)
        
        assert len(config.prompt_templates) == 1
        assert config.prompt_templates[0] == template
        assert config.get_prompt_template("test_template") == template
    
    def test_validation_with_invalid_model_provider(self):
        """Test validation catches models with invalid providers."""
        with pytest.raises(ValueError, match="references unknown provider"):
            ConfigData(
                providers=[
                    Provider(id="p1", name="P1", base_url="https://p1.com/v1/", api_key="key1")
                ],
                models=[
                    ModelConfig(id="m1", provider_id="p2", model_name="model1", display_name="Model 1")  # p2 doesn't exist
                ]
            )


class TestConfigValidationResult:
    """Test ConfigValidationResult model."""
    
    def test_empty_validation_result(self):
        """Test empty validation result."""
        result = ConfigValidationResult(is_valid=True)
        
        assert result.is_valid is True
        assert result.errors == []
        assert result.warnings == []
        assert result.has_errors is False
        assert result.has_warnings is False
    
    def test_add_error(self):
        """Test adding error."""
        result = ConfigValidationResult(is_valid=True)
        result.add_error("Test error")
        
        assert result.is_valid is False
        assert result.has_errors is True
        assert "Test error" in result.errors
    
    def test_add_warning(self):
        """Test adding warning."""
        result = ConfigValidationResult(is_valid=True)
        result.add_warning("Test warning")
        
        assert result.is_valid is True  # Warnings don't affect validity
        assert result.has_warnings is True
        assert "Test warning" in result.warnings


class TestProviderConnectionStatus:
    """Test ProviderConnectionStatus model."""
    
    def test_connected_status(self):
        """Test connected provider status."""
        status = ProviderConnectionStatus(
            provider_id="test_provider",
            is_connected=True,
            response_time=0.5
        )
        
        assert status.provider_id == "test_provider"
        assert status.is_connected is True
        assert status.response_time == 0.5
        assert status.status_text == "Connected (0.50s)"
    
    def test_failed_status(self):
        """Test failed provider status."""
        status = ProviderConnectionStatus(
            provider_id="test_provider",
            is_connected=False,
            error_message="Connection timeout"
        )
        
        assert status.provider_id == "test_provider"
        assert status.is_connected is False
        assert status.status_text == "Failed: Connection timeout"
    
    def test_failed_status_no_error_message(self):
        """Test failed status without error message."""
        status = ProviderConnectionStatus(
            provider_id="test_provider",
            is_connected=False
        )
        
        assert status.status_text == "Failed"