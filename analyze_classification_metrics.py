#!/usr/bin/env python3
"""
分类指标分析脚本
计算模型的准确率、精确率、召回率、F1分数等指标
"""

import pandas as pd
import json
from collections import defaultdict
from pathlib import Path
import sys

def calculate_classification_metrics(y_true, y_pred, model_name="Model"):
    """
    计算分类指标
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        model_name: 模型名称
        
    Returns:
        分类指标字典
    """
    # 获取所有类别
    all_classes = sorted(set(y_true) | set(y_pred))
    
    # 计算混淆矩阵
    confusion_matrix = {}
    for true_class in all_classes:
        confusion_matrix[true_class] = {}
        for pred_class in all_classes:
            count = ((y_true == true_class) & (y_pred == pred_class)).sum()
            confusion_matrix[true_class][pred_class] = int(count)
    
    # 计算整体准确率
    accuracy = (y_true == y_pred).mean()
    
    # 计算每个类别的指标
    per_class_metrics = {}
    class_precisions = []
    class_recalls = []
    class_f1s = []
    
    for class_name in all_classes:
        # True Positive, False Positive, False Negative
        tp = ((y_true == class_name) & (y_pred == class_name)).sum()
        fp = ((y_true != class_name) & (y_pred == class_name)).sum()
        fn = ((y_true == class_name) & (y_pred != class_name)).sum()
        
        # 精确率 = TP / (TP + FP)
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        
        # 召回率 = TP / (TP + FN)
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        
        # F1分数 = 2 * (precision * recall) / (precision + recall)
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        
        per_class_metrics[class_name] = {
            "precision": float(precision),
            "recall": float(recall),
            "f1_score": float(f1),
            "support": int((y_true == class_name).sum()),
            "tp": int(tp),
            "fp": int(fp),
            "fn": int(fn)
        }
        
        # 收集用于计算宏平均的指标
        if (tp + fp) > 0:  # 只有当该类别有预测时才计入
            class_precisions.append(precision)
        if (tp + fn) > 0:  # 只有当该类别有真实样本时才计入
            class_recalls.append(recall)
        if f1 > 0:
            class_f1s.append(f1)
    
    # 计算宏平均
    macro_precision = sum(class_precisions) / len(class_precisions) if class_precisions else 0.0
    macro_recall = sum(class_recalls) / len(class_recalls) if class_recalls else 0.0
    macro_f1 = sum(class_f1s) / len(class_f1s) if class_f1s else 0.0
    
    # 计算加权平均（按支持度加权）
    total_support = sum(metrics["support"] for metrics in per_class_metrics.values())
    if total_support > 0:
        weighted_precision = sum(
            metrics["precision"] * metrics["support"] 
            for metrics in per_class_metrics.values()
        ) / total_support
        weighted_recall = sum(
            metrics["recall"] * metrics["support"] 
            for metrics in per_class_metrics.values()
        ) / total_support
        weighted_f1 = sum(
            metrics["f1_score"] * metrics["support"] 
            for metrics in per_class_metrics.values()
        ) / total_support
    else:
        weighted_precision = weighted_recall = weighted_f1 = 0.0
    
    return {
        "model_name": model_name,
        "accuracy": float(accuracy),
        "macro_avg": {
            "precision": float(macro_precision),
            "recall": float(macro_recall),
            "f1_score": float(macro_f1)
        },
        "weighted_avg": {
            "precision": float(weighted_precision),
            "recall": float(weighted_recall),
            "f1_score": float(weighted_f1)
        },
        "per_class_metrics": per_class_metrics,
        "confusion_matrix": confusion_matrix,
        "total_samples": int(len(y_true))
    }

def analyze_result_file(file_path):
    """分析结果文件"""
    df = pd.read_csv(file_path)
    
    print(f"📊 分析文件: {file_path}")
    print(f"📋 总样本数: {len(df)}")
    
    # 找到所有模型列
    model_columns = [col for col in df.columns if col.endswith('_result') and col != 'expected_result']
    print(f"🤖 发现模型: {[col.replace('_result', '') for col in model_columns]}")
    
    if 'expected_result' not in df.columns:
        print("⚠️ 没有找到 expected_result 列，无法计算分类指标")
        return
    
    results = {}
    
    for model_col in model_columns:
        model_name = model_col.replace('_result', '')
        print(f"\n{'='*50}")
        print(f"🔍 分析模型: {model_name}")
        print(f"{'='*50}")
        
        # 过滤有效数据
        valid_mask = (
            df['expected_result'].notna() & 
            (df['expected_result'] != 'none') &
            (df['expected_result'] != '') &
            df[model_col].notna() & 
            (df[model_col] != 'None') & 
            (df[model_col] != '')
        )
        
        valid_count = valid_mask.sum()
        total_predictions = (df[model_col].notna() & (df[model_col] != 'None') & (df[model_col] != '')).sum()
        
        print(f"📈 预测成功: {total_predictions}/{len(df)} ({total_predictions/len(df):.1%})")
        print(f"📊 有效对比样本: {valid_count}")
        
        if valid_count == 0:
            print("❌ 没有有效的对比样本，无法计算分类指标")
            results[model_name] = {
                "model_name": model_name,
                "accuracy": 0.0,
                "valid_samples": 0,
                "note": "No valid samples for comparison"
            }
            continue
        
        # 计算分类指标
        y_true = df[valid_mask]['expected_result'].astype(str)
        y_pred = df[valid_mask][model_col].astype(str)
        
        metrics = calculate_classification_metrics(y_true, y_pred, model_name)
        results[model_name] = metrics
        
        # 显示结果
        print(f"\n📊 整体指标:")
        print(f"   准确率 (Accuracy): {metrics['accuracy']:.3f}")
        
        print(f"\n📈 宏平均 (Macro Average):")
        print(f"   精确率 (Precision): {metrics['macro_avg']['precision']:.3f}")
        print(f"   召回率 (Recall): {metrics['macro_avg']['recall']:.3f}")
        print(f"   F1分数 (F1-Score): {metrics['macro_avg']['f1_score']:.3f}")
        
        print(f"\n⚖️ 加权平均 (Weighted Average):")
        print(f"   精确率 (Precision): {metrics['weighted_avg']['precision']:.3f}")
        print(f"   召回率 (Recall): {metrics['weighted_avg']['recall']:.3f}")
        print(f"   F1分数 (F1-Score): {metrics['weighted_avg']['f1_score']:.3f}")
        
        print(f"\n🎯 各类别详细指标 (前10个):")
        print(f"{'类别':<15} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'支持度':<6}")
        print("-" * 50)
        
        # 按支持度排序显示
        sorted_classes = sorted(
            metrics['per_class_metrics'].items(), 
            key=lambda x: x[1]['support'], 
            reverse=True
        )
        
        for i, (class_name, class_metrics) in enumerate(sorted_classes[:10]):
            print(f"{class_name:<15} {class_metrics['precision']:<8.3f} {class_metrics['recall']:<8.3f} {class_metrics['f1_score']:<8.3f} {class_metrics['support']:<6}")
    
    # 保存结果
    output_file = Path(file_path).parent / "classification_metrics_analysis.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    return results

def main():
    """主函数"""
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认分析最新的多模型结果
        file_path = "evaluation_results/multi_model_test/multi_model_test_results.csv"
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        print("💡 用法: python analyze_classification_metrics.py <结果文件路径>")
        return
    
    try:
        analyze_result_file(file_path)
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
